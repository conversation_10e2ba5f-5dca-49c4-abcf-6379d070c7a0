const express = require('express');
const path = require('path');
const { spawn } = require('child_process');
const { createClient } = require('@supabase/supabase-js');
const app = express();
const PORT = process.env.PORT || 3001;

// Import citation crawler from local directory
const { runManualCrawl } = require('./citation_crawler');

// Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://rbvgtaqimzpsarvoxubn.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJidmd0YXFpbXpwc2Fydm94dWJuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjAxMDI0MSwiZXhwIjoyMDYxNTg2MjQxfQ.Y1Xcvq50peqVvgRhVVtHP7eEvpGBn-A2EG65H--8XX8';
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Parse JSON bodies
app.use(express.json());

// Store running tasks
const runningTasks = new Map();

// Helper function to run Python scripts
function runPythonScript(scriptName, args = []) {
  return new Promise((resolve, reject) => {
    const python = spawn('python', [scriptName, ...args]);
    let stdout = '';
    let stderr = '';

    python.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    python.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    python.on('close', (code) => {
      if (code === 0) {
        resolve(stdout);
      } else {
        reject(new Error(`Python script failed with code ${code}: ${stderr}`));
      }
    });
  });
}

// Original internal citation crawling endpoint
app.post('/api/crawl-citations', async (req, res) => {
  try {
    console.log('Starting internal citation crawl from API...');
    await runManualCrawl();
    res.status(200).json({
      message: 'Internal citation crawling completed successfully!',
      type: 'internal',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Internal citation crawl error:', error);
    res.status(500).json({
      error: error.message || 'Internal citation crawling failed',
      timestamp: new Date().toISOString()
    });
  }
});

// Enhanced web citation crawling endpoint
app.post('/api/crawl-web-citations', async (req, res) => {
  try {
    const { publicationId } = req.body;
    console.log('🌐 [server.js] Starting REAL web crawl for:', publicationId || 'all publications');

    // Try to load the real web crawler
    let crawler;
    try {
      const { RealWebCrawler } = require('./real_web_crawler');
      crawler = new RealWebCrawler();
      console.log('✅ [server.js] Real web crawler loaded successfully');
    } catch (crawlerError) {
      console.log('❌ [server.js] Real web crawler failed to load:', crawlerError.message);
      crawler = null;
    }

    if (!crawler) {
      return res.status(500).json({
        error: 'Real web crawler not available',
        timestamp: new Date().toISOString()
      });
    }

    if (publicationId) {
      // Get specific publication
      const { data: publication, error } = await supabase
        .from('publications')
        .select('*')
        .eq('id', publicationId)
        .single();

      if (error) {
        throw new Error(`Publication not found: ${error.message}`);
      }

      console.log(`[server.js] 🎯 Processing publication: ${publication.title}`);
      const result = await crawler.crawlPublication(publication);

      res.json({
        message: `Web crawl completed for "${publication.title}". Found ${result.citationsFound} citations, saved ${result.citationsSaved} new ones.`,
        type: 'web_external',
        publicationId,
        citationsFound: result.citationsFound,
        citationsSaved: result.citationsSaved,
        timestamp: new Date().toISOString()
      });
    } else {
      // Crawl all publications
      console.log('[server.js] 🌐 Processing all publications...');
      const crawlResult = await crawler.crawlAllPublications();
      res.json({
        message: `Web crawl completed for ${crawlResult.publicationsProcessed} publications. Found ${crawlResult.totalCitationsFound} citations, saved ${crawlResult.totalSaved} new ones.`,
        type: 'web_external',
        publicationsProcessed: crawlResult.publicationsProcessed,
        totalCitationsFound: crawlResult.totalCitationsFound,
        totalSaved: crawlResult.totalSaved,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('[server.js] ❌ Web crawl error:', error);
    res.status(500).json({
      error: error.message || 'Web citation crawling failed',
      timestamp: new Date().toISOString()
    });
  }
});

// Enhanced web citation crawling with real-time progress using Server-Sent Events
app.post('/api/crawl-web-citations-with-progress', async (req, res) => {
  try {
    const { publicationId } = req.body;
    console.log('🌐 [server.js] Starting REAL web crawl with progress for:', publicationId || 'all publications');

    // Set up Server-Sent Events
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Send initial connection confirmation
    res.write(`data: ${JSON.stringify({
      type: 'connected',
      message: 'Real-time progress connection established',
      timestamp: new Date().toISOString()
    })}\n\n`);

    // Keep connection alive with heartbeat
    const heartbeat = setInterval(() => {
      res.write(`data: ${JSON.stringify({
        type: 'heartbeat',
        timestamp: new Date().toISOString()
      })}\n\n`);
    }, 5000); // Every 5 seconds

    // Clean up heartbeat on connection close
    req.on('close', () => {
      clearInterval(heartbeat);
      console.log('📡 [SSE] Client disconnected');
    });

    const sendProgress = (data) => {
      const progressData = {
        timestamp: new Date().toISOString(),
        ...data
      };
      console.log('📡 [SSE] Sending progress:', progressData);
      res.write(`data: ${JSON.stringify(progressData)}\n\n`);

      // Force flush the response to ensure immediate delivery
      if (res.flush) {
        res.flush();
      }
    };

    // Try to load the real web crawler
    let crawler;
    try {
      const { RealWebCrawler } = require('./real_web_crawler');
      crawler = new RealWebCrawler();
      console.log('✅ [server.js] Real web crawler loaded successfully');
      sendProgress({
        type: 'info',
        message: 'Real web crawler loaded successfully',
        percentage: 5
      });
    } catch (crawlerError) {
      console.log('❌ [server.js] Real web crawler failed to load:', crawlerError.message);
      sendProgress({
        type: 'error',
        message: 'Real web crawler not available: ' + crawlerError.message
      });
      res.end();
      return;
    }

    if (publicationId) {
      // Get specific publication
      const { data: publication, error } = await supabase
        .from('publications')
        .select('*')
        .eq('id', publicationId)
        .single();

      if (error) {
        sendProgress({
          type: 'error',
          message: `Publication not found: ${error.message}`
        });
        res.end();
        return;
      }

      sendProgress({
        type: 'info',
        message: `Starting crawl for: ${publication.title}`,
        current: 0,
        total: 1,
        percentage: 10,
        currentPublication: publication.title,
        currentSite: 'Initializing'
      });

      // Use the existing crawlPublication method but with progress callback
      const result = await crawler.crawlPublication(publication, sendProgress);

      sendProgress({
        type: 'success',
        message: `Completed! Found ${result.citationsFound} citations, saved ${result.citationsSaved} new ones.`,
        current: 1,
        total: 1,
        percentage: 100,
        currentSite: 'Completed'
      });

    } else {
      // Get all publications first
      const { data: publications, error } = await supabase
        .from('publications')
        .select('id, title, authors, doi, journal, year');

      if (error) {
        sendProgress({
          type: 'error',
          message: `Database error: ${error.message}`
        });
        res.end();
        return;
      }

      sendProgress({
        type: 'info',
        message: `Found ${publications.length} publications to crawl`,
        current: 0,
        total: publications.length,
        percentage: 10
      });

      let totalCitationsFound = 0;
      let totalSaved = 0;

      for (let i = 0; i < publications.length; i++) {
        const publication = publications[i];
        const percentage = Math.round(((i + 1) / publications.length) * 90) + 10; // 10-100%

        sendProgress({
          type: 'info',
          message: `Processing publication ${i + 1} of ${publications.length}: ${publication.title}`,
          current: i + 1,
          total: publications.length,
          percentage: percentage,
          currentPublication: publication.title,
          currentSite: 'Starting'
        });

        const result = await crawler.crawlPublication(publication, sendProgress);
        totalCitationsFound += result.citationsFound;
        totalSaved += result.citationsSaved;

        sendProgress({
          type: 'info',
          message: `Completed ${i + 1}/${publications.length}. Total found: ${totalCitationsFound}`,
          current: i + 1,
          total: publications.length,
          percentage: percentage,
          totalCitationsFound: totalCitationsFound
        });

        // Delay between publications
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      sendProgress({
        type: 'success',
        message: `All done! Processed ${publications.length} publications, found ${totalCitationsFound} citations, saved ${totalSaved} new ones.`,
        percentage: 100,
        currentSite: 'All Completed'
      });
    }

    // Clear heartbeat and close connection
    clearInterval(heartbeat);
    res.end();

  } catch (error) {
    console.error('[server.js] ❌ Web crawl with progress error:', error);
    res.write(`data: ${JSON.stringify({
      type: 'error',
      message: error.message || 'Web citation crawling failed'
    })}\n\n`);

    // Clear heartbeat and close connection
    clearInterval(heartbeat);
    res.end();
  }
});

// Get task status endpoint
app.get('/api/crawl-status/:taskId', (req, res) => {
  const { taskId } = req.params;
  const task = runningTasks.get(taskId);

  if (!task) {
    return res.status(404).json({
      error: 'Task not found',
      taskId: taskId
    });
  }

  res.json({
    taskId: taskId,
    status: 'running',
    startTime: task.startTime,
    type: task.type,
    publicationId: task.publicationId,
    pid: task.pid
  });
});

// Get all publications
app.get('/api/publications', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('publications')
      .select('*')
      .order('citation_count', { ascending: false });

    if (error) {
      throw error;
    }

    res.json(data);

  } catch (error) {
    console.error('Error fetching publications:', error);
    res.status(500).json({
      error: 'Failed to fetch publications',
      details: error.message
    });
  }
});

// Get external citations for a publication
app.get('/api/publications/:publicationId/citations', async (req, res) => {
  try {
    const { publicationId } = req.params;
    const { verified, active = true } = req.query;

    let query = supabase
      .from('external_citations')
      .select('*')
      .eq('publication_id', publicationId)
      .eq('is_active', active === 'true')
      .order('confidence_score', { ascending: false });

    if (verified !== undefined) {
      query = query.eq('is_verified', verified === 'true');
    }

    const { data, error } = await query;

    if (error) {
      throw error;
    }

    res.json({
      publicationId: publicationId,
      citations: data,
      count: data.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching citations:', error);
    res.status(500).json({
      error: 'Failed to fetch citations',
      details: error.message
    });
  }
});

// Verify/reject a citation
app.patch('/api/citations/:citationId/verify', async (req, res) => {
  try {
    const { citationId } = req.params;
    const { verified } = req.body;

    if (typeof verified !== 'boolean') {
      return res.status(400).json({
        error: 'verified field must be a boolean'
      });
    }

    const { data, error } = await supabase
      .from('external_citations')
      .update({
        is_verified: verified,
        last_verified: new Date().toISOString()
      })
      .eq('id', citationId)
      .select();

    if (error) {
      throw error;
    }

    if (data.length === 0) {
      return res.status(404).json({
        error: 'Citation not found'
      });
    }

    res.json({
      message: `Citation ${verified ? 'verified' : 'rejected'} successfully`,
      citation: data[0],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error verifying citation:', error);
    res.status(500).json({
      error: 'Failed to verify citation',
      details: error.message
    });
  }
});

// Test real-time progress endpoint
app.post('/api/test-progress', async (req, res) => {
  try {
    // Set up Server-Sent Events
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    console.log('🧪 [TEST] Starting progress test...');

    // Send test progress updates
    for (let i = 1; i <= 10; i++) {
      const progressData = {
        type: 'info',
        message: `Test progress update ${i}/10`,
        current: i,
        total: 10,
        percentage: i * 10,
        currentPublication: 'Test Publication',
        currentSite: 'Test Site',
        citationsFound: i * 2,
        totalCitationsFound: i * 2,
        timestamp: new Date().toISOString()
      };

      console.log(`🧪 [TEST] Sending progress ${i}/10:`, progressData);
      res.write(`data: ${JSON.stringify(progressData)}\n\n`);

      if (res.flush) {
        res.flush();
      }

      // Wait 1 second between updates
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Send completion
    res.write(`data: ${JSON.stringify({
      type: 'success',
      message: 'Test completed successfully!',
      current: 10,
      total: 10,
      percentage: 100,
      timestamp: new Date().toISOString()
    })}\n\n`);

    res.end();
    console.log('🧪 [TEST] Progress test completed');

  } catch (error) {
    console.error('🧪 [TEST] Progress test failed:', error);
    res.status(500).json({ error: error.message });
  }
});

// Test citation save endpoint
app.post('/api/test-citation-save', async (req, res) => {
  try {
    console.log('🧪 [TEST] Testing citation save...');

    // Get first publication
    const { data: publications, error: pubError } = await supabase
      .from('publications')
      .select('id, title')
      .limit(1);

    if (pubError || !publications || publications.length === 0) {
      throw new Error('No publications found for testing');
    }

    const testPub = publications[0];
    console.log('🧪 [TEST] Using publication:', testPub.title);

    // Try to save a test citation
    const testCitation = {
      publication_id: testPub.id,
      source_url: `https://test-citation-${Date.now()}.com`,
      source_title: 'Test Citation',
      source_domain: 'test.com',
      citation_context: 'Found in Google Scholar profile with 5 citations',
      citation_type: 'reference',
      confidence_score: 0.9,
      found_date: new Date().toISOString(),
      is_verified: true,
      is_active: true,
      metadata: { citationCount: 5, source: 'Test' }
    };

    console.log('🧪 [TEST] Inserting test citation:', testCitation);

    const { data, error } = await supabase
      .from('external_citations')
      .insert(testCitation);

    console.log('🧪 [TEST] Insert result:', { data, error });

    if (error) {
      throw new Error(`Citation save failed: ${error.message}`);
    }

    res.json({
      success: true,
      message: 'Test citation saved successfully',
      publicationId: testPub.id,
      publicationTitle: testPub.title,
      testCitation
    });

  } catch (error) {
    console.error('🧪 [TEST] Citation save test failed:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Recalculate citation counts for all publications
app.post('/api/recalculate-citations', async (req, res) => {
  try {
    console.log('🔄 [server.js] Recalculating citation counts...');

    // Get all publications
    const { data: publications, error: pubError } = await supabase
      .from('publications')
      .select('id, title');

    if (pubError) {
      throw new Error(`Error fetching publications: ${pubError.message}`);
    }

    let updatedCount = 0;
    const results = [];

    for (const publication of publications) {
      // Get all external citations for this publication
      const { data: citations, error: citError } = await supabase
        .from('external_citations')
        .select('id, citation_context, metadata')
        .eq('publication_id', publication.id)
        .eq('is_active', true);

      if (citError) {
        console.error(`❌ Error getting citations for ${publication.title}: ${citError.message}`);
        continue;
      }

      let totalCitationCount = 0;

      if (citations && citations.length > 0) {
        for (const citation of citations) {
          // Extract citation number from citation_context
          let citationNumber = 1; // default

          // First try metadata
          if (citation.metadata && citation.metadata.citationCount) {
            citationNumber = parseInt(citation.metadata.citationCount) || 1;
          } else if (citation.citation_context) {
            // Extract from text like "Found in Google Scholar profile with 7 citations"
            const patterns = [
              /with (\d+) citations/i,
              /(\d+) citations/i,
              /cited (\d+) times/i,
              /(\d+) times cited/i
            ];

            for (const pattern of patterns) {
              const match = citation.citation_context.match(pattern);
              if (match && match[1]) {
                citationNumber = parseInt(match[1]);
                break;
              }
            }
          }

          totalCitationCount += citationNumber;
        }
      }

      // Update the publication
      const { error: updateError } = await supabase
        .from('publications')
        .update({
          external_citation_count: totalCitationCount
        })
        .eq('id', publication.id);

      if (!updateError) {
        updatedCount++;
        results.push({
          id: publication.id,
          title: publication.title,
          citationRecords: citations.length,
          totalCitations: totalCitationCount
        });
        console.log(`✅ Updated "${publication.title}": ${citations.length} records = ${totalCitationCount} total citations`);
      }
    }

    res.json({
      message: `Successfully recalculated citation counts for ${updatedCount} publications`,
      updatedCount,
      totalPublications: publications.length,
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[server.js] ❌ Recalculation error:', error);
    res.status(500).json({
      error: error.message || 'Citation recalculation failed',
      timestamp: new Date().toISOString()
    });
  }
});

// Get crawling statistics
app.get('/api/crawl-stats', async (req, res) => {
  try {
    const { limit = 10, crawl_type } = req.query;

    let query = supabase
      .from('crawling_logs')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(parseInt(limit));

    if (crawl_type) {
      query = query.eq('crawl_type', crawl_type);
    }

    const { data, error } = await query;

    if (error) {
      throw error;
    }

    res.json({
      stats: data,
      count: data.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching crawl stats:', error);
    res.status(500).json({
      error: 'Failed to fetch crawl statistics',
      details: error.message
    });
  }
});

// Get API usage statistics
app.get('/api/api-usage', async (req, res) => {
  try {
    const { provider, days = 7 } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    let query = supabase
      .from('search_api_usage')
      .select('*')
      .gte('timestamp', startDate.toISOString())
      .order('timestamp', { ascending: false });

    if (provider) {
      query = query.eq('api_provider', provider);
    }

    const { data, error } = await query;

    if (error) {
      throw error;
    }

    // Calculate summary statistics
    const summary = {
      total_calls: data.length,
      successful_calls: data.filter(call => call.success).length,
      failed_calls: data.filter(call => !call.success).length,
      total_results: data.reduce((sum, call) => sum + (call.results_count || 0), 0),
      providers: {}
    };

    // Group by provider
    data.forEach(call => {
      if (!summary.providers[call.api_provider]) {
        summary.providers[call.api_provider] = {
          calls: 0,
          successful: 0,
          failed: 0,
          results: 0
        };
      }
      summary.providers[call.api_provider].calls++;
      if (call.success) {
        summary.providers[call.api_provider].successful++;
      } else {
        summary.providers[call.api_provider].failed++;
      }
      summary.providers[call.api_provider].results += call.results_count || 0;
    });

    res.json({
      summary: summary,
      usage_data: data,
      period_days: parseInt(days),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching API usage:', error);
    res.status(500).json({
      error: 'Failed to fetch API usage statistics',
      details: error.message
    });
  }
});

// Cleanup old citations
app.post('/api/cleanup-citations', async (req, res) => {
  try {
    const { days = 30 } = req.body;

    const output = await runPythonScript('../web_citation_crawler.py', ['--cleanup']);

    res.json({
      message: `Citation cleanup completed for citations older than ${days} days`,
      output: output,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error cleaning up citations:', error);
    res.status(500).json({
      error: 'Failed to cleanup citations',
      details: error.message
    });
  }
});

// Serve static files from the React app
app.use(express.static(path.join(__dirname, 'build')));

// Handle React routing, return all requests to React app
app.get('*', function(req, res) {
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
  console.log(`Citation crawler APIs available:`);
  console.log(`  Internal crawl: POST http://localhost:${PORT}/api/crawl-citations`);
  console.log(`  Web crawl: POST http://localhost:${PORT}/api/crawl-web-citations`);
  console.log(`  View citations: GET http://localhost:${PORT}/api/publications/:id/citations`);
  console.log(`  Crawl stats: GET http://localhost:${PORT}/api/crawl-stats`);
});