"use strict";var e=require("@react-pdf-viewer/core");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var a=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,a.get?a:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var n=t(require("react")),a=function(t){var a=t.files,r=n.useRef(),c=n.useContext(e.LocalizationContext).l10n,i=n.useContext(e.ThemeContext).direction===e.TextDirection.RightToLeft,o=n.useRef([]),u=c&&c.attachment?c.attachment.clickToDownload:"Click to download",l=function(e){var t=r.current,n=[].slice.call(t.getElementsByClassName("rpv-attachment__item"));if(0!==n.length){n.forEach((function(e){return e.setAttribute("tabindex","-1")}));var a=document.activeElement,c=n[Math.min(n.length-1,Math.max(0,e(n,a)))];c.setAttribute("tabindex","0"),c.focus()}};return e.useIsomorphicLayoutEffect((function(){var e=r.current;if(e){var t=[].slice.call(e.getElementsByClassName("rpv-attachment__item"));if(o.current=t,t.length>0){var n=t[0];n.focus(),n.setAttribute("tabindex","0")}}}),[]),n.createElement("div",{"data-testid":"attachment__list",className:e.classNames({"rpv-attachment__list":!0,"rpv-attachment__list--rtl":i}),ref:r,tabIndex:-1,onKeyDown:function(e){switch(e.key){case"ArrowDown":e.preventDefault(),l((function(e,t){return e.indexOf(t)+1}));break;case"ArrowUp":e.preventDefault(),l((function(e,t){return e.indexOf(t)-1}));break;case"End":e.preventDefault(),l((function(e,t){return e.length-1}));break;case"Home":e.preventDefault(),l((function(e,t){return 0}))}}},a.map((function(e){return n.createElement("button",{className:"rpv-attachment__item",key:e.fileName,tabIndex:-1,title:u,type:"button",onClick:function(){return t=e.fileName,n=e.data,a="string"==typeof n?"":URL.createObjectURL(new Blob([n],{type:""})),(r=document.createElement("a")).style.display="none",r.href=a||t,r.setAttribute("download",function(e){var t=e.split("/").pop();return t?t.split("#")[0].split("?")[0]:e}(t)),document.body.appendChild(r),r.click(),document.body.removeChild(r),void(a&&URL.revokeObjectURL(a));var t,n,a,r}},e.fileName)})))},r=function(t){var r=t.doc,c=n.useContext(e.LocalizationContext).l10n,i=n.useContext(e.ThemeContext).direction===e.TextDirection.RightToLeft,o=c&&c.attachment?c.attachment.noAttachment:"There is no attachment",u=n.useState({files:[],isLoaded:!1}),l=u[0],s=u[1];return n.useEffect((function(){r.getAttachments().then((function(e){var t=e?Object.keys(e).map((function(t){return{data:e[t].content,fileName:e[t].filename}})):[];s({files:t,isLoaded:!0})}))}),[r]),l.isLoaded?0===l.files.length?n.createElement("div",{"data-testid":"attachment__empty",className:e.classNames({"rpv-attachment__empty":!0,"rpv-attachment__empty--rtl":i})},o):n.createElement(a,{files:l.files}):n.createElement(e.Spinner,null)},c=function(t){var a=t.store,c=n.useState(a.get("doc")),i=c[0],o=c[1],u=function(e){o(e)};return n.useEffect((function(){return a.subscribe("doc",u),function(){a.unsubscribe("doc",u)}}),[]),i?n.createElement(r,{doc:i}):n.createElement("div",{className:"rpv-attachment__loader"},n.createElement(e.Spinner,null))};exports.attachmentPlugin=function(){var t=n.useMemo((function(){return e.createStore({})}),[]);return{onDocumentLoad:function(e){t.update("doc",e.doc)},Attachments:function(){return n.createElement(c,{store:t})}}};
