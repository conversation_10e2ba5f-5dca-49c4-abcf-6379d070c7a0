"use strict";var e=require("@react-pdf-viewer/core");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var r,o=t(require("react")),n=function(){return n=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},n.apply(this,arguments)},a=function(){return o.createElement(e.Icon,{size:16},o.createElement("path",{d:"M6.427,8.245A.5.5,0,0,1,6.862,7.5H17.138a.5.5,0,0,1,.435.749l-5.139,9a.5.5,0,0,1-.868,0Z"}))},i=function(){return o.createElement(e.Icon,{size:16},o.createElement("path",{d:"M9.248,17.572a.5.5,0,0,1-.748-.434V6.862a.5.5,0,0,1,.748-.434l8.992,5.138a.5.5,0,0,1,0,.868Z"}))},c=function(t){var r=t.bookmark,c=t.depth,l=t.doc,s=t.index,u=t.isBookmarkExpanded,k=t.numberOfSiblings,d=t.pathFromRoot,p=t.renderBookmarkItem,f=t.store,b=d?"".concat(d,".").concat(s):"".concat(s),v=o.useMemo((function(){return function(e){var t=e.count,r=e.items;if(t>=0)return!1;var o=r.length;if(0===o)return!1;for(var n=r.concat([]);n.length>0;){var a=n.shift(),i=a.items;a.count&&i&&a.count>0&&i.length>0&&(o+=i.length,n=n.concat(i))}return Math.abs(t)===o}(r)}),[r]),E=f.get("bookmarkExpandedMap"),g=u?u({bookmark:r,doc:l,depth:c,index:s}):E.has(b)?E.get(b):!v,h=o.useState(g),_=h[0],x=h[1],B=r.items&&r.items.length>0,I=function(){var e=!_;f.updateCurrentValue("bookmarkExpandedMap",(function(t){return t.set(b,e)})),x(e)},y=function(){var t=r.dest,o=f.get("jumpToDestination");e.getDestination(l,t).then((function(e){o&&o(n({label:r.title},e))}))},w=function(){B&&r.dest&&y()},C=function(){!B&&r.dest&&y()},D=function(e,t){return o.createElement("div",{className:"rpv-bookmark__item",style:{paddingLeft:"".concat(1.25*c,"rem")},onClick:e},t)},N=function(e,t){return B?o.createElement("span",{className:"rpv-bookmark__toggle","data-testid":"bookmark__toggle-".concat(c,"-").concat(s),onClick:I},_?e:t):o.createElement("span",{className:"rpv-bookmark__toggle"})},R=function(e){return r.url?o.createElement("a",{className:"rpv-bookmark__title",href:r.url,rel:"noopener noreferrer nofollow",target:r.newWindow?"_blank":""},r.title):o.createElement("div",{className:"rpv-bookmark__title","aria-label":r.title,onClick:e},r.title)};return o.createElement("li",{"aria-expanded":_?"true":"false","aria-label":r.title,"aria-level":c+1,"aria-posinset":s+1,"aria-setsize":k,role:"treeitem",tabIndex:-1},p?p({bookmark:r,depth:c,hasSubItems:B,index:s,isExpanded:_,path:b,defaultRenderItem:D,defaultRenderTitle:R,defaultRenderToggle:N,onClickItem:C,onClickTitle:w,onToggleSubItems:I}):D(C,o.createElement(o.Fragment,null,N(o.createElement(a,null),o.createElement(i,null)),R(w))),B&&_&&o.createElement(m,{bookmarks:r.items,depth:c+1,doc:l,isBookmarkExpanded:u,isRoot:!1,pathFromRoot:b,renderBookmarkItem:p,store:f}))},m=function(e){var t=e.bookmarks,r=e.depth,n=void 0===r?0:r,a=e.doc,i=e.isBookmarkExpanded,m=e.isRoot,l=e.pathFromRoot,s=e.renderBookmarkItem,u=e.store;return o.createElement("ul",{className:"rpv-bookmark__list",role:m?"tree":"group",tabIndex:-1},t.map((function(e,r){return o.createElement(c,{bookmark:e,depth:n,doc:a,index:r,isBookmarkExpanded:i,key:r,numberOfSiblings:t.length,pathFromRoot:l,renderBookmarkItem:s,store:u})})))};!function(e){e[e.Collapse=0]="Collapse",e[e.Expand=1]="Expand"}(r||(r={}));var l=function(e){var t=e.bookmarks,n=e.doc,a=e.isBookmarkExpanded,i=e.renderBookmarkItem,c=e.store,l=o.useRef(),s=function(e){var t=l.current;if(t&&e.target instanceof HTMLElement&&t.contains(e.target))switch(e.key){case"ArrowDown":e.preventDefault(),k((function(e,t){return e.indexOf(t)+1}));break;case"ArrowLeft":e.preventDefault(),d(r.Collapse);break;case"ArrowRight":e.preventDefault(),d(r.Expand);break;case"ArrowUp":e.preventDefault,k((function(e,t){return e.indexOf(t)-1}));break;case"End":e.preventDefault(),k((function(e,t){return e.length-1}));break;case" ":case"Enter":case"Space":e.preventDefault(),u();break;case"Home":e.preventDefault(),k((function(e,t){return 0}))}},u=function(){var e=document.activeElement.closest(".rpv-bookmark__item").querySelector(".rpv-bookmark__title");e&&e.click()},k=function(e){var t=l.current,r=[].slice.call(t.getElementsByClassName("rpv-bookmark__item"));if(0!==r.length){var o=document.activeElement,n=r[Math.min(r.length-1,Math.max(0,e(r,o)))];o.setAttribute("tabindex","-1"),n.setAttribute("tabindex","0"),n.focus()}},d=function(e){var t=l.current;if(0!==[].slice.call(t.getElementsByClassName("rpv-bookmark__item")).length){var o=document.activeElement.closest(".rpv-bookmark__item"),n=e===r.Collapse?"true":"false";if(o&&o.parentElement.getAttribute("aria-expanded")===n){var a=o.querySelector(".rpv-bookmark__toggle");a&&a.click()}}};return o.useEffect((function(){return document.addEventListener("keydown",s),function(){document.removeEventListener("keydown",s)}}),[]),o.useEffect((function(){var e=l.current;if(e){var t=[].slice.call(e.getElementsByClassName("rpv-bookmark__item"));t.length>0&&(t[0].focus(),t[0].setAttribute("tabindex","0"))}}),[]),o.createElement("div",{ref:l},o.createElement(m,{bookmarks:t,depth:0,doc:n,isBookmarkExpanded:a,isRoot:!0,pathFromRoot:"",renderBookmarkItem:i,store:c}))},s=function(t){var r=t.doc,n=t.isBookmarkExpanded,a=t.renderBookmarkItem,i=t.store,c=o.useContext(e.LocalizationContext).l10n,m=o.useContext(e.ThemeContext).direction===e.TextDirection.RightToLeft,s=o.useState({isLoaded:!1,items:[]}),u=s[0],k=s[1];return o.useEffect((function(){k({isLoaded:!1,items:[]}),r.getOutline().then((function(e){k({isLoaded:!0,items:e||[]})}))}),[r]),u.isLoaded?0===u.items.length?o.createElement("div",{"data-testid":"bookmark__empty",className:e.classNames({"rpv-bookmark__empty":!0,"rpv-bookmark__empty--rtl":m})},c&&c.bookmark?c.bookmark.noBookmark:"There is no bookmark"):o.createElement("div",{"data-testid":"bookmark__container",className:e.classNames({"rpv-bookmark__container":!0,"rpv-bookmark__container--rtl":m})},o.createElement(l,{bookmarks:u.items,doc:r,isBookmarkExpanded:n,renderBookmarkItem:a,store:i})):o.createElement("div",{className:"rpv-bookmark__loader"},o.createElement(e.Spinner,null))},u=function(t){var r=t.isBookmarkExpanded,n=t.renderBookmarkItem,a=t.store,i=o.useState(a.get("doc")),c=i[0],m=i[1],l=function(e){m(e)};return o.useEffect((function(){return a.subscribe("doc",l),function(){a.unsubscribe("doc",l)}}),[]),c?o.createElement(s,{doc:c,isBookmarkExpanded:r,renderBookmarkItem:n,store:a}):o.createElement("div",{className:"rpv-bookmark__loader"},o.createElement(e.Spinner,null))};exports.DownArrowIcon=a,exports.RightArrowIcon=i,exports.bookmarkPlugin=function(){var t=o.useMemo((function(){return e.createStore({bookmarkExpandedMap:new Map})}),[]);return{install:function(e){t.update("jumpToDestination",e.jumpToDestination)},onDocumentLoad:function(e){t.update("doc",e.doc)},Bookmarks:function(e){return o.createElement(u,{isBookmarkExpanded:null==e?void 0:e.isBookmarkExpanded,renderBookmarkItem:null==e?void 0:e.renderBookmarkItem,store:t})}}};
