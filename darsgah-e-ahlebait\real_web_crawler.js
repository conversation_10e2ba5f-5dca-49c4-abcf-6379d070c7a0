// Real Web Crawler for Google Scholar and ResearchGate
const https = require('https');
const { URL } = require('url');
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://rbvgtaqimzpsarvoxubn.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJidmd0YXFpbXpwc2Fydm94dWJuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjAxMDI0MSwiZXhwIjoyMDYxNTg2MjQxfQ.Y1Xcvq50peqVvgRhVVtHP7eEvpGBn-A2EG65H--8XX8';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Test external_citations table access on startup
console.log('🔧 [INIT] Testing external_citations table access...');
supabase.from('external_citations').select('count', { count: 'exact', head: true })
  .then(({ count, error }) => {
    if (error) {
      console.error('❌ [INIT] external_citations table access failed:', error.message);
    } else {
      console.log('✅ [INIT] external_citations table accessible, count:', count);
    }
  });

class RealWebCrawler {
  constructor() {
    this.delay = 3000; // 3 seconds between requests (respectful crawling)
    this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    this.maxRetries = 3;
  }

  async makeRequest(url) {
    return new Promise((resolve, reject) => {
      const options = {
        headers: {
          'User-Agent': this.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        },
        timeout: 15000
      };

      const req = https.get(url, options, (res) => {
        let data = '';
        
        // Handle gzip encoding
        const stream = res.headers['content-encoding'] === 'gzip' ? 
          require('zlib').createGunzip() : res;
        
        stream.on('data', (chunk) => {
          data += chunk;
        });
        
        stream.on('end', () => {
          resolve(data);
        });

        if (res.headers['content-encoding'] === 'gzip') {
          res.pipe(stream);
        }
      });

      req.on('error', (err) => {
        reject(err);
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  async searchGoogleScholar(query, maxResults = 10) {
    try {
      console.log(`🎓 Searching Google Scholar for: ${query}`);

      const searchUrl = `https://scholar.google.com/scholar?q=${encodeURIComponent(query)}&hl=en&num=${maxResults}`;
      const html = await this.makeRequest(searchUrl);

      return this.parseGoogleScholarResults(html);

    } catch (error) {
      console.error(`❌ Google Scholar search error: ${error.message}`);
      return [];
    }
  }

  async searchGoogleScholarProfiles(publicationTitle) {
    const profiles = [
      'https://scholar.google.com/citations?user=YZIkuuMAAAAJ&hl=en',
      'https://scholar.google.com/citations?user=VBEz3MUAAAAJ&hl=en'
    ];

    const results = [];

    for (const profileUrl of profiles) {
      try {
        console.log(`🔍 Searching profile: ${profileUrl}`);
        const html = await this.makeRequest(profileUrl);
        const citations = this.parseGoogleScholarProfile(html, publicationTitle);
        results.push(...citations);

        // Respectful delay between profile requests
        await new Promise(resolve => setTimeout(resolve, this.delay));
      } catch (error) {
        console.error(`❌ Error searching profile ${profileUrl}: ${error.message}`);
      }
    }

    return results;
  }

  async searchGoogleScholarProfiles(publicationTitle) {
    const profiles = [
      'https://scholar.google.com/citations?user=YZIkuuMAAAAJ&hl=en&authuser=4',
      'https://scholar.google.com/citations?user=VBEz3MUAAAAJ&hl=en&authuser=9'
    ];

    const results = [];

    for (const profileUrl of profiles) {
      try {
        console.log(`🔍 Searching profile: ${profileUrl}`);
        const html = await this.makeRequest(profileUrl);
        const citations = this.parseGoogleScholarProfile(html, publicationTitle);
        results.push(...citations);

        // Respectful delay between profile requests
        await new Promise(resolve => setTimeout(resolve, this.delay));
      } catch (error) {
        console.error(`❌ Error searching profile ${profileUrl}: ${error.message}`);
      }
    }

    return results;
  }

  parseGoogleScholarProfile(html, publicationTitle) {
    const results = [];

    try {
      // Look for publication entries in the profile
      const publicationPattern = /<tr class="gsc_a_tr">[\s\S]*?<\/tr>/g;
      const titlePattern = /<a[^>]*class="gsc_a_at"[^>]*>([^<]+)<\/a>/;
      const citationPattern = /<a[^>]*class="gsc_a_ac gs_ibl"[^>]*>(\d+)<\/a>/;
      const yearPattern = /<span class="gsc_a_h gsc_a_hc gs_ibl">(\d{4})<\/span>/;

      let match;
      while ((match = publicationPattern.exec(html)) !== null) {
        const rowHtml = match[0];

        const titleMatch = titlePattern.exec(rowHtml);
        const citationMatch = citationPattern.exec(rowHtml);
        const yearMatch = yearPattern.exec(rowHtml);

        if (titleMatch) {
          const title = this.cleanText(titleMatch[1]);
          const citations = citationMatch ? parseInt(citationMatch[1]) : 0;
          const year = yearMatch ? parseInt(yearMatch[1]) : null;

          // Check if this publication matches what we're looking for
          const similarity = this.calculateTitleSimilarity(title, publicationTitle);

          if (similarity > 0.6) { // 60% similarity threshold
            console.log(`📋 Found matching publication: "${title}" with ${citations} citations`);
            results.push({
              title: title,
              citations: citations,
              year: year,
              source: 'Google Scholar Profile',
              domain: 'scholar.google.com',
              similarity: similarity,
              url: 'https://scholar.google.com',
              citedBy: citations
            });
          }
        }
      }

      console.log(`✅ Found ${results.length} matching publications in profile`);
      return results;

    } catch (error) {
      console.error(`❌ Error parsing Google Scholar profile: ${error.message}`);
      return [];
    }
  }

  calculateTitleSimilarity(title1, title2) {
    // Simple similarity calculation based on common words
    const words1 = title1.toLowerCase().split(/\s+/).filter(word => word.length > 3);
    const words2 = title2.toLowerCase().split(/\s+/).filter(word => word.length > 3);

    if (words1.length === 0 || words2.length === 0) return 0;

    const commonWords = words1.filter(word => words2.includes(word));
    const similarity = (commonWords.length * 2) / (words1.length + words2.length);

    return similarity;
  }

  parseGoogleScholarResults(html) {
    const results = [];

    try {
      // Google Scholar result patterns
      const resultPattern = /<div class="gs_r gs_or gs_scl"[\s\S]*?<\/div>/g;
      const titlePattern = /<h3 class="gs_rt"><a[^>]*href="([^"]*)"[^>]*>([^<]*)<\/a><\/h3>/;
      const citedByPattern = /Cited by (\d+)/;
      const authorPattern = /<div class="gs_a">([^<]*)<\/div>/;
      const snippetPattern = /<div class="gs_rs">([^<]*)<\/div>/;

      let match;
      while ((match = resultPattern.exec(html)) !== null && results.length < 10) {
        const resultHtml = match[0];

        const titleMatch = titlePattern.exec(resultHtml);
        const citedByMatch = citedByPattern.exec(resultHtml);
        const authorMatch = authorPattern.exec(resultHtml);
        const snippetMatch = snippetPattern.exec(resultHtml);

        if (titleMatch) {
          results.push({
            url: titleMatch[1],
            title: this.cleanText(titleMatch[2]),
            authors: authorMatch ? this.cleanText(authorMatch[1]) : '',
            snippet: snippetMatch ? this.cleanText(snippetMatch[1]) : '',
            citedBy: citedByMatch ? parseInt(citedByMatch[1]) : 0,
            source: 'Google Scholar',
            domain: 'scholar.google.com'
          });
        }
      }

      console.log(`✅ Found ${results.length} Google Scholar results`);
      return results;

    } catch (error) {
      console.error(`❌ Error parsing Google Scholar results: ${error.message}`);
      return [];
    }
  }

  parseGoogleScholarProfile(html, publicationTitle) {
    const results = [];

    try {
      // Look for publication entries in the profile
      const publicationPattern = /<tr class="gsc_a_tr">[\s\S]*?<\/tr>/g;
      const titlePattern = /<a[^>]*class="gsc_a_at"[^>]*>([^<]+)<\/a>/;
      const citationPattern = /<a[^>]*class="gsc_a_ac gs_ibl"[^>]*>(\d+)<\/a>/;
      const yearPattern = /<span class="gsc_a_h gsc_a_hc gs_ibl">(\d{4})<\/span>/;

      let match;
      while ((match = publicationPattern.exec(html)) !== null) {
        const rowHtml = match[0];

        const titleMatch = titlePattern.exec(rowHtml);
        const citationMatch = citationPattern.exec(rowHtml);
        const yearMatch = yearPattern.exec(rowHtml);

        if (titleMatch) {
          const title = this.cleanText(titleMatch[1]);
          const citations = citationMatch ? parseInt(citationMatch[1]) : 0;
          const year = yearMatch ? parseInt(yearMatch[1]) : null;

          // Check if this publication matches what we're looking for
          const similarity = this.calculateTitleSimilarity(title, publicationTitle);

          if (similarity > 0.6) { // 60% similarity threshold
            console.log(`📋 Found matching publication: "${title}" with ${citations} citations`);
            results.push({
              title: title,
              citations: citations,
              year: year,
              source: 'Google Scholar Profile',
              domain: 'scholar.google.com',
              similarity: similarity,
              url: 'https://scholar.google.com',
              citedBy: citations
            });
          }
        }
      }

      console.log(`✅ Found ${results.length} matching publications in profile`);
      return results;

    } catch (error) {
      console.error(`❌ Error parsing Google Scholar profile: ${error.message}`);
      return [];
    }
  }

  calculateTitleSimilarity(title1, title2) {
    // Simple similarity calculation based on common words
    const words1 = title1.toLowerCase().split(/\s+/).filter(word => word.length > 3);
    const words2 = title2.toLowerCase().split(/\s+/).filter(word => word.length > 3);

    if (words1.length === 0 || words2.length === 0) return 0;

    const commonWords = words1.filter(word => words2.includes(word));
    const similarity = (commonWords.length * 2) / (words1.length + words2.length);

    return similarity;
  }

  async searchResearchGate(query, maxResults = 10) {
    try {
      console.log(`🔬 Searching ResearchGate for: ${query}`);
      
      const searchUrl = `https://www.researchgate.net/search?q=${encodeURIComponent(query)}`;
      const html = await this.makeRequest(searchUrl);
      
      return this.parseResearchGateResults(html);
      
    } catch (error) {
      console.error(`❌ ResearchGate search error: ${error.message}`);
      return [];
    }
  }

  parseResearchGateResults(html) {
    const results = [];
    
    try {
      // ResearchGate result patterns (simplified)
      const titlePattern = /<a[^>]*class="[^"]*nova-legacy-e-link[^"]*"[^>]*href="([^"]*)"[^>]*>([^<]*)<\/a>/g;
      const authorPattern = /<span[^>]*class="[^"]*nova-legacy-v-person-list-item__fullname[^"]*"[^>]*>([^<]*)<\/span>/g;
      
      let match;
      let count = 0;
      while ((match = titlePattern.exec(html)) !== null && count < maxResults) {
        const url = match[1].startsWith('http') ? match[1] : `https://www.researchgate.net${match[1]}`;
        const title = this.cleanText(match[2]);
        
        if (title && title.length > 10) { // Filter out short/invalid titles
          results.push({
            url: url,
            title: title,
            authors: '',
            snippet: '',
            citedBy: 0,
            source: 'ResearchGate',
            domain: 'researchgate.net'
          });
          count++;
        }
      }
      
      console.log(`✅ Found ${results.length} ResearchGate results`);
      return results;
      
    } catch (error) {
      console.error(`❌ Error parsing ResearchGate results: ${error.message}`);
      return [];
    }
  }

  cleanText(text) {
    return text
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .trim();
  }

  analyzeCitation(result, publicationTitle, authors = '') {
    const title = result.title.toLowerCase();
    const snippet = result.snippet.toLowerCase();
    const resultAuthors = result.authors.toLowerCase();
    
    const pubTitleLower = publicationTitle.toLowerCase();
    const authorsLower = authors.toLowerCase();
    
    let confidenceScore = 0.0;
    let citationType = 'mention';

    // Check for exact title matches
    if (title.includes(pubTitleLower)) {
      confidenceScore += 0.6;
    }

    // Check for partial title matches
    const titleWords = pubTitleLower.split(' ').filter(word => word.length > 3);
    const matchingWords = titleWords.filter(word => title.includes(word));
    if (matchingWords.length > 0) {
      confidenceScore += (matchingWords.length / titleWords.length) * 0.3;
    }

    // Check for author matches
    if (authorsLower && resultAuthors) {
      const authorList = authorsLower.split(',').map(a => a.trim());
      const authorMatches = authorList.some(author => 
        resultAuthors.includes(author) || title.includes(author)
      );
      if (authorMatches) {
        confidenceScore += 0.4;
      }
    }

    // Boost score for academic sources
    if (result.source === 'Google Scholar') {
      confidenceScore += 0.2;
      citationType = 'reference';
    }

    // Check for citation indicators
    if (result.citedBy > 0) {
      confidenceScore += 0.1;
      citationType = 'reference';
    }

    // Check for research keywords
    const researchKeywords = ['research', 'study', 'analysis', 'investigation', 'paper', 'article'];
    if (researchKeywords.some(keyword => title.includes(keyword) || snippet.includes(keyword))) {
      confidenceScore += 0.1;
    }

    return {
      url: result.url,
      title: result.title,
      snippet: result.snippet || result.authors,
      domain: result.domain,
      confidenceScore: Math.min(confidenceScore, 1.0),
      citationType,
      foundDate: new Date().toISOString(),
      metadata: {
        source: result.source,
        citedBy: result.citedBy,
        authors: result.authors
      }
    };
  }

  async saveCitationToDatabase(publicationId, citation) {
    try {
      console.log(`💾 [SAVE] Attempting to save citation for publication ${publicationId}`);
      console.log(`📋 [SAVE] Citation data:`, {
        title: citation.title,
        url: citation.url,
        domain: citation.domain,
        snippet: citation.snippet,
        citationContext: citation.citationContext,
        metadata: citation.metadata
      });

      // Check if citation already exists
      const sourceUrl = citation.url || 'https://scholar.google.com';
      console.log(`🔍 [SAVE] Checking for existing citation with URL: ${sourceUrl}`);

      const { data: existing, error: checkError } = await supabase
        .from('external_citations')
        .select('id')
        .eq('publication_id', publicationId)
        .eq('source_url', sourceUrl)
        .single();

      console.log(`🔍 [SAVE] Existing citation check:`, { existing, checkError });

      if (existing) {
        // Instead of skipping, UPDATE the existing citation with new data
        console.log(`🔄 [SAVE] Citation exists, updating with new data: ${citation.title?.substring(0, 50)}...`);

        const updateData = {
          citation_context: citation.snippet || citation.citationContext || 'No context available',
          metadata: citation.metadata || {},
          confidence_score: citation.confidenceScore || 0.5,
          found_date: new Date().toISOString(),
          is_verified: (citation.confidenceScore || 0.5) > 0.8
        };

        console.log(`🔄 [SAVE] Updating citation with:`, updateData);

        const { data: updateResult, error: updateError } = await supabase
          .from('external_citations')
          .update(updateData)
          .eq('id', existing.id);

        console.log(`🔄 [SAVE] Update result:`, { updateResult, updateError });

        if (updateError) {
          console.error(`❌ [SAVE] Update error: ${updateError.message}`);
          return false;
        }

        console.log(`✅ [SAVE] Successfully updated existing citation with new data`);
        return true;
      }

      // TEMPORARY: Force save even if duplicate (for testing)
      if (checkError && checkError.code === 'PGRST116') {
        console.log(`🔧 [SAVE] No existing citation found (expected), proceeding with save...`);
      }

      // Insert new citation
      const insertData = {
        publication_id: publicationId,
        source_url: citation.url || 'https://scholar.google.com',
        source_title: citation.title || 'Unknown Title',
        source_domain: citation.domain || 'scholar.google.com',
        citation_context: citation.snippet || citation.citationContext || 'No context available',
        citation_type: citation.citationType || 'reference',
        confidence_score: citation.confidenceScore || 0.5,
        found_date: citation.foundDate || new Date().toISOString(),
        is_verified: (citation.confidenceScore || 0.5) > 0.8,
        is_active: true,
        metadata: citation.metadata || {}
      };

      console.log(`💾 [SAVE] Inserting citation data:`, insertData);

      const { data, error } = await supabase
        .from('external_citations')
        .insert(insertData);

      console.log(`💾 [SAVE] Insert result:`, { data, error });

      if (error) {
        console.error(`❌ [SAVE] Database error: ${error.message}`);
        console.error(`❌ [SAVE] Full error:`, error);
        return false;
      }

      console.log(`✅ [SAVE] Successfully saved citation: ${citation.title?.substring(0, 50)}...`);
      return true;

    } catch (error) {
      console.error(`❌ Error saving citation: ${error.message}`);
      return false;
    }
  }

  async updatePublicationCitationCount(publicationId) {
    try {
      const { data: citations, error } = await supabase
        .from('external_citations')
        .select('id, citation_context, metadata')
        .eq('publication_id', publicationId)
        .eq('is_active', true);

      if (error) {
        console.error(`❌ Error counting citations: ${error.message}`);
        return false;
      }

      let totalCitationCount = 0;

      if (citations && citations.length > 0) {
        for (const citation of citations) {
          // Extract citation number from citation_context
          const citationNumber = this.extractCitationNumber(citation.citation_context, citation.metadata);
          totalCitationCount += citationNumber;
        }
      }

      const { error: updateError } = await supabase
        .from('publications')
        .update({
          external_citation_count: totalCitationCount
        })
        .eq('id', publicationId);

      if (updateError) {
        console.error(`❌ Error updating publication: ${updateError.message}`);
        return false;
      }

      console.log(`✅ Updated publication with ${totalCitationCount} total external citations (extracted from ${citations.length} records)`);
      return true;

    } catch (error) {
      console.error(`❌ Error updating citation count: ${error.message}`);
      return false;
    }
  }

  extractCitationNumber(citationContext, metadata) {
    try {
      // First try to get from metadata if available
      if (metadata && metadata.citationCount) {
        return parseInt(metadata.citationCount) || 0;
      }

      // Extract number from citation context text
      if (citationContext) {
        // Look for patterns like "with 7 citations" or "7 citations"
        const patterns = [
          /with (\d+) citations/i,
          /(\d+) citations/i,
          /cited (\d+) times/i,
          /(\d+) times cited/i
        ];

        for (const pattern of patterns) {
          const match = citationContext.match(pattern);
          if (match && match[1]) {
            const number = parseInt(match[1]);
            console.log(`📊 Extracted ${number} citations from: "${citationContext}"`);
            return number;
          }
        }
      }

      // Default to 1 if no number found but citation exists
      return 1;

    } catch (error) {
      console.error(`❌ Error extracting citation number: ${error.message}`);
      return 1;
    }
  }

  async crawlPublication(publication, progressCallback = null) {
    try {
      const startMsg = `🚀 Starting real web crawl for: ${publication.title}`;
      console.log(`\n${startMsg}`);
      if (progressCallback) {
        progressCallback({
          type: 'info',
          message: startMsg,
          currentPublication: publication.title,
          currentSite: 'Initializing'
        });
        console.log('📡 [CRAWL] Sent initial progress update');
      }

      const prepMsg = `⏰ This will search Google Scholar and ResearchGate...`;
      console.log(prepMsg);
      if (progressCallback) progressCallback({
        type: 'info',
        message: prepMsg,
        currentSite: 'Preparing'
      });

      const queries = [
        `"${publication.title}"`,
        publication.authors ? `"${publication.title}" "${publication.authors.split(',')[0].trim()}"` : null,
        publication.doi ? `"${publication.doi}"` : null
      ].filter(Boolean);

      let totalCitationsFound = 0;
      let totalSaved = 0;

      for (let queryIndex = 0; queryIndex < queries.slice(0, 2).length; queryIndex++) {
        const query = queries[queryIndex];
        const queryMsg = `🔍 Query ${queryIndex + 1}/2: ${query}`;
        console.log(`\n${queryMsg}`);
        if (progressCallback) progressCallback({
          type: 'info',
          message: queryMsg,
          currentSite: 'Preparing Query'
        });

        // Search Google Scholar Profiles (specific profiles)
        const profileMsg = `🎓 Searching Google Scholar profiles for: ${publication.title}`;
        console.log(profileMsg);
        if (progressCallback) progressCallback({
          type: 'info',
          message: profileMsg,
          currentSite: 'Google Scholar Profiles'
        });

        const profileResults = await this.searchGoogleScholarProfiles(publication.title);
        const profileResultMsg = `✅ Found ${profileResults.length} matching publications in profiles`;
        console.log(profileResultMsg);
        if (progressCallback) progressCallback({
          type: 'info',
          message: profileResultMsg,
          currentSite: 'Google Scholar Profiles'
        });

        await new Promise(resolve => setTimeout(resolve, this.delay));

        // Also search regular Google Scholar
        const scholarMsg = `🎓 Searching Google Scholar for: ${query}`;
        console.log(scholarMsg);
        if (progressCallback) progressCallback({
          type: 'info',
          message: scholarMsg,
          currentSite: 'Google Scholar'
        });

        const scholarResults = await this.searchGoogleScholar(query, 5);
        const scholarResultMsg = `✅ Found ${scholarResults.length} Google Scholar results`;
        console.log(scholarResultMsg);
        if (progressCallback) progressCallback({
          type: 'info',
          message: scholarResultMsg,
          currentSite: 'Google Scholar'
        });

        await new Promise(resolve => setTimeout(resolve, this.delay));

        // Search ResearchGate
        const rgMsg = `🔬 Searching ResearchGate for: ${query}`;
        console.log(rgMsg);
        if (progressCallback) progressCallback({
          type: 'info',
          message: rgMsg,
          currentSite: 'ResearchGate'
        });

        const rgResults = await this.searchResearchGate(query, 5);
        const rgResultMsg = `✅ Found ${rgResults.length} ResearchGate results`;
        console.log(rgResultMsg);
        if (progressCallback) progressCallback({
          type: 'info',
          message: rgResultMsg,
          currentSite: 'ResearchGate'
        });

        await new Promise(resolve => setTimeout(resolve, this.delay));

        // Combine and analyze results
        const allResults = [...profileResults, ...scholarResults, ...rgResults];

        if (allResults.length > 0) {
          const analyzeMsg = `🔍 Analyzing ${allResults.length} results (${profileResults.length} from profiles, ${scholarResults.length} from Scholar, ${rgResults.length} from ResearchGate)...`;
          console.log(analyzeMsg);
          if (progressCallback) progressCallback({
            type: 'info',
            message: analyzeMsg,
            currentSite: 'Analyzing Results'
          });
        }

        for (const result of allResults) {
          let citation;

          // Handle profile results differently (they already have citation counts)
          if (result.source === 'Google Scholar Profile') {
            citation = {
              title: result.title,
              url: result.url || 'https://scholar.google.com', // Ensure url is not null
              domain: result.domain || 'scholar.google.com',
              citationType: 'reference',
              confidenceScore: result.similarity,
              snippet: `Found in Google Scholar profile with ${result.citations} citations`,
              citationContext: `Found in Google Scholar profile with ${result.citations} citations`,
              foundDate: new Date().toISOString(),
              metadata: {
                citationCount: result.citations,
                year: result.year,
                source: 'Google Scholar Profile',
                extractedCitationNumber: result.citations
              }
            };
          } else {
            citation = this.analyzeCitation(result, publication.title, publication.authors);
            // For regular citations, default to 1 citation per result
            if (!citation.metadata) {
              citation.metadata = {};
            }
            citation.metadata.citationCount = citation.metadata.citationCount || 1;
          }

          if (citation.confidenceScore > 0.4) { // Higher threshold for real results
            totalCitationsFound++;
            const citationMsg = result.source === 'Google Scholar Profile' ?
              `📋 Found in profile: "${citation.title}" with ${result.citations} citations (similarity: ${citation.confidenceScore.toFixed(2)})` :
              `📋 Found citation (confidence: ${citation.confidenceScore.toFixed(2)}): ${citation.title.substring(0, 60)}...`;

            console.log(citationMsg);
            if (progressCallback) progressCallback({
              type: 'success',
              message: citationMsg,
              currentSite: result.source === 'Google Scholar Profile' ? 'Profile Match' : 'Found Citation',
              totalCitationsFound: totalCitationsFound
            });

            const saved = await this.saveCitationToDatabase(publication.id, citation);
            if (saved) {
              totalSaved++;
              const savedMsg = `💾 Saved citation to database`;
              console.log(savedMsg);
              if (progressCallback) progressCallback({
                type: 'success',
                message: savedMsg,
                currentSite: 'Saving to Database'
              });
            }
          }
        }
      }

      const updateMsg = `🔄 Updating publication citation count...`;
      console.log(updateMsg);
      if (progressCallback) progressCallback({
        type: 'info',
        message: updateMsg,
        currentSite: 'Updating Database'
      });

      await this.updatePublicationCitationCount(publication.id);

      const completedMsg = `✅ Real crawl completed for "${publication.title}"`;
      console.log(`\n${completedMsg}`);
      console.log(`   Citations found: ${totalCitationsFound}`);
      console.log(`   New citations saved: ${totalSaved}`);

      if (progressCallback) progressCallback({
        type: 'success',
        message: `${completedMsg} - Found: ${totalCitationsFound}, Saved: ${totalSaved}`,
        currentSite: 'Completed'
      });

      return {
        publicationId: publication.id,
        citationsFound: totalCitationsFound,
        citationsSaved: totalSaved
      };

    } catch (error) {
      const errorMsg = `❌ Error crawling publication: ${error.message}`;
      console.error(errorMsg);
      if (progressCallback) progressCallback({
        type: 'error',
        message: errorMsg,
        currentSite: 'Error'
      });
      return {
        publicationId: publication.id,
        citationsFound: 0,
        citationsSaved: 0,
        error: error.message
      };
    }
  }

  async crawlAllPublications() {
    try {
      console.log('🚀 Starting real web crawl for all publications...');
      console.log('🎓 Searching Google Scholar and ResearchGate...');
      console.log('⏰ This will take 5-15 minutes for thorough searching');

      const { data: publications, error } = await supabase
        .from('publications')
        .select('id, title, authors, doi, journal, year');

      console.log('DEBUG: Publications fetched from Supabase:', publications);
      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      console.log(`📚 Found ${publications.length} publications to crawl`);

      const results = [];
      let totalCitationsFound = 0;
      let totalSaved = 0;

      for (const publication of publications) {
        const result = await this.crawlPublication(publication);
        results.push(result);
        totalCitationsFound += result.citationsFound;
        totalSaved += result.citationsSaved;

        // Longer delay between publications for respectful crawling
        await new Promise(resolve => setTimeout(resolve, this.delay * 2));
      }

      console.log(`\n🎉 Real web crawl completed!`);
      console.log(`   Publications processed: ${publications.length}`);
      console.log(`   Total citations found: ${totalCitationsFound}`);
      console.log(`   Total new citations saved: ${totalSaved}`);

      return {
        publicationsProcessed: publications.length,
        totalCitationsFound,
        totalSaved,
        results
      };

    } catch (error) {
      console.error(`❌ Error in real web crawl: ${error.message}`);
      throw error;
    }
  }

  async crawlPublicationWithProgress(publication, progressCallback) {
    try {
      progressCallback({
        type: 'info',
        message: `Starting crawl for: ${publication.title}`,
        currentPublication: publication.title,
        currentSite: 'Initializing...'
      });

      let totalCitationsFound = 0;
      let totalSaved = 0;

      // Create search queries
      const queries = [publication.title];
      if (publication.doi) {
        queries.push(publication.doi);
      }

      for (const query of queries) {
        progressCallback({
          type: 'info',
          message: `Searching Google Scholar for: ${query.substring(0, 50)}...`,
          currentSite: 'Google Scholar',
          currentPublication: publication.title
        });

        // Search Google Scholar
        const scholarResults = await this.searchGoogleScholar(query, 5);
        await new Promise(resolve => setTimeout(resolve, this.delay));

        progressCallback({
          type: 'info',
          message: `Found ${scholarResults.length} results on Google Scholar`,
          currentSite: 'Google Scholar'
        });

        progressCallback({
          type: 'info',
          message: `Searching ResearchGate for: ${query.substring(0, 50)}...`,
          currentSite: 'ResearchGate',
          currentPublication: publication.title
        });

        // Search ResearchGate
        const rgResults = await this.searchResearchGate(query, 5);
        await new Promise(resolve => setTimeout(resolve, this.delay));

        progressCallback({
          type: 'info',
          message: `Found ${rgResults.length} results on ResearchGate`,
          currentSite: 'ResearchGate'
        });

        // Combine and analyze results
        const allResults = [...scholarResults, ...rgResults];

        for (const result of allResults) {
          const citation = this.analyzeCitation(result, publication.title, publication.authors);

          if (citation.confidenceScore > 0.4) {
            totalCitationsFound++;
            progressCallback({
              type: 'success',
              message: `Found citation (confidence: ${(citation.confidenceScore * 100).toFixed(0)}%): ${citation.title.substring(0, 60)}...`,
              citationsFound: totalCitationsFound,
              totalCitationsFound: totalCitationsFound
            });

            const saved = await this.saveCitationToDatabase(publication.id, citation);
            if (saved) {
              totalSaved++;
              progressCallback({
                type: 'success',
                message: `Saved citation: ${citation.title.substring(0, 60)}...`
              });
            }
          }
        }
      }

      await this.updatePublicationCitationCount(publication.id);

      progressCallback({
        type: 'success',
        message: `Completed "${publication.title}" - Found: ${totalCitationsFound}, Saved: ${totalSaved}`
      });

      return {
        publicationId: publication.id,
        citationsFound: totalCitationsFound,
        citationsSaved: totalSaved
      };

    } catch (error) {
      progressCallback({
        type: 'error',
        message: `Error crawling publication: ${error.message}`
      });
      return {
        publicationId: publication.id,
        citationsFound: 0,
        citationsSaved: 0,
        error: error.message
      };
    }
  }

  async crawlAllPublicationsWithProgress(progressCallback) {
    try {
      progressCallback({
        type: 'info',
        message: 'Starting real web crawl for all publications...'
      });

      const { data: publications, error } = await supabase
        .from('publications')
        .select('id, title, authors, doi, journal, year');

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      progressCallback({
        type: 'info',
        message: `Found ${publications.length} publications to crawl`,
        total: publications.length,
        current: 0,
        percentage: 0
      });

      const results = [];
      let totalCitationsFound = 0;
      let totalSaved = 0;

      for (let i = 0; i < publications.length; i++) {
        const publication = publications[i];
        const percentage = Math.round(((i + 1) / publications.length) * 100);

        progressCallback({
          type: 'info',
          message: `Processing publication ${i + 1} of ${publications.length}`,
          current: i + 1,
          total: publications.length,
          percentage: percentage,
          currentPublication: publication.title
        });

        const result = await this.crawlPublicationWithProgress(publication, progressCallback);
        results.push(result);
        totalCitationsFound += result.citationsFound;
        totalSaved += result.citationsSaved;

        progressCallback({
          type: 'info',
          message: `Completed ${i + 1}/${publications.length} publications. Total citations found: ${totalCitationsFound}`,
          totalCitationsFound: totalCitationsFound
        });

        // Longer delay between publications for respectful crawling
        await new Promise(resolve => setTimeout(resolve, this.delay * 2));
      }

      progressCallback({
        type: 'success',
        message: `All publications completed! Processed: ${publications.length}, Found: ${totalCitationsFound}, Saved: ${totalSaved}`,
        percentage: 100
      });

      return {
        publicationsProcessed: publications.length,
        totalCitationsFound,
        totalSaved,
        results
      };

    } catch (error) {
      progressCallback({
        type: 'error',
        message: `Error in real web crawl: ${error.message}`
      });
      throw error;
    }
  }
}

module.exports = { RealWebCrawler };
