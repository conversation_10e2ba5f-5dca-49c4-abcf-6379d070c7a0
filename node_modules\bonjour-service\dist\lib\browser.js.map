{"version": 3, "file": "browser.js", "sourceRoot": "", "sources": ["../../src/lib/browser.ts"], "names": [], "mappings": ";;;;;;AACA,wDAAoF;AACpF,kEAA4F;AAC5F,mCAAiF;AAEjF,mDAA0F;AAC1F,4EAAiG;AACjG,oEAA6F;AAC7F,kEAA0F;AAE1F,MAAM,GAAG,GAAa,QAAQ,CAAA;AAC9B,MAAM,QAAQ,GAAQ,wBAAwB,GAAG,GAAG,CAAA;AA2BpD,MAAa,OAAQ,SAAQ,qBAAY;IAarC,YAAY,IAAS,EAAE,IAAwC,EAAE,IAAkB;QAC/E,KAAK,EAAE,CAAA;QAXH,eAAU,GAAoC,SAAS,CAAA;QACvD,eAAU,GAAgB,EAAE,CAAA;QAK5B,aAAQ,GAAkB,KAAK,CAAA;QAE/B,cAAS,GAAkB,EAAE,CAAA;QAKjC,IAAI,OAAO,IAAI,KAAK,UAAU;YAAE,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAmB,CAAC,CAAA;QAEnF,IAAI,CAAC,IAAI,GAAK,IAAI,CAAA;QAClB,IAAI,CAAC,GAAG,GAAM,IAAI,iBAAM,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;QAGlF,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC3C,IAAI,CAAC,IAAI,GAAS,QAAQ,CAAA;YAC1B,IAAI,CAAC,QAAQ,GAAK,IAAI,CAAA;QAC1B,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,IAAI,GAAG,IAAA,wBAAe,EAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,KAAK,EAAC,CAAC,GAAG,GAAG,CAAA;YACvF,IAAI,IAAI,CAAC,IAAI;gBAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAA;YACtD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;QACzB,CAAC;QAGD,IAAG,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS;YAAE,IAAI,CAAC,QAAQ,GAAG,IAAA,oBAAS,EAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAE9E,IAAI,IAAI;YAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAE7B,IAAI,CAAC,KAAK,EAAE,CAAA;IAChB,CAAC;IAEM,KAAK;QACR,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS;YAAE,OAAM;QAEtD,IAAI,IAAI,GAAG,IAAI,CAAA;QAKf,IAAI,OAAO,GAAa,EAAE,CAAA;QAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;QAE7C,IAAI,CAAC,UAAU,GAAG,CAAC,MAAW,EAAE,KAAU,EAAE,EAAE;YAC1C,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAW,EAAE,EAAE;oBACnC,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,IAAI,OAAO;wBAAE,OAAM;oBACxF,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;oBAC3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;gBACvC,CAAC,CAAC,CAAA;YACN,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI;gBAEvC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAGlE,IAAI,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;gBAClE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;oBAAE,OAAM;gBAEhC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAgB,EAAE,EAAE;oBACjC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;wBAChC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;wBAC3B,OAAM;oBACV,CAAC;oBACD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;gBAC5B,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;QACN,CAAC,CAAA;QAED,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;QACzC,IAAI,CAAC,MAAM,EAAE,CAAA;IACjB,CAAC;IAEM,IAAI;QACP,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAM;QAE5B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;QACrD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;IAC/B,CAAC;IAEM,MAAM;QACT,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IACrC,CAAC;IAED,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAEO,UAAU,CAAC,OAAgB;QAE/B,IAAG,IAAA,wBAAa,EAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK;YAAE,OAAM;QAC1D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC5B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;QACpC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC5B,CAAC;IAEO,aAAa,CAAC,OAAgB;;QAElC,IAAI,IAAA,mBAAQ,EAAC,OAAO,CAAC,GAAG,EAAE,CAAA,MAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,mBAAQ,EAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,0CAAE,GAAG,KAAI,EAAE,CAAC;YAAE,OAAM;QAExG,IAAG,CAAC,IAAA,wBAAa,EAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAChC,OAAM;QACV,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC;YAC3C,IAAI,CAAC,IAAA,mBAAQ,EAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC;gBAAE,OAAO,CAAC,CAAA;YAC7C,OAAO,OAAO,CAAA;QAClB,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;IAEO,aAAa,CAAC,IAAY;QAC9B,IAAI,OAAO,EAAE,KAAK,CAAA;QAClB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,IAAG,IAAA,mBAAQ,EAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;gBACxB,OAAO,GAAG,CAAC,CAAA;gBACX,KAAK,GAAG,CAAC,CAAA;gBACT,OAAO,IAAI,CAAA;YACf,CAAC;QACL,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,OAAO,IAAI,KAAK,KAAK,SAAS;YAAE,OAAM;QAC3C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC9B,CAAC;IAWO,QAAQ,CAAC,IAAY,EAAE,MAAW;QACtC,OAAO,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC;aAC/C,MAAM,CAAC,CAAC,EAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,IAAA,mBAAQ,EAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aAC3F,GAAG,CAAC,CAAC,EAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;IACxC,CAAC;IAUO,gBAAgB,CAAC,IAAY,EAAE,MAAW,EAAE,GAAa,EAAE,OAAY;QAC3E,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,MAAM,CAAE,CAAC,EAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;QAElG,OAAO,OAAO;aACX,MAAM,CAAC,CAAC,EAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,KAAK,IAAI,IAAA,mBAAQ,EAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aAC3E,GAAG,CAAC,CAAC,GAAkB,EAAE,EAAE;YAC1B,MAAM,OAAO,GAAa;gBACxB,SAAS,EAAE,EAAE;gBACb,QAAQ,EAAE,EAAE;aACb,CAAA;YAED,OAAO,CAAC,MAAM,CAAC,CAAC,EAAiB,EAAE,EAAE;gBACjC,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,KAAK,IAAI,IAAA,mBAAQ,EAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAA;YACxF,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAiB,EAAE,EAAE;gBAC/B,MAAM,KAAK,GAAG,IAAA,sBAAa,EAAC,EAAE,CAAC,IAAI,CAAC,CAAA;gBACpC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;YACxC,CAAC,CAAC,CAAA;YAEF,OAAO;iBACJ,MAAM,CAAC,CAAC,EAAiB,EAAE,EAAE;gBAC5B,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAAA,mBAAQ,EAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;YAChF,CAAC,CAAC;iBACD,OAAO,CAAC,CAAC,EAAiB,EAAE,EAAE;gBAC7B,IAAI,EAAE,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;oBACtB,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;oBAC9B,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;oBACnB,IAAI,KAAK,GAAG,IAAA,sBAAa,EAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;oBACvD,OAAO,CAAC,IAAI,GAAG,IAAI,CAAA;oBACnB,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAA;oBACtB,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAA;oBAC7B,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;oBACzB,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAA;oBAC3B,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAA;oBACzB,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAA;gBACnC,CAAC;qBAAM,IAAI,EAAE,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;oBAC7B,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,IAAI,CAAA;oBACxB,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;gBAC3C,CAAC;YACH,CAAC,CAAC,CAAA;YAEJ,IAAI,CAAC,OAAO,CAAC,IAAI;gBAAE,OAAM;YAEzB,OAAO;iBACJ,MAAM,CAAC,CAAC,EAAiB,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAA,mBAAQ,EAAC,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;iBACzG,OAAO,CAAC,CAAC,EAAiB,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAA;YAElE,OAAO,OAAO,CAAA;QAChB,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,EAAiB,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;IACxC,CAAC;CAEN;AAjND,0BAiNC;AAED,kBAAe,OAAO,CAAA"}