"use strict";var e=require("@react-pdf-viewer/core");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var n=t(require("react")),r=function(){return n.createElement(e.Icon,{size:16},n.createElement("path",{d:"M19.5,15.106l2.4-2.4a1,1,0,0,0,0-1.414l-2.4-2.4V5.5a1,1,0,0,0-1-1H15.106l-2.4-2.4a1,1,0,0,0-1.414,0l-2.4,2.4H5.5a1,1,0,0,0-1,1V8.894l-2.4,2.4a1,1,0,0,0,0,1.414l2.4,2.4V18.5a1,1,0,0,0,1,1H8.894l2.4,2.4a1,1,0,0,0,1.414,0l2.4-2.4H18.5a1,1,0,0,0,1-1Z"}),n.createElement("path",{d:"M10,6.349a6,6,0,0,1,0,11.3,6,6,0,1,0,0-11.3Z"}))},c=function(){return n.createElement(e.Icon,{size:16},n.createElement("path",{d:"M19.491,15.106l2.4-2.4a1,1,0,0,0,0-1.414l-2.4-2.4V5.5a1,1,0,0,0-1-1H15.1L12.7,2.1a1,1,0,0,0-1.414,0l-2.4,2.4H5.491a1,1,0,0,0-1,1V8.894l-2.4,2.4a1,1,0,0,0,0,1.414l2.4,2.4V18.5a1,1,0,0,0,1,1H8.885l2.4,2.4a1,1,0,0,0,1.414,0l2.4-2.4h3.394a1,1,0,0,0,1-1Z"}),n.createElement("path",{d:"M11.491,6c4,0,6,2.686,6,6s-2,6-6,6Z"}))},o=function(){return o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var c in t=arguments[n])Object.prototype.hasOwnProperty.call(t,c)&&(e[c]=t[c]);return e},o.apply(this,arguments)},a={left:0,top:8},i=function(t){var o=t.onClick,i=n.useContext(e.ThemeContext),l=n.useContext(e.LocalizationContext).l10n,u="dark"===i.currentTheme,h=l&&l.theme?u?l.theme.switchLightTheme:l.theme.switchDarkTheme:u?"Switch to the light theme":"Switch to the dark theme";return n.createElement(e.Tooltip,{ariaControlsSuffix:"theme-switch",position:e.Position.BottomCenter,target:n.createElement(e.MinimalButton,{ariaLabel:h,testId:"theme__switch-button",onClick:o},u?n.createElement(c,null):n.createElement(r,null)),content:function(){return h},offset:a})},l=function(t){var r=t.children,c=n.useContext(e.ThemeContext);return(r||function(e){return n.createElement(i,{onClick:e.onClick})})({onClick:function(){return c.setCurrentTheme("dark"===c.currentTheme?"light":"dark")}})},u=function(t){var o=t.onClick,a=n.useContext(e.ThemeContext),i=n.useContext(e.LocalizationContext).l10n,l="dark"===a.currentTheme,u=i&&i.theme?l?i.theme.switchLightTheme:i.theme.switchDarkTheme:l?"Switch to the light theme":"Switch to the dark theme";return n.createElement(e.MenuItem,{icon:l?n.createElement(c,null):n.createElement(r,null),testId:"theme__switch-menu",onClick:o},u)};exports.DarkIcon=r,exports.LightIcon=c,exports.themePlugin=function(){var e=function(e){return n.createElement(l,o({},e))};return{SwitchTheme:e,SwitchThemeButton:function(){return n.createElement(e,null,(function(e){return n.createElement(i,o({},e))}))},SwitchThemeMenuItem:function(t){return n.createElement(e,null,(function(e){return n.createElement(u,{onClick:function(){e.onClick(),t.onClick()}})}))}}};
