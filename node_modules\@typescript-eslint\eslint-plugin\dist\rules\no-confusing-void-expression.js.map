{"version": 3, "file": "no-confusing-void-expression.js", "sourceRoot": "", "sources": ["../../src/rules/no-confusing-void-expression.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AAmBhC,kBAAe,IAAI,CAAC,UAAU,CAAqB;IACjD,IAAI,EAAE,8BAA8B;IACpC,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,kEAAkE;YACpE,WAAW,EAAE,KAAK;YAClB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,eAAe,EACb,oEAAoE;gBACpE,uCAAuC;YACzC,uBAAuB,EACrB,kDAAkD;gBAClD,qCAAqC;gBACrC,gDAAgD;YAClD,oBAAoB,EAClB,6EAA6E;gBAC7E,0CAA0C;YAC5C,4BAA4B,EAC1B,6DAA6D;gBAC7D,qDAAqD;YACvD,qBAAqB,EACnB,4DAA4D;gBAC5D,+CAA+C;YACjD,yBAAyB,EACvB,4DAA4D;gBAC5D,uCAAuC;YACzC,6BAA6B,EAC3B,4CAA4C;gBAC5C,qDAAqD;YACvD,gBAAgB,EAAE,wCAAwC;SAC3D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,oBAAoB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBACzC,kBAAkB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;iBACxC;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;KACrB;IACD,cAAc,EAAE,CAAC,EAAE,CAAC;IAEpB,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,OAAO;YACL,2DAA2D,CACzD,IAGqC;gBAErC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBACxD,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAChE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;oBACvD,wBAAwB;oBACxB,OAAO;iBACR;gBAED,MAAM,eAAe,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAClD,IAAI,eAAe,IAAI,IAAI,EAAE;oBAC3B,uCAAuC;oBACvC,OAAO;iBACR;gBAED,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC3C,MAAM,WAAW,GAAG,CAAC,KAAyB,EAAoB,EAAE;oBAClE,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC1C,MAAM,WAAW,GAAG,QAAQ,QAAQ,EAAE,CAAC;oBACvC,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;gBAC9C,CAAC,CAAC;gBAEF,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB,EAAE;oBACnE,kCAAkC;oBAElC,IAAI,OAAO,CAAC,kBAAkB,EAAE;wBAC9B,8BAA8B;wBAC9B,OAAO,OAAO,CAAC,MAAM,CAAC;4BACpB,IAAI;4BACJ,SAAS,EAAE,8BAA8B;4BACzC,GAAG,EAAE,WAAW;yBACjB,CAAC,CAAC;qBACJ;oBAED,8BAA8B;oBAC9B,MAAM,aAAa,GAAG,eAAe,CAAC;oBACtC,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI;wBACJ,SAAS,EAAE,sBAAsB;wBACjC,GAAG,CAAC,KAAK;4BACP,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC;4BACrC,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;4BACpD,MAAM,gBAAgB,GAAG,KAAK,aAAa,KAAK,CAAC;4BACjD,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE;gCAC/C,MAAM,gBAAgB,GAAG,UAAU,CAAC,cAAc,CAChD,SAAS,EACT,IAAI,CAAC,mBAAmB,CACxB,CAAC;gCACH,MAAM,gBAAgB,GAAG,UAAU,CAAC,aAAa,CAC/C,SAAS,EACT,IAAI,CAAC,mBAAmB,CACxB,CAAC;gCACH,OAAO,KAAK,CAAC,gBAAgB,CAC3B,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACtD,gBAAgB,CACjB,CAAC;6BACH;4BACD,OAAO,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;wBACxD,CAAC;qBACF,CAAC,CAAC;iBACJ;gBAED,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;oBAC3D,0BAA0B;oBAE1B,IAAI,OAAO,CAAC,kBAAkB,EAAE;wBAC9B,8BAA8B;wBAC9B,OAAO,OAAO,CAAC,MAAM,CAAC;4BACpB,IAAI;4BACJ,SAAS,EAAE,+BAA+B;4BAC1C,GAAG,EAAE,WAAW;yBACjB,CAAC,CAAC;qBACJ;oBAED,MAAM,UAAU,GAAG,eAAe,CAAC;oBAEnC,IAAI,aAAa,CAAC,UAAU,CAAC,EAAE;wBAC7B,8BAA8B;wBAC9B,OAAO,OAAO,CAAC,MAAM,CAAC;4BACpB,IAAI;4BACJ,SAAS,EAAE,2BAA2B;4BACtC,GAAG,CAAC,KAAK;gCACP,MAAM,WAAW,GAAG,UAAU,CAAC,QAAS,CAAC;gCACzC,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gCACxD,IAAI,iBAAiB,GAAG,GAAG,eAAe,GAAG,CAAC;gCAC9C,IAAI,eAAe,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE;oCAC5C,+CAA+C;oCAC/C,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;iCAC7C;gCACD,OAAO,KAAK,CAAC,WAAW,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;4BAC1D,CAAC;yBACF,CAAC,CAAC;qBACJ;oBAED,mCAAmC;oBACnC,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI;wBACJ,SAAS,EAAE,uBAAuB;wBAClC,GAAG,CAAC,KAAK;;4BACP,MAAM,WAAW,GAAG,UAAU,CAAC,QAAS,CAAC;4BACzC,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;4BACxD,IAAI,iBAAiB,GAAG,GAAG,eAAe,WAAW,CAAC;4BACtD,IAAI,eAAe,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE;gCAC5C,+CAA+C;gCAC/C,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;6BAC7C;4BACD,IAAI,CAAA,MAAA,UAAU,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,cAAc,EAAE;gCAC7D,2CAA2C;gCAC3C,mCAAmC;gCACnC,iBAAiB,GAAG,KAAK,iBAAiB,IAAI,CAAC;6BAChD;4BACD,OAAO,KAAK,CAAC,WAAW,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;wBAC1D,CAAC;qBACF,CAAC,CAAC;iBACJ;gBAED,sBAAsB;gBACtB,IAAI,OAAO,CAAC,kBAAkB,EAAE;oBAC9B,sDAAsD;oBACtD,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI;wBACJ,SAAS,EAAE,yBAAyB;wBACpC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,kBAAkB,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC;qBAC/D,CAAC,CAAC;iBACJ;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,iBAAiB;iBAC7B,CAAC,CAAC;YACL,CAAC;SACF,CAAC;QAEF;;;;;;;WAOG;QACH,SAAS,mBAAmB,CAAC,IAAmB;YAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAC5B,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CAAC,aAAa,CACrC,CAAC;YACF,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE;gBACrD,IAAI,IAAI,KAAK,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;oBAC9D,OAAO,IAAI,CAAC;iBACb;aACF;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE;gBACtD,iCAAiC;gBACjC,uBAAuB;gBACvB,OAAO,IAAI,CAAC;aACb;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;gBACpD,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;oBACzB,6BAA6B;oBAC7B,mDAAmD;oBACnD,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;iBACpC;aACF;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB,EAAE;gBACxD,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,EAAE;oBAC3D,uDAAuD;oBACvD,mDAAmD;oBACnD,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;iBACpC;aACF;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB,EAAE;gBAC1D,kCAAkC;gBAClC,2CAA2C;gBAC3C,IAAI,OAAO,CAAC,oBAAoB,EAAE;oBAChC,OAAO,IAAI,CAAC;iBACb;aACF;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;gBAClD,IAAI,MAAM,CAAC,QAAQ,KAAK,MAAM,EAAE;oBAC9B,iCAAiC;oBACjC,2CAA2C;oBAC3C,IAAI,OAAO,CAAC,kBAAkB,EAAE;wBAC9B,OAAO,IAAI,CAAC;qBACb;iBACF;aACF;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;gBAClD,6BAA6B;gBAC7B,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;aACpC;YAED,8BAA8B;YAC9B,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,oFAAoF;QACpF,SAAS,aAAa,CAAC,IAA8B;YACnD,6BAA6B;YAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAC3B,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CAAC,aAAa,CACrC,CAAC;YACF,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE;gBAChD,4CAA4C;gBAC5C,OAAO,KAAK,CAAC;aACd;YAED,wCAAwC;YACxC,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CACjC,KAAK,CAAC,MAAM,EACZ,IAAI,CAAC,iBAAiB,CAAC,aAAa,CACrC,CAAC;YACF,IACE,CAAC;gBACC,sBAAc,CAAC,mBAAmB;gBAClC,sBAAc,CAAC,kBAAkB;gBACjC,sBAAc,CAAC,uBAAuB;aACvC,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAC5B;gBACA,+BAA+B;gBAC/B,oCAAoC;gBACpC,OAAO,KAAK,CAAC;aACd;YAED,sCAAsC;YACtC,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBACpD,sCAAsC;gBACtC,OAAO,KAAK,CAAC;aACd;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;;;WAKG;QACH,SAAS,eAAe,CACtB,IAAyB,EACzB,UAAyC;YAEzC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAChC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,EAC9B,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAC9D,CAAC;YAEF,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;CACF,CAAC,CAAC"}