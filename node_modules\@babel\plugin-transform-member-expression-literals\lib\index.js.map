{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "visitor", "MemberExpression", "exit", "node", "prop", "property", "computed", "t", "isIdentifier", "isValidES3Identifier", "stringLiteral"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-member-expression-literals\",\n\n    visitor: {\n      MemberExpression: {\n        exit({ node }) {\n          const prop = node.property;\n          if (\n            !node.computed &&\n            t.isIdentifier(prop) &&\n            !t.isValidES3Identifier(prop.name)\n          ) {\n            // foo.default -> foo[\"default\"]\n            node.property = t.stringLiteral(prop.name);\n            node.computed = true;\n          }\n        },\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAyC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE1B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,sCAAsC;IAE5CC,OAAO,EAAE;MACPC,gBAAgB,EAAE;QAChBC,IAAIA,CAAC;UAAEC;QAAK,CAAC,EAAE;UACb,MAAMC,IAAI,GAAGD,IAAI,CAACE,QAAQ;UAC1B,IACE,CAACF,IAAI,CAACG,QAAQ,IACdC,WAAC,CAACC,YAAY,CAACJ,IAAI,CAAC,IACpB,CAACG,WAAC,CAACE,oBAAoB,CAACL,IAAI,CAACL,IAAI,CAAC,EAClC;YAEAI,IAAI,CAACE,QAAQ,GAAGE,WAAC,CAACG,aAAa,CAACN,IAAI,CAACL,IAAI,CAAC;YAC1CI,IAAI,CAACG,QAAQ,GAAG,IAAI;UACtB;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}