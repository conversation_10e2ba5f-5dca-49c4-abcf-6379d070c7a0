-- Drop existing table if it exists (be careful with this in production!)
-- DROP TABLE IF EXISTS blog_comments CASCADE;

-- Create blog_comments table
CREATE TABLE IF NOT EXISTS blog_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL,
    user_id UUID NOT NULL,
    parent_id UUID,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add parent_id column if it doesn't exist (do this FIRST)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'blog_comments' AND column_name = 'parent_id'
    ) THEN
        ALTER TABLE blog_comments ADD COLUMN parent_id UUID;
    END IF;
END $$;

-- Add foreign key constraints (do this AFTER ensuring columns exist)
DO $$ 
BEGIN
    -- Add foreign key for post_id if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'blog_comments_post_id_fkey'
    ) THEN
        ALTER TABLE blog_comments 
        ADD CONSTRAINT blog_comments_post_id_fkey 
        FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE;
    END IF;

    -- Add foreign key for user_id if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'blog_comments_user_id_fkey'
    ) THEN
        ALTER TABLE blog_comments 
        ADD CONSTRAINT blog_comments_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
    END IF;

    -- Add foreign key for parent_id if it doesn't exist (do this LAST)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'blog_comments_parent_id_fkey'
    ) THEN
        ALTER TABLE blog_comments 
        ADD CONSTRAINT blog_comments_parent_id_fkey 
        FOREIGN KEY (parent_id) REFERENCES blog_comments(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Create indexes for better performance (only if they don't exist)
CREATE INDEX IF NOT EXISTS idx_blog_comments_post_id ON blog_comments(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_user_id ON blog_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_parent_id ON blog_comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_created_at ON blog_comments(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE blog_comments ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Allow users to read comments" ON blog_comments;
DROP POLICY IF EXISTS "Allow users to insert comments" ON blog_comments;
DROP POLICY IF EXISTS "Allow users to update their own comments" ON blog_comments;
DROP POLICY IF EXISTS "Allow users to delete their own comments" ON blog_comments;
DROP POLICY IF EXISTS "Allow admins to delete any comment" ON blog_comments;

-- Create policies
-- Allow users to read all comments
CREATE POLICY "Allow users to read comments" ON blog_comments
    FOR SELECT USING (true);

-- Allow authenticated users to insert their own comments
CREATE POLICY "Allow users to insert comments" ON blog_comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Allow users to update their own comments
CREATE POLICY "Allow users to update their own comments" ON blog_comments
    FOR UPDATE USING (auth.uid() = user_id);

-- Allow users to delete their own comments
CREATE POLICY "Allow users to delete their own comments" ON blog_comments
    FOR DELETE USING (auth.uid() = user_id);

-- Allow admins to delete any comment
CREATE POLICY "Allow admins to delete any comment" ON blog_comments
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS update_blog_comments_updated_at ON blog_comments;

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_blog_comments_updated_at 
    BEFORE UPDATE ON blog_comments 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column(); 