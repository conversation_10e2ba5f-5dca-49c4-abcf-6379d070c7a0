"use strict";var t=require("@react-pdf-viewer/core");function e(t){var e=Object.create(null);return t&&Object.keys(t).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}})),e.default=t,Object.freeze(e)}var r=e(require("react")),n=function(){return r.createElement(t.Icon,{ignoreDirection:!0,size:16},r.createElement("path",{d:"M3.434,10.537c0.141-0.438,0.316-0.864,0.523-1.274\n            M3.069,14.425C3.023,14.053,3,13.679,3,13.305 c0-0.291,0.014-0.579,0.041-0.863\n            M4.389,18.111c-0.341-0.539-0.623-1.112-0.843-1.711\n            M7.163,20.9 c-0.543-0.345-1.048-0.747-1.506-1.2\n            M10.98,22.248c-0.65-0.074-1.29-0.218-1.909-0.431\n            M10,4.25h2 c4.987,0.015,9.017,4.069,9.003,9.055c-0.013,4.581-3.456,8.426-8.008,8.945\n            M13.5,1.75L10,4.25l3.5,2.5"}))},o=function(){return r.createElement(t.Icon,{ignoreDirection:!0,size:16},r.createElement("path",{d:"M20.566,10.537c-0.141-0.438-0.316-0.864-0.523-1.274\n            M20.931,14.425C20.977,14.053,21,13.679,21,13.305 c0-0.291-0.014-0.579-0.041-0.863\n            M19.611,18.111c0.341-0.539,0.624-1.114,0.843-1.713\n            M16.837,20.9 c0.543-0.345,1.048-0.747,1.506-1.2\n            M13.02,22.248c0.65-0.074,1.29-0.218,1.909-0.431\n            M14,4.25h-2 c-4.987,0.015-9.017,4.069-9.003,9.055c0.013,4.581,3.456,8.426,8.008,8.945\n            M10.5,1.75l3.5,2.5l-3.5,2.5"}))},c=function(){return c=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},c.apply(this,arguments)},a={left:0,top:8},i=function(e){var c=e.direction,i=e.onClick,u=r.useContext(t.LocalizationContext).l10n,l=u&&u.rotate?u.rotate.rotateBackward:"Rotate counterclockwise",d=u&&u.rotate?u.rotate.rotateForward:"Rotate clockwise",f=c===t.RotateDirection.Backward?l:d,k=c===t.RotateDirection.Backward?r.createElement(n,null):r.createElement(o,null);return r.createElement(t.Tooltip,{ariaControlsSuffix:"rotate",position:t.Position.BottomCenter,target:r.createElement(t.MinimalButton,{ariaLabel:f,testId:c===t.RotateDirection.Backward?"rotate__backward-button":"rotate__forward-button",onClick:i},k),content:function(){return f},offset:a})},u=function(t){var e=t.children,n=t.direction,o=t.store;return(e||function(t){return r.createElement(i,{direction:t.direction,onClick:t.onClick})})({direction:n,onClick:function(){var t=o.get("rotate");t&&t(n)}})},l=function(e){var c=e.direction,a=e.onClick,i=r.useContext(t.LocalizationContext).l10n,u=i&&i.rotate?i.rotate.rotateBackward:"Rotate counterclockwise",l=i&&i.rotate?i.rotate.rotateForward:"Rotate clockwise",d=c===t.RotateDirection.Backward?u:l,f=c===t.RotateDirection.Backward?r.createElement(n,null):r.createElement(o,null);return r.createElement(t.MenuItem,{icon:f,testId:c===t.RotateDirection.Backward?"rotate__backward-menu":"rotate__forward-menu",onClick:a},d)},d=function(t){var e=t.children,r=t.store;return e({onRotatePage:function(t,e){var n=r.get("rotatePage");n&&n(t,e)}})};exports.RotateBackwardIcon=n,exports.RotateForwardIcon=o,exports.rotatePlugin=function(){var e=r.useMemo((function(){return t.createStore()}),[]),n=function(t){return r.createElement(u,c({},t,{store:e}))};return{install:function(t){e.update("rotate",t.rotate),e.update("rotatePage",t.rotatePage)},Rotate:n,RotateBackwardButton:function(){return r.createElement(n,{direction:t.RotateDirection.Backward},(function(t){return r.createElement(i,c({},t))}))},RotateBackwardMenuItem:function(e){return r.createElement(n,{direction:t.RotateDirection.Backward},(function(t){return r.createElement(l,{direction:t.direction,onClick:function(){t.onClick(),e.onClick()}})}))},RotateForwardButton:function(){return r.createElement(n,{direction:t.RotateDirection.Forward},(function(t){return r.createElement(i,c({},t))}))},RotateForwardMenuItem:function(e){return r.createElement(n,{direction:t.RotateDirection.Forward},(function(t){return r.createElement(l,{direction:t.direction,onClick:function(){t.onClick(),e.onClick()}})}))},RotatePage:function(t){return r.createElement(d,c({},t,{store:e}))}}};
