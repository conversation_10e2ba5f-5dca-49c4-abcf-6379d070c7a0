{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Darsgah-e-ahlebait\\\\darsgah-e-ahlebait\\\\src\\\\Submit.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { supabase } from './supabaseClient';\nimport { useNavigate } from 'react-router-dom';\nimport { Helmet } from 'react-helmet';\nimport jsPDF from 'jspdf';\nimport html2canvas from 'html2canvas';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Submit() {\n  _s();\n  // Get user from localStorage\n  const user = JSON.parse(localStorage.getItem('user') || 'null');\n  const [form, setForm] = useState({\n    // username will be set from user\n    title: '',\n    authors: '',\n    abstract: '',\n    keywords: '',\n    mainContent: '',\n    conclusion: '',\n    references: '',\n    organization: '',\n    country: '',\n    journal: ''\n  });\n  const [step, setStep] = useState(1); // 1: form, 2: preview, 3: confirmation\n  const [generatedPdf, setGeneratedPdf] = useState(null);\n  const [pdfBlob, setPdfBlob] = useState(null);\n  const [submitted, setSubmitted] = useState(false);\n  const [submitting, setSubmitting] = useState(false);\n  const [error, setError] = useState('');\n  const pdfRef = useRef();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setForm({\n      ...form,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  // Generate PDF from form data\n  const generatePDF = async () => {\n    const pdf = new jsPDF('p', 'mm', 'a4');\n    const pageWidth = pdf.internal.pageSize.getWidth();\n    const pageHeight = pdf.internal.pageSize.getHeight();\n    const margin = 25.4; // 1 inch margin in mm\n    const contentWidth = pageWidth - 2 * margin;\n    let yPosition = margin;\n\n    // Add journal logo/header (placeholder for now)\n    pdf.setFontSize(10);\n    pdf.text('Darsgah-e-Ahlebait Journal', pageWidth / 2, 15, {\n      align: 'center'\n    });\n    yPosition += 20;\n\n    // Title - Bold, 18pt, Centered\n    pdf.setFont('times', 'bold');\n    pdf.setFontSize(18);\n    const titleLines = pdf.splitTextToSize(form.title, contentWidth);\n    titleLines.forEach(line => {\n      pdf.text(line, pageWidth / 2, yPosition, {\n        align: 'center'\n      });\n      yPosition += 7;\n    });\n    yPosition += 10;\n\n    // Authors - Italic, 12pt, Centered\n    pdf.setFont('times', 'italic');\n    pdf.setFontSize(12);\n    const authorLines = pdf.splitTextToSize(form.authors, contentWidth);\n    authorLines.forEach(line => {\n      pdf.text(line, pageWidth / 2, yPosition, {\n        align: 'center'\n      });\n      yPosition += 5;\n    });\n    yPosition += 15;\n\n    // Abstract section\n    pdf.setFont('times', 'bold');\n    pdf.setFontSize(12);\n    pdf.text('Abstract', margin, yPosition);\n    yPosition += 8;\n    pdf.setFont('times', 'normal');\n    const abstractLines = pdf.splitTextToSize(form.abstract, contentWidth);\n    abstractLines.forEach(line => {\n      if (yPosition > pageHeight - margin) {\n        pdf.addPage();\n        yPosition = margin;\n      }\n      pdf.text(line, margin, yPosition, {\n        align: 'justify'\n      });\n      yPosition += 5;\n    });\n    yPosition += 10;\n\n    // Keywords section\n    pdf.setFont('times', 'bold');\n    pdf.text('Keywords', margin, yPosition);\n    yPosition += 8;\n    pdf.setFont('times', 'normal');\n    const keywordLines = pdf.splitTextToSize(form.keywords, contentWidth);\n    keywordLines.forEach(line => {\n      if (yPosition > pageHeight - margin) {\n        pdf.addPage();\n        yPosition = margin;\n      }\n      pdf.text(line, margin, yPosition, {\n        align: 'justify'\n      });\n      yPosition += 5;\n    });\n    yPosition += 15;\n\n    // Main Content section with 1.5 line spacing\n    pdf.setFont('times', 'bold');\n    pdf.text('Main Content', margin, yPosition);\n    yPosition += 8;\n    pdf.setFont('times', 'normal');\n    const contentLines = pdf.splitTextToSize(form.mainContent, contentWidth);\n    contentLines.forEach(line => {\n      if (yPosition > pageHeight - margin) {\n        pdf.addPage();\n        yPosition = margin;\n      }\n      pdf.text(line, margin, yPosition, {\n        align: 'justify'\n      });\n      yPosition += 7.5; // 1.5 line spacing\n    });\n    yPosition += 15;\n\n    // Conclusion section\n    pdf.setFont('times', 'bold');\n    pdf.text('Conclusion', margin, yPosition);\n    yPosition += 8;\n    pdf.setFont('times', 'normal');\n    const conclusionLines = pdf.splitTextToSize(form.conclusion, contentWidth);\n    conclusionLines.forEach(line => {\n      if (yPosition > pageHeight - margin) {\n        pdf.addPage();\n        yPosition = margin;\n      }\n      pdf.text(line, margin, yPosition, {\n        align: 'justify'\n      });\n      yPosition += 5;\n    });\n    yPosition += 15;\n\n    // References section\n    pdf.setFont('times', 'bold');\n    pdf.text('References', margin, yPosition);\n    yPosition += 8;\n    pdf.setFont('times', 'normal');\n    const refLines = form.references.split('\\n');\n    refLines.forEach((ref, index) => {\n      if (ref.trim()) {\n        if (yPosition > pageHeight - margin) {\n          pdf.addPage();\n          yPosition = margin;\n        }\n        pdf.text(`${index + 1}. ${ref}`, margin, yPosition);\n        yPosition += 5;\n      }\n    });\n\n    // Add page numbers\n    const pageCount = pdf.internal.getNumberOfPages();\n    for (let i = 1; i <= pageCount; i++) {\n      pdf.setPage(i);\n      pdf.setFontSize(10);\n      pdf.text(`${i}`, pageWidth / 2, pageHeight - 10, {\n        align: 'center'\n      });\n    }\n    return pdf;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setSubmitting(true);\n    setError('');\n    let pdf_url = '';\n    if (!pdfFile) {\n      setError('Please upload a PDF file.');\n      setSubmitting(false);\n      return;\n    }\n    if (!user) {\n      setError('You must be logged in to submit a publication.');\n      setSubmitting(false);\n      return;\n    }\n    // Upload PDF to Supabase Storage\n    const fileExt = pdfFile.name.split('.').pop();\n    const fileName = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExt}`;\n    const {\n      data,\n      error: uploadError\n    } = await supabase.storage.from('pdf').upload(fileName, pdfFile);\n    if (uploadError) {\n      setError('Failed to upload PDF.');\n      setSubmitting(false);\n      return;\n    }\n    // Get public URL\n    const {\n      data: urlData\n    } = supabase.storage.from('pdf').getPublicUrl(fileName);\n    pdf_url = urlData.publicUrl;\n    // Insert into request table\n    const {\n      error: insertError\n    } = await supabase.from('request').insert([{\n      ...form,\n      username: user.username || user.full_name || user.email,\n      email: user.email,\n      pdf_url\n    }]);\n    if (insertError) {\n      setError('Failed to submit publication. ' + (insertError.message || ''));\n      setSubmitting(false);\n      return;\n    }\n    setSubmitted(true);\n    setSubmitting(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"submit-page\",\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Submit - darsgah-e-ahlebait\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Submit your research paper to darsgah-e-ahlebait journals.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-9\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card shadow-sm border-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"mb-4 text-center\",\n              children: \"Submit a Publication\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), submitted ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"alert alert-success text-center\",\n              children: \"Your submission has been received!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this) : !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"alert alert-danger text-center my-5\",\n              children: \"You must be logged in to submit a publication.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSubmit,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Title\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control\",\n                    name: \"title\",\n                    value: form.title,\n                    onChange: handleChange,\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Organization\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control\",\n                    name: \"organization\",\n                    value: form.organization,\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Country\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control\",\n                    name: \"country\",\n                    value: form.country,\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Journal\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    className: \"form-control\",\n                    name: \"journal\",\n                    value: form.journal,\n                    onChange: handleChange,\n                    required: true,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Journal\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"JBS\",\n                      children: \"JBS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"JNS\",\n                      children: \"JNS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Abstract\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  className: \"form-control\",\n                  name: \"abstract\",\n                  rows: \"3\",\n                  value: form.abstract,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Authors\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control\",\n                    name: \"authors\",\n                    value: form.authors,\n                    onChange: handleChange,\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Keywords\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control\",\n                    name: \"keywords\",\n                    value: form.keywords,\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"PDF File\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  className: \"form-control\",\n                  accept: \"application/pdf\",\n                  onChange: handleFileChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 21\n              }, this), submitting && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"alert alert-info text-center\",\n                children: \"Submitting...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 36\n              }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"alert alert-danger text-center\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 31\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-grid\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"btn btn-success\",\n                  children: \"Submit Publication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n}\n_s(Submit, \"L553YBHgwz0p9bCcXQyTMPwCtp4=\", false, function () {\n  return [useNavigate];\n});\n_c = Submit;\nexport default Submit;\nvar _c;\n$RefreshReg$(_c, \"Submit\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "supabase", "useNavigate", "<PERSON><PERSON><PERSON>", "jsPDF", "html2canvas", "jsxDEV", "_jsxDEV", "Submit", "_s", "user", "JSON", "parse", "localStorage", "getItem", "form", "setForm", "title", "authors", "abstract", "keywords", "mainContent", "conclusion", "references", "organization", "country", "journal", "step", "setStep", "generatedPdf", "setGeneratedPdf", "pdfBlob", "setPdfBlob", "submitted", "setSubmitted", "submitting", "setSubmitting", "error", "setError", "pdfRef", "navigate", "handleChange", "e", "target", "name", "value", "generatePDF", "pdf", "pageWidth", "internal", "pageSize", "getWidth", "pageHeight", "getHeight", "margin", "contentWidth", "yPosition", "setFontSize", "text", "align", "setFont", "titleLines", "splitTextToSize", "for<PERSON>ach", "line", "authorLines", "abstractLines", "addPage", "keywordLines", "contentLines", "conclusionLines", "refLines", "split", "ref", "index", "trim", "pageCount", "getNumberOfPages", "i", "setPage", "handleSubmit", "preventDefault", "pdf_url", "pdfFile", "fileExt", "pop", "fileName", "Date", "now", "Math", "random", "toString", "substr", "data", "uploadError", "storage", "from", "upload", "urlData", "getPublicUrl", "publicUrl", "insertError", "insert", "username", "full_name", "email", "message", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "content", "onSubmit", "type", "onChange", "required", "rows", "accept", "handleFileChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Darsgah-e-ahlebait/darsgah-e-ahlebait/src/Submit.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\r\nimport { supabase } from './supabaseClient';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { Helmet } from 'react-helmet';\r\nimport jsPDF from 'jspdf';\r\nimport html2canvas from 'html2canvas';\r\n\r\nfunction Submit() {\r\n  // Get user from localStorage\r\n  const user = JSON.parse(localStorage.getItem('user') || 'null');\r\n  const [form, setForm] = useState({\r\n    // username will be set from user\r\n    title: '',\r\n    authors: '',\r\n    abstract: '',\r\n    keywords: '',\r\n    mainContent: '',\r\n    conclusion: '',\r\n    references: '',\r\n    organization: '',\r\n    country: '',\r\n    journal: '',\r\n  });\r\n  const [step, setStep] = useState(1); // 1: form, 2: preview, 3: confirmation\r\n  const [generatedPdf, setGeneratedPdf] = useState(null);\r\n  const [pdfBlob, setPdfBlob] = useState(null);\r\n  const [submitted, setSubmitted] = useState(false);\r\n  const [submitting, setSubmitting] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const pdfRef = useRef();\r\n\r\n  const navigate = useNavigate();\r\n\r\n  const handleChange = e => {\r\n    setForm({ ...form, [e.target.name]: e.target.value });\r\n  };\r\n\r\n  // Generate PDF from form data\r\n  const generatePDF = async () => {\r\n    const pdf = new jsPDF('p', 'mm', 'a4');\r\n    const pageWidth = pdf.internal.pageSize.getWidth();\r\n    const pageHeight = pdf.internal.pageSize.getHeight();\r\n    const margin = 25.4; // 1 inch margin in mm\r\n    const contentWidth = pageWidth - (2 * margin);\r\n    let yPosition = margin;\r\n\r\n    // Add journal logo/header (placeholder for now)\r\n    pdf.setFontSize(10);\r\n    pdf.text('Darsgah-e-Ahlebait Journal', pageWidth / 2, 15, { align: 'center' });\r\n    yPosition += 20;\r\n\r\n    // Title - Bold, 18pt, Centered\r\n    pdf.setFont('times', 'bold');\r\n    pdf.setFontSize(18);\r\n    const titleLines = pdf.splitTextToSize(form.title, contentWidth);\r\n    titleLines.forEach(line => {\r\n      pdf.text(line, pageWidth / 2, yPosition, { align: 'center' });\r\n      yPosition += 7;\r\n    });\r\n    yPosition += 10;\r\n\r\n    // Authors - Italic, 12pt, Centered\r\n    pdf.setFont('times', 'italic');\r\n    pdf.setFontSize(12);\r\n    const authorLines = pdf.splitTextToSize(form.authors, contentWidth);\r\n    authorLines.forEach(line => {\r\n      pdf.text(line, pageWidth / 2, yPosition, { align: 'center' });\r\n      yPosition += 5;\r\n    });\r\n    yPosition += 15;\r\n\r\n    // Abstract section\r\n    pdf.setFont('times', 'bold');\r\n    pdf.setFontSize(12);\r\n    pdf.text('Abstract', margin, yPosition);\r\n    yPosition += 8;\r\n\r\n    pdf.setFont('times', 'normal');\r\n    const abstractLines = pdf.splitTextToSize(form.abstract, contentWidth);\r\n    abstractLines.forEach(line => {\r\n      if (yPosition > pageHeight - margin) {\r\n        pdf.addPage();\r\n        yPosition = margin;\r\n      }\r\n      pdf.text(line, margin, yPosition, { align: 'justify' });\r\n      yPosition += 5;\r\n    });\r\n    yPosition += 10;\r\n\r\n    // Keywords section\r\n    pdf.setFont('times', 'bold');\r\n    pdf.text('Keywords', margin, yPosition);\r\n    yPosition += 8;\r\n\r\n    pdf.setFont('times', 'normal');\r\n    const keywordLines = pdf.splitTextToSize(form.keywords, contentWidth);\r\n    keywordLines.forEach(line => {\r\n      if (yPosition > pageHeight - margin) {\r\n        pdf.addPage();\r\n        yPosition = margin;\r\n      }\r\n      pdf.text(line, margin, yPosition, { align: 'justify' });\r\n      yPosition += 5;\r\n    });\r\n    yPosition += 15;\r\n\r\n    // Main Content section with 1.5 line spacing\r\n    pdf.setFont('times', 'bold');\r\n    pdf.text('Main Content', margin, yPosition);\r\n    yPosition += 8;\r\n\r\n    pdf.setFont('times', 'normal');\r\n    const contentLines = pdf.splitTextToSize(form.mainContent, contentWidth);\r\n    contentLines.forEach(line => {\r\n      if (yPosition > pageHeight - margin) {\r\n        pdf.addPage();\r\n        yPosition = margin;\r\n      }\r\n      pdf.text(line, margin, yPosition, { align: 'justify' });\r\n      yPosition += 7.5; // 1.5 line spacing\r\n    });\r\n    yPosition += 15;\r\n\r\n    // Conclusion section\r\n    pdf.setFont('times', 'bold');\r\n    pdf.text('Conclusion', margin, yPosition);\r\n    yPosition += 8;\r\n\r\n    pdf.setFont('times', 'normal');\r\n    const conclusionLines = pdf.splitTextToSize(form.conclusion, contentWidth);\r\n    conclusionLines.forEach(line => {\r\n      if (yPosition > pageHeight - margin) {\r\n        pdf.addPage();\r\n        yPosition = margin;\r\n      }\r\n      pdf.text(line, margin, yPosition, { align: 'justify' });\r\n      yPosition += 5;\r\n    });\r\n    yPosition += 15;\r\n\r\n    // References section\r\n    pdf.setFont('times', 'bold');\r\n    pdf.text('References', margin, yPosition);\r\n    yPosition += 8;\r\n\r\n    pdf.setFont('times', 'normal');\r\n    const refLines = form.references.split('\\n');\r\n    refLines.forEach((ref, index) => {\r\n      if (ref.trim()) {\r\n        if (yPosition > pageHeight - margin) {\r\n          pdf.addPage();\r\n          yPosition = margin;\r\n        }\r\n        pdf.text(`${index + 1}. ${ref}`, margin, yPosition);\r\n        yPosition += 5;\r\n      }\r\n    });\r\n\r\n    // Add page numbers\r\n    const pageCount = pdf.internal.getNumberOfPages();\r\n    for (let i = 1; i <= pageCount; i++) {\r\n      pdf.setPage(i);\r\n      pdf.setFontSize(10);\r\n      pdf.text(`${i}`, pageWidth / 2, pageHeight - 10, { align: 'center' });\r\n    }\r\n\r\n    return pdf;\r\n  };\r\n\r\n  const handleSubmit = async e => {\r\n    e.preventDefault();\r\n    setSubmitting(true);\r\n    setError('');\r\n    let pdf_url = '';\r\n    if (!pdfFile) {\r\n      setError('Please upload a PDF file.');\r\n      setSubmitting(false);\r\n      return;\r\n    }\r\n    if (!user) {\r\n      setError('You must be logged in to submit a publication.');\r\n      setSubmitting(false);\r\n      return;\r\n    }\r\n    // Upload PDF to Supabase Storage\r\n    const fileExt = pdfFile.name.split('.').pop();\r\n    const fileName = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExt}`;\r\n    const { data, error: uploadError } = await supabase.storage.from('pdf').upload(fileName, pdfFile);\r\n    if (uploadError) {\r\n      setError('Failed to upload PDF.');\r\n      setSubmitting(false);\r\n      return;\r\n    }\r\n    // Get public URL\r\n    const { data: urlData } = supabase.storage.from('pdf').getPublicUrl(fileName);\r\n    pdf_url = urlData.publicUrl;\r\n    // Insert into request table\r\n    const { error: insertError } = await supabase.from('request').insert([\r\n      { ...form, username: user.username || user.full_name || user.email, email: user.email, pdf_url }\r\n    ]);\r\n    if (insertError) {\r\n      setError('Failed to submit publication. ' + (insertError.message || ''));\r\n      setSubmitting(false);\r\n      return;\r\n    }\r\n    setSubmitted(true);\r\n    setSubmitting(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"submit-page\">\r\n      <Helmet>\r\n        <title>Submit - darsgah-e-ahlebait</title>\r\n        <meta name=\"description\" content=\"Submit your research paper to darsgah-e-ahlebait journals.\" />\r\n      </Helmet>\r\n      <div className=\"row justify-content-center\">\r\n        <div className=\"col-md-9\">\r\n          <div className=\"card shadow-sm border-0\">\r\n            <div className=\"card-body\">\r\n              <h2 className=\"mb-4 text-center\">Submit a Publication</h2>\r\n              {submitted ? (\r\n                <div className=\"alert alert-success text-center\">Your submission has been received!</div>\r\n              ) : (\r\n                !user ? (\r\n                  <div className=\"alert alert-danger text-center my-5\">You must be logged in to submit a publication.</div>\r\n                ) : (\r\n                  <form onSubmit={handleSubmit}>\r\n                    <div className=\"row\">\r\n                      <div className=\"col-md-6 mb-3\">\r\n                        <label className=\"form-label\">Title</label>\r\n                        <input type=\"text\" className=\"form-control\" name=\"title\" value={form.title} onChange={handleChange} required />\r\n                      </div>\r\n                      <div className=\"col-md-6 mb-3\">\r\n                        <label className=\"form-label\">Organization</label>\r\n                        <input type=\"text\" className=\"form-control\" name=\"organization\" value={form.organization} onChange={handleChange} />\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"row\">\r\n                      <div className=\"col-md-6 mb-3\">\r\n                        <label className=\"form-label\">Country</label>\r\n                        <input type=\"text\" className=\"form-control\" name=\"country\" value={form.country} onChange={handleChange} />\r\n                      </div>\r\n                      <div className=\"col-md-6 mb-3\">\r\n                        <label className=\"form-label\">Journal</label>\r\n                        <select className=\"form-control\" name=\"journal\" value={form.journal} onChange={handleChange} required>\r\n                          <option value=\"\">Select Journal</option>\r\n                          <option value=\"JBS\">JBS</option>\r\n                          <option value=\"JNS\">JNS</option>\r\n                        </select>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"mb-3\">\r\n                      <label className=\"form-label\">Abstract</label>\r\n                      <textarea className=\"form-control\" name=\"abstract\" rows=\"3\" value={form.abstract} onChange={handleChange} required></textarea>\r\n                    </div>\r\n                    <div className=\"row\">\r\n                      <div className=\"col-md-6 mb-3\">\r\n                        <label className=\"form-label\">Authors</label>\r\n                        <input type=\"text\" className=\"form-control\" name=\"authors\" value={form.authors} onChange={handleChange} required />\r\n                      </div>\r\n                      <div className=\"col-md-6 mb-3\">\r\n                        <label className=\"form-label\">Keywords</label>\r\n                        <input type=\"text\" className=\"form-control\" name=\"keywords\" value={form.keywords} onChange={handleChange} />\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"mb-3\">\r\n                      <label className=\"form-label\">PDF File</label>\r\n                      <input type=\"file\" className=\"form-control\" accept=\"application/pdf\" onChange={handleFileChange} required />\r\n                    </div>\r\n                    {submitting && <div className=\"alert alert-info text-center\">Submitting...</div>}\r\n                    {error && <div className=\"alert alert-danger text-center\">{error}</div>}\r\n                    <div className=\"d-grid\">\r\n                      <button type=\"submit\" className=\"btn btn-success\">Submit Publication</button>\r\n                    </div>\r\n                  </form>\r\n                )\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Submit; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,cAAc;AACrC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB;EACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC;EAC/D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAC;IAC/B;IACAkB,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMwC,MAAM,GAAGvC,MAAM,CAAC,CAAC;EAEvB,MAAMwC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAE9B,MAAMuC,YAAY,GAAGC,CAAC,IAAI;IACxB1B,OAAO,CAAC;MAAE,GAAGD,IAAI;MAAE,CAAC2B,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAMC,GAAG,GAAG,IAAI3C,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;IACtC,MAAM4C,SAAS,GAAGD,GAAG,CAACE,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,CAAC;IAClD,MAAMC,UAAU,GAAGL,GAAG,CAACE,QAAQ,CAACC,QAAQ,CAACG,SAAS,CAAC,CAAC;IACpD,MAAMC,MAAM,GAAG,IAAI,CAAC,CAAC;IACrB,MAAMC,YAAY,GAAGP,SAAS,GAAI,CAAC,GAAGM,MAAO;IAC7C,IAAIE,SAAS,GAAGF,MAAM;;IAEtB;IACAP,GAAG,CAACU,WAAW,CAAC,EAAE,CAAC;IACnBV,GAAG,CAACW,IAAI,CAAC,4BAA4B,EAAEV,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE;MAAEW,KAAK,EAAE;IAAS,CAAC,CAAC;IAC9EH,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IAC5Bb,GAAG,CAACU,WAAW,CAAC,EAAE,CAAC;IACnB,MAAMI,UAAU,GAAGd,GAAG,CAACe,eAAe,CAAC/C,IAAI,CAACE,KAAK,EAAEsC,YAAY,CAAC;IAChEM,UAAU,CAACE,OAAO,CAACC,IAAI,IAAI;MACzBjB,GAAG,CAACW,IAAI,CAACM,IAAI,EAAEhB,SAAS,GAAG,CAAC,EAAEQ,SAAS,EAAE;QAAEG,KAAK,EAAE;MAAS,CAAC,CAAC;MAC7DH,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC9Bb,GAAG,CAACU,WAAW,CAAC,EAAE,CAAC;IACnB,MAAMQ,WAAW,GAAGlB,GAAG,CAACe,eAAe,CAAC/C,IAAI,CAACG,OAAO,EAAEqC,YAAY,CAAC;IACnEU,WAAW,CAACF,OAAO,CAACC,IAAI,IAAI;MAC1BjB,GAAG,CAACW,IAAI,CAACM,IAAI,EAAEhB,SAAS,GAAG,CAAC,EAAEQ,SAAS,EAAE;QAAEG,KAAK,EAAE;MAAS,CAAC,CAAC;MAC7DH,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IAC5Bb,GAAG,CAACU,WAAW,CAAC,EAAE,CAAC;IACnBV,GAAG,CAACW,IAAI,CAAC,UAAU,EAAEJ,MAAM,EAAEE,SAAS,CAAC;IACvCA,SAAS,IAAI,CAAC;IAEdT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC9B,MAAMM,aAAa,GAAGnB,GAAG,CAACe,eAAe,CAAC/C,IAAI,CAACI,QAAQ,EAAEoC,YAAY,CAAC;IACtEW,aAAa,CAACH,OAAO,CAACC,IAAI,IAAI;MAC5B,IAAIR,SAAS,GAAGJ,UAAU,GAAGE,MAAM,EAAE;QACnCP,GAAG,CAACoB,OAAO,CAAC,CAAC;QACbX,SAAS,GAAGF,MAAM;MACpB;MACAP,GAAG,CAACW,IAAI,CAACM,IAAI,EAAEV,MAAM,EAAEE,SAAS,EAAE;QAAEG,KAAK,EAAE;MAAU,CAAC,CAAC;MACvDH,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IAC5Bb,GAAG,CAACW,IAAI,CAAC,UAAU,EAAEJ,MAAM,EAAEE,SAAS,CAAC;IACvCA,SAAS,IAAI,CAAC;IAEdT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC9B,MAAMQ,YAAY,GAAGrB,GAAG,CAACe,eAAe,CAAC/C,IAAI,CAACK,QAAQ,EAAEmC,YAAY,CAAC;IACrEa,YAAY,CAACL,OAAO,CAACC,IAAI,IAAI;MAC3B,IAAIR,SAAS,GAAGJ,UAAU,GAAGE,MAAM,EAAE;QACnCP,GAAG,CAACoB,OAAO,CAAC,CAAC;QACbX,SAAS,GAAGF,MAAM;MACpB;MACAP,GAAG,CAACW,IAAI,CAACM,IAAI,EAAEV,MAAM,EAAEE,SAAS,EAAE;QAAEG,KAAK,EAAE;MAAU,CAAC,CAAC;MACvDH,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IAC5Bb,GAAG,CAACW,IAAI,CAAC,cAAc,EAAEJ,MAAM,EAAEE,SAAS,CAAC;IAC3CA,SAAS,IAAI,CAAC;IAEdT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC9B,MAAMS,YAAY,GAAGtB,GAAG,CAACe,eAAe,CAAC/C,IAAI,CAACM,WAAW,EAAEkC,YAAY,CAAC;IACxEc,YAAY,CAACN,OAAO,CAACC,IAAI,IAAI;MAC3B,IAAIR,SAAS,GAAGJ,UAAU,GAAGE,MAAM,EAAE;QACnCP,GAAG,CAACoB,OAAO,CAAC,CAAC;QACbX,SAAS,GAAGF,MAAM;MACpB;MACAP,GAAG,CAACW,IAAI,CAACM,IAAI,EAAEV,MAAM,EAAEE,SAAS,EAAE;QAAEG,KAAK,EAAE;MAAU,CAAC,CAAC;MACvDH,SAAS,IAAI,GAAG,CAAC,CAAC;IACpB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IAC5Bb,GAAG,CAACW,IAAI,CAAC,YAAY,EAAEJ,MAAM,EAAEE,SAAS,CAAC;IACzCA,SAAS,IAAI,CAAC;IAEdT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC9B,MAAMU,eAAe,GAAGvB,GAAG,CAACe,eAAe,CAAC/C,IAAI,CAACO,UAAU,EAAEiC,YAAY,CAAC;IAC1Ee,eAAe,CAACP,OAAO,CAACC,IAAI,IAAI;MAC9B,IAAIR,SAAS,GAAGJ,UAAU,GAAGE,MAAM,EAAE;QACnCP,GAAG,CAACoB,OAAO,CAAC,CAAC;QACbX,SAAS,GAAGF,MAAM;MACpB;MACAP,GAAG,CAACW,IAAI,CAACM,IAAI,EAAEV,MAAM,EAAEE,SAAS,EAAE;QAAEG,KAAK,EAAE;MAAU,CAAC,CAAC;MACvDH,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IAC5Bb,GAAG,CAACW,IAAI,CAAC,YAAY,EAAEJ,MAAM,EAAEE,SAAS,CAAC;IACzCA,SAAS,IAAI,CAAC;IAEdT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC9B,MAAMW,QAAQ,GAAGxD,IAAI,CAACQ,UAAU,CAACiD,KAAK,CAAC,IAAI,CAAC;IAC5CD,QAAQ,CAACR,OAAO,CAAC,CAACU,GAAG,EAAEC,KAAK,KAAK;MAC/B,IAAID,GAAG,CAACE,IAAI,CAAC,CAAC,EAAE;QACd,IAAInB,SAAS,GAAGJ,UAAU,GAAGE,MAAM,EAAE;UACnCP,GAAG,CAACoB,OAAO,CAAC,CAAC;UACbX,SAAS,GAAGF,MAAM;QACpB;QACAP,GAAG,CAACW,IAAI,CAAC,GAAGgB,KAAK,GAAG,CAAC,KAAKD,GAAG,EAAE,EAAEnB,MAAM,EAAEE,SAAS,CAAC;QACnDA,SAAS,IAAI,CAAC;MAChB;IACF,CAAC,CAAC;;IAEF;IACA,MAAMoB,SAAS,GAAG7B,GAAG,CAACE,QAAQ,CAAC4B,gBAAgB,CAAC,CAAC;IACjD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,SAAS,EAAEE,CAAC,EAAE,EAAE;MACnC/B,GAAG,CAACgC,OAAO,CAACD,CAAC,CAAC;MACd/B,GAAG,CAACU,WAAW,CAAC,EAAE,CAAC;MACnBV,GAAG,CAACW,IAAI,CAAC,GAAGoB,CAAC,EAAE,EAAE9B,SAAS,GAAG,CAAC,EAAEI,UAAU,GAAG,EAAE,EAAE;QAAEO,KAAK,EAAE;MAAS,CAAC,CAAC;IACvE;IAEA,OAAOZ,GAAG;EACZ,CAAC;EAED,MAAMiC,YAAY,GAAG,MAAMtC,CAAC,IAAI;IAC9BA,CAAC,CAACuC,cAAc,CAAC,CAAC;IAClB7C,aAAa,CAAC,IAAI,CAAC;IACnBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI4C,OAAO,GAAG,EAAE;IAChB,IAAI,CAACC,OAAO,EAAE;MACZ7C,QAAQ,CAAC,2BAA2B,CAAC;MACrCF,aAAa,CAAC,KAAK,CAAC;MACpB;IACF;IACA,IAAI,CAAC1B,IAAI,EAAE;MACT4B,QAAQ,CAAC,gDAAgD,CAAC;MAC1DF,aAAa,CAAC,KAAK,CAAC;MACpB;IACF;IACA;IACA,MAAMgD,OAAO,GAAGD,OAAO,CAACvC,IAAI,CAAC4B,KAAK,CAAC,GAAG,CAAC,CAACa,GAAG,CAAC,CAAC;IAC7C,MAAMC,QAAQ,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIR,OAAO,EAAE;IACtF,MAAM;MAAES,IAAI;MAAExD,KAAK,EAAEyD;IAAY,CAAC,GAAG,MAAM7F,QAAQ,CAAC8F,OAAO,CAACC,IAAI,CAAC,KAAK,CAAC,CAACC,MAAM,CAACX,QAAQ,EAAEH,OAAO,CAAC;IACjG,IAAIW,WAAW,EAAE;MACfxD,QAAQ,CAAC,uBAAuB,CAAC;MACjCF,aAAa,CAAC,KAAK,CAAC;MACpB;IACF;IACA;IACA,MAAM;MAAEyD,IAAI,EAAEK;IAAQ,CAAC,GAAGjG,QAAQ,CAAC8F,OAAO,CAACC,IAAI,CAAC,KAAK,CAAC,CAACG,YAAY,CAACb,QAAQ,CAAC;IAC7EJ,OAAO,GAAGgB,OAAO,CAACE,SAAS;IAC3B;IACA,MAAM;MAAE/D,KAAK,EAAEgE;IAAY,CAAC,GAAG,MAAMpG,QAAQ,CAAC+F,IAAI,CAAC,SAAS,CAAC,CAACM,MAAM,CAAC,CACnE;MAAE,GAAGvF,IAAI;MAAEwF,QAAQ,EAAE7F,IAAI,CAAC6F,QAAQ,IAAI7F,IAAI,CAAC8F,SAAS,IAAI9F,IAAI,CAAC+F,KAAK;MAAEA,KAAK,EAAE/F,IAAI,CAAC+F,KAAK;MAAEvB;IAAQ,CAAC,CACjG,CAAC;IACF,IAAImB,WAAW,EAAE;MACf/D,QAAQ,CAAC,gCAAgC,IAAI+D,WAAW,CAACK,OAAO,IAAI,EAAE,CAAC,CAAC;MACxEtE,aAAa,CAAC,KAAK,CAAC;MACpB;IACF;IACAF,YAAY,CAAC,IAAI,CAAC;IAClBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,oBACE7B,OAAA;IAAKoG,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BrG,OAAA,CAACJ,MAAM;MAAAyG,QAAA,gBACLrG,OAAA;QAAAqG,QAAA,EAAO;MAA2B;QAAAtB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1CxG,OAAA;QAAMqC,IAAI,EAAC,aAAa;QAACoE,OAAO,EAAC;MAA4D;QAAA1B,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAzB,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1F,CAAC,eACTxG,OAAA;MAAKoG,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzCrG,OAAA;QAAKoG,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBrG,OAAA;UAAKoG,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtCrG,OAAA;YAAKoG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrG,OAAA;cAAIoG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAoB;cAAAtB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACzD9E,SAAS,gBACR1B,OAAA;cAAKoG,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAkC;cAAAtB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEzF,CAACrG,IAAI,gBACHH,OAAA;cAAKoG,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAA8C;cAAAtB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAEzGxG,OAAA;cAAM0G,QAAQ,EAAEjC,YAAa;cAAA4B,QAAA,gBAC3BrG,OAAA;gBAAKoG,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBrG,OAAA;kBAAKoG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BrG,OAAA;oBAAOoG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAK;oBAAAtB,QAAA,EAAAuB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3CxG,OAAA;oBAAO2G,IAAI,EAAC,MAAM;oBAACP,SAAS,EAAC,cAAc;oBAAC/D,IAAI,EAAC,OAAO;oBAACC,KAAK,EAAE9B,IAAI,CAACE,KAAM;oBAACkG,QAAQ,EAAE1E,YAAa;oBAAC2E,QAAQ;kBAAA;oBAAA9B,QAAA,EAAAuB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAzB,QAAA,EAAAuB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5G,CAAC,eACNxG,OAAA;kBAAKoG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BrG,OAAA;oBAAOoG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAY;oBAAAtB,QAAA,EAAAuB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClDxG,OAAA;oBAAO2G,IAAI,EAAC,MAAM;oBAACP,SAAS,EAAC,cAAc;oBAAC/D,IAAI,EAAC,cAAc;oBAACC,KAAK,EAAE9B,IAAI,CAACS,YAAa;oBAAC2F,QAAQ,EAAE1E;kBAAa;oBAAA6C,QAAA,EAAAuB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAzB,QAAA,EAAAuB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjH,CAAC;cAAA;gBAAAzB,QAAA,EAAAuB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxG,OAAA;gBAAKoG,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBrG,OAAA;kBAAKoG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BrG,OAAA;oBAAOoG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAO;oBAAAtB,QAAA,EAAAuB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7CxG,OAAA;oBAAO2G,IAAI,EAAC,MAAM;oBAACP,SAAS,EAAC,cAAc;oBAAC/D,IAAI,EAAC,SAAS;oBAACC,KAAK,EAAE9B,IAAI,CAACU,OAAQ;oBAAC0F,QAAQ,EAAE1E;kBAAa;oBAAA6C,QAAA,EAAAuB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAzB,QAAA,EAAAuB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG,CAAC,eACNxG,OAAA;kBAAKoG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BrG,OAAA;oBAAOoG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAO;oBAAAtB,QAAA,EAAAuB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7CxG,OAAA;oBAAQoG,SAAS,EAAC,cAAc;oBAAC/D,IAAI,EAAC,SAAS;oBAACC,KAAK,EAAE9B,IAAI,CAACW,OAAQ;oBAACyF,QAAQ,EAAE1E,YAAa;oBAAC2E,QAAQ;oBAAAR,QAAA,gBACnGrG,OAAA;sBAAQsC,KAAK,EAAC,EAAE;sBAAA+D,QAAA,EAAC;oBAAc;sBAAAtB,QAAA,EAAAuB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCxG,OAAA;sBAAQsC,KAAK,EAAC,KAAK;sBAAA+D,QAAA,EAAC;oBAAG;sBAAAtB,QAAA,EAAAuB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAChCxG,OAAA;sBAAQsC,KAAK,EAAC,KAAK;sBAAA+D,QAAA,EAAC;oBAAG;sBAAAtB,QAAA,EAAAuB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAzB,QAAA,EAAAuB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAzB,QAAA,EAAAuB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAzB,QAAA,EAAAuB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxG,OAAA;gBAAKoG,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBrG,OAAA;kBAAOoG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAtB,QAAA,EAAAuB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9CxG,OAAA;kBAAUoG,SAAS,EAAC,cAAc;kBAAC/D,IAAI,EAAC,UAAU;kBAACyE,IAAI,EAAC,GAAG;kBAACxE,KAAK,EAAE9B,IAAI,CAACI,QAAS;kBAACgG,QAAQ,EAAE1E,YAAa;kBAAC2E,QAAQ;gBAAA;kBAAA9B,QAAA,EAAAuB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAzB,QAAA,EAAAuB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3H,CAAC,eACNxG,OAAA;gBAAKoG,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBrG,OAAA;kBAAKoG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BrG,OAAA;oBAAOoG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAO;oBAAAtB,QAAA,EAAAuB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7CxG,OAAA;oBAAO2G,IAAI,EAAC,MAAM;oBAACP,SAAS,EAAC,cAAc;oBAAC/D,IAAI,EAAC,SAAS;oBAACC,KAAK,EAAE9B,IAAI,CAACG,OAAQ;oBAACiG,QAAQ,EAAE1E,YAAa;oBAAC2E,QAAQ;kBAAA;oBAAA9B,QAAA,EAAAuB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAzB,QAAA,EAAAuB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChH,CAAC,eACNxG,OAAA;kBAAKoG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BrG,OAAA;oBAAOoG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAtB,QAAA,EAAAuB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9CxG,OAAA;oBAAO2G,IAAI,EAAC,MAAM;oBAACP,SAAS,EAAC,cAAc;oBAAC/D,IAAI,EAAC,UAAU;oBAACC,KAAK,EAAE9B,IAAI,CAACK,QAAS;oBAAC+F,QAAQ,EAAE1E;kBAAa;oBAAA6C,QAAA,EAAAuB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAzB,QAAA,EAAAuB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzG,CAAC;cAAA;gBAAAzB,QAAA,EAAAuB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxG,OAAA;gBAAKoG,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBrG,OAAA;kBAAOoG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAtB,QAAA,EAAAuB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9CxG,OAAA;kBAAO2G,IAAI,EAAC,MAAM;kBAACP,SAAS,EAAC,cAAc;kBAACW,MAAM,EAAC,iBAAiB;kBAACH,QAAQ,EAAEI,gBAAiB;kBAACH,QAAQ;gBAAA;kBAAA9B,QAAA,EAAAuB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAzB,QAAA,EAAAuB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzG,CAAC,EACL5E,UAAU,iBAAI5B,OAAA;gBAAKoG,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAa;gBAAAtB,QAAA,EAAAuB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAC/E1E,KAAK,iBAAI9B,OAAA;gBAAKoG,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAEvE;cAAK;gBAAAiD,QAAA,EAAAuB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvExG,OAAA;gBAAKoG,SAAS,EAAC,QAAQ;gBAAAC,QAAA,eACrBrG,OAAA;kBAAQ2G,IAAI,EAAC,QAAQ;kBAACP,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAtB,QAAA,EAAAuB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAzB,QAAA,EAAAuB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAzB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAET;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAzB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAzB,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAzB,QAAA,EAAAuB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACtG,EAAA,CApRQD,MAAM;EAAA,QAwBIN,WAAW;AAAA;AAAAsH,EAAA,GAxBrBhH,MAAM;AAsRf,eAAeA,MAAM;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}