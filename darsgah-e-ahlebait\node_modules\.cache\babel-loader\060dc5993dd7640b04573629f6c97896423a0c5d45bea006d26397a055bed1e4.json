{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Darsgah-e-ahlebait\\\\darsgah-e-ahlebait\\\\src\\\\Submit.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { supabase } from './supabaseClient';\nimport { useNavigate } from 'react-router-dom';\nimport { Helmet } from 'react-helmet';\nimport jsPDF from 'jspdf';\nimport html2canvas from 'html2canvas';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Submit() {\n  _s();\n  // Get user from localStorage\n  const user = JSON.parse(localStorage.getItem('user') || 'null');\n  const [form, setForm] = useState({\n    // username will be set from user\n    title: '',\n    authors: '',\n    abstract: '',\n    keywords: '',\n    mainContent: '',\n    conclusion: '',\n    references: '',\n    organization: '',\n    country: '',\n    journal: ''\n  });\n  const [step, setStep] = useState(1); // 1: form, 2: preview, 3: confirmation\n  const [generatedPdf, setGeneratedPdf] = useState(null);\n  const [pdfBlob, setPdfBlob] = useState(null);\n  const [submitted, setSubmitted] = useState(false);\n  const [submitting, setSubmitting] = useState(false);\n  const [error, setError] = useState('');\n  const pdfRef = useRef();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setForm({\n      ...form,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  // Generate PDF from form data\n  const generatePDF = async () => {\n    const pdf = new jsPDF('p', 'mm', 'a4');\n    const pageWidth = pdf.internal.pageSize.getWidth();\n    const pageHeight = pdf.internal.pageSize.getHeight();\n    const margin = 25.4; // 1 inch margin in mm\n    const contentWidth = pageWidth - 2 * margin;\n    let yPosition = margin;\n\n    // Add journal logo/header (placeholder for now)\n    pdf.setFontSize(10);\n    pdf.text('Darsgah-e-Ahlebait Journal', pageWidth / 2, 15, {\n      align: 'center'\n    });\n    yPosition += 20;\n\n    // Title - Bold, 18pt, Centered\n    pdf.setFont('times', 'bold');\n    pdf.setFontSize(18);\n    const titleLines = pdf.splitTextToSize(form.title, contentWidth);\n    titleLines.forEach(line => {\n      pdf.text(line, pageWidth / 2, yPosition, {\n        align: 'center'\n      });\n      yPosition += 7;\n    });\n    yPosition += 10;\n\n    // Authors - Italic, 12pt, Centered\n    pdf.setFont('times', 'italic');\n    pdf.setFontSize(12);\n    const authorLines = pdf.splitTextToSize(form.authors, contentWidth);\n    authorLines.forEach(line => {\n      pdf.text(line, pageWidth / 2, yPosition, {\n        align: 'center'\n      });\n      yPosition += 5;\n    });\n    yPosition += 15;\n\n    // Abstract section\n    pdf.setFont('times', 'bold');\n    pdf.setFontSize(12);\n    pdf.text('Abstract', margin, yPosition);\n    yPosition += 8;\n    pdf.setFont('times', 'normal');\n    const abstractLines = pdf.splitTextToSize(form.abstract, contentWidth);\n    abstractLines.forEach(line => {\n      if (yPosition > pageHeight - margin) {\n        pdf.addPage();\n        yPosition = margin;\n      }\n      pdf.text(line, margin, yPosition, {\n        align: 'justify'\n      });\n      yPosition += 5;\n    });\n    yPosition += 10;\n\n    // Keywords section\n    pdf.setFont('times', 'bold');\n    pdf.text('Keywords', margin, yPosition);\n    yPosition += 8;\n    pdf.setFont('times', 'normal');\n    const keywordLines = pdf.splitTextToSize(form.keywords, contentWidth);\n    keywordLines.forEach(line => {\n      if (yPosition > pageHeight - margin) {\n        pdf.addPage();\n        yPosition = margin;\n      }\n      pdf.text(line, margin, yPosition, {\n        align: 'justify'\n      });\n      yPosition += 5;\n    });\n    yPosition += 15;\n\n    // Main Content section with 1.5 line spacing\n    pdf.setFont('times', 'bold');\n    pdf.text('Main Content', margin, yPosition);\n    yPosition += 8;\n    pdf.setFont('times', 'normal');\n    const contentLines = pdf.splitTextToSize(form.mainContent, contentWidth);\n    contentLines.forEach(line => {\n      if (yPosition > pageHeight - margin) {\n        pdf.addPage();\n        yPosition = margin;\n      }\n      pdf.text(line, margin, yPosition, {\n        align: 'justify'\n      });\n      yPosition += 7.5; // 1.5 line spacing\n    });\n    yPosition += 15;\n\n    // Conclusion section\n    pdf.setFont('times', 'bold');\n    pdf.text('Conclusion', margin, yPosition);\n    yPosition += 8;\n    pdf.setFont('times', 'normal');\n    const conclusionLines = pdf.splitTextToSize(form.conclusion, contentWidth);\n    conclusionLines.forEach(line => {\n      if (yPosition > pageHeight - margin) {\n        pdf.addPage();\n        yPosition = margin;\n      }\n      pdf.text(line, margin, yPosition, {\n        align: 'justify'\n      });\n      yPosition += 5;\n    });\n    yPosition += 15;\n\n    // References section\n    pdf.setFont('times', 'bold');\n    pdf.text('References', margin, yPosition);\n    yPosition += 8;\n    pdf.setFont('times', 'normal');\n    const refLines = form.references.split('\\n');\n    refLines.forEach((ref, index) => {\n      if (ref.trim()) {\n        if (yPosition > pageHeight - margin) {\n          pdf.addPage();\n          yPosition = margin;\n        }\n        pdf.text(`${index + 1}. ${ref}`, margin, yPosition);\n        yPosition += 5;\n      }\n    });\n\n    // Add page numbers\n    const pageCount = pdf.internal.getNumberOfPages();\n    for (let i = 1; i <= pageCount; i++) {\n      pdf.setPage(i);\n      pdf.setFontSize(10);\n      pdf.text(`${i}`, pageWidth / 2, pageHeight - 10, {\n        align: 'center'\n      });\n    }\n    return pdf;\n  };\n\n  // Handle form submission to generate PDF preview\n  const handleFormSubmit = async e => {\n    e.preventDefault();\n    if (!user) {\n      setError('You must be logged in to submit a publication.');\n      return;\n    }\n\n    // Validate required fields\n    if (!form.title || !form.authors || !form.abstract || !form.mainContent) {\n      setError('Please fill in all required fields.');\n      return;\n    }\n    setError('');\n    try {\n      const pdf = await generatePDF();\n      const pdfBlob = pdf.output('blob');\n      setPdfBlob(pdfBlob);\n      setGeneratedPdf(pdf.output('datauristring'));\n      setStep(2); // Move to preview step\n    } catch (err) {\n      setError('Failed to generate PDF preview.');\n      console.error('PDF generation error:', err);\n    }\n  };\n\n  // Handle final submission\n  const handleFinalSubmit = async () => {\n    setSubmitting(true);\n    setError('');\n    try {\n      // Generate filename\n      const authorName = form.authors.split(',')[0].trim().replace(/\\s+/g, '_');\n      const titleShort = form.title.substring(0, 30).replace(/\\s+/g, '_');\n      const fileName = `${authorName}_${titleShort}_${Date.now()}.pdf`;\n\n      // Upload PDF to Supabase Storage\n      const {\n        data,\n        error: uploadError\n      } = await supabase.storage.from('pdf').upload(fileName, pdfBlob);\n      if (uploadError) {\n        setError('Failed to upload PDF.');\n        setSubmitting(false);\n        return;\n      }\n\n      // Get public URL\n      const {\n        data: urlData\n      } = supabase.storage.from('pdf').getPublicUrl(fileName);\n      const pdf_url = urlData.publicUrl;\n\n      // Insert into request table\n      const {\n        error: insertError\n      } = await supabase.from('request').insert([{\n        ...form,\n        username: user.username || user.full_name || user.email,\n        email: user.email,\n        pdf_url\n      }]);\n      if (insertError) {\n        setError('Failed to submit publication. ' + (insertError.message || ''));\n        setSubmitting(false);\n        return;\n      }\n      setSubmitted(true);\n      setSubmitting(false);\n      setStep(3); // Move to confirmation step\n    } catch (err) {\n      setError('Failed to submit publication.');\n      setSubmitting(false);\n      console.error('Submission error:', err);\n    }\n  };\n\n  // Handle edit button\n  const handleEdit = () => {\n    setStep(1);\n    setGeneratedPdf(null);\n    setPdfBlob(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"submit-page\",\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Submit - darsgah-e-ahlebait\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Submit your research paper to darsgah-e-ahlebait journals.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-9\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card shadow-sm border-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"mb-4 text-center\",\n              children: \"Submit a Publication\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), submitted ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"alert alert-success text-center\",\n              children: \"Your submission has been received!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this) : !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"alert alert-danger text-center my-5\",\n              children: \"You must be logged in to submit a publication.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleFormSubmit,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Title\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control\",\n                    name: \"title\",\n                    value: form.title,\n                    onChange: handleChange,\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Organization\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control\",\n                    name: \"organization\",\n                    value: form.organization,\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Country\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control\",\n                    name: \"country\",\n                    value: form.country,\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Journal\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    className: \"form-control\",\n                    name: \"journal\",\n                    value: form.journal,\n                    onChange: handleChange,\n                    required: true,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Journal\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"JBS\",\n                      children: \"JBS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"JNS\",\n                      children: \"JNS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Abstract\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  className: \"form-control\",\n                  name: \"abstract\",\n                  rows: \"3\",\n                  value: form.abstract,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Authors\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control\",\n                    name: \"authors\",\n                    value: form.authors,\n                    onChange: handleChange,\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-6 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label\",\n                    children: \"Keywords\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control\",\n                    name: \"keywords\",\n                    value: form.keywords,\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Main Content (support paragraphs and headers) *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  className: \"form-control\",\n                  name: \"mainContent\",\n                  value: form.mainContent,\n                  onChange: handleChange,\n                  rows: \"10\",\n                  placeholder: \"Enter your main content here. Use line breaks for paragraphs.\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Conclusion *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  className: \"form-control\",\n                  name: \"conclusion\",\n                  value: form.conclusion,\n                  onChange: handleChange,\n                  rows: \"4\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"References\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  className: \"form-control\",\n                  name: \"references\",\n                  value: form.references,\n                  onChange: handleChange,\n                  rows: \"6\",\n                  placeholder: \"Enter each reference on a new line\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 21\n              }, this), submitting && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"alert alert-info text-center\",\n                children: \"Submitting...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 36\n              }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"alert alert-danger text-center\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 31\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-grid\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"btn btn-success\",\n                  children: \"Submit Publication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 257,\n    columnNumber: 5\n  }, this);\n}\n_s(Submit, \"L553YBHgwz0p9bCcXQyTMPwCtp4=\", false, function () {\n  return [useNavigate];\n});\n_c = Submit;\nexport default Submit;\nvar _c;\n$RefreshReg$(_c, \"Submit\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "supabase", "useNavigate", "<PERSON><PERSON><PERSON>", "jsPDF", "html2canvas", "jsxDEV", "_jsxDEV", "Submit", "_s", "user", "JSON", "parse", "localStorage", "getItem", "form", "setForm", "title", "authors", "abstract", "keywords", "mainContent", "conclusion", "references", "organization", "country", "journal", "step", "setStep", "generatedPdf", "setGeneratedPdf", "pdfBlob", "setPdfBlob", "submitted", "setSubmitted", "submitting", "setSubmitting", "error", "setError", "pdfRef", "navigate", "handleChange", "e", "target", "name", "value", "generatePDF", "pdf", "pageWidth", "internal", "pageSize", "getWidth", "pageHeight", "getHeight", "margin", "contentWidth", "yPosition", "setFontSize", "text", "align", "setFont", "titleLines", "splitTextToSize", "for<PERSON>ach", "line", "authorLines", "abstractLines", "addPage", "keywordLines", "contentLines", "conclusionLines", "refLines", "split", "ref", "index", "trim", "pageCount", "getNumberOfPages", "i", "setPage", "handleFormSubmit", "preventDefault", "output", "err", "console", "handleFinalSubmit", "<PERSON><PERSON><PERSON>", "replace", "titleShort", "substring", "fileName", "Date", "now", "data", "uploadError", "storage", "from", "upload", "urlData", "getPublicUrl", "pdf_url", "publicUrl", "insertError", "insert", "username", "full_name", "email", "message", "handleEdit", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "content", "onSubmit", "type", "onChange", "required", "rows", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Darsgah-e-ahlebait/darsgah-e-ahlebait/src/Submit.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\r\nimport { supabase } from './supabaseClient';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { Helmet } from 'react-helmet';\r\nimport jsPDF from 'jspdf';\r\nimport html2canvas from 'html2canvas';\r\n\r\nfunction Submit() {\r\n  // Get user from localStorage\r\n  const user = JSON.parse(localStorage.getItem('user') || 'null');\r\n  const [form, setForm] = useState({\r\n    // username will be set from user\r\n    title: '',\r\n    authors: '',\r\n    abstract: '',\r\n    keywords: '',\r\n    mainContent: '',\r\n    conclusion: '',\r\n    references: '',\r\n    organization: '',\r\n    country: '',\r\n    journal: '',\r\n  });\r\n  const [step, setStep] = useState(1); // 1: form, 2: preview, 3: confirmation\r\n  const [generatedPdf, setGeneratedPdf] = useState(null);\r\n  const [pdfBlob, setPdfBlob] = useState(null);\r\n  const [submitted, setSubmitted] = useState(false);\r\n  const [submitting, setSubmitting] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const pdfRef = useRef();\r\n\r\n  const navigate = useNavigate();\r\n\r\n  const handleChange = e => {\r\n    setForm({ ...form, [e.target.name]: e.target.value });\r\n  };\r\n\r\n  // Generate PDF from form data\r\n  const generatePDF = async () => {\r\n    const pdf = new jsPDF('p', 'mm', 'a4');\r\n    const pageWidth = pdf.internal.pageSize.getWidth();\r\n    const pageHeight = pdf.internal.pageSize.getHeight();\r\n    const margin = 25.4; // 1 inch margin in mm\r\n    const contentWidth = pageWidth - (2 * margin);\r\n    let yPosition = margin;\r\n\r\n    // Add journal logo/header (placeholder for now)\r\n    pdf.setFontSize(10);\r\n    pdf.text('Darsgah-e-Ahlebait Journal', pageWidth / 2, 15, { align: 'center' });\r\n    yPosition += 20;\r\n\r\n    // Title - Bold, 18pt, Centered\r\n    pdf.setFont('times', 'bold');\r\n    pdf.setFontSize(18);\r\n    const titleLines = pdf.splitTextToSize(form.title, contentWidth);\r\n    titleLines.forEach(line => {\r\n      pdf.text(line, pageWidth / 2, yPosition, { align: 'center' });\r\n      yPosition += 7;\r\n    });\r\n    yPosition += 10;\r\n\r\n    // Authors - Italic, 12pt, Centered\r\n    pdf.setFont('times', 'italic');\r\n    pdf.setFontSize(12);\r\n    const authorLines = pdf.splitTextToSize(form.authors, contentWidth);\r\n    authorLines.forEach(line => {\r\n      pdf.text(line, pageWidth / 2, yPosition, { align: 'center' });\r\n      yPosition += 5;\r\n    });\r\n    yPosition += 15;\r\n\r\n    // Abstract section\r\n    pdf.setFont('times', 'bold');\r\n    pdf.setFontSize(12);\r\n    pdf.text('Abstract', margin, yPosition);\r\n    yPosition += 8;\r\n\r\n    pdf.setFont('times', 'normal');\r\n    const abstractLines = pdf.splitTextToSize(form.abstract, contentWidth);\r\n    abstractLines.forEach(line => {\r\n      if (yPosition > pageHeight - margin) {\r\n        pdf.addPage();\r\n        yPosition = margin;\r\n      }\r\n      pdf.text(line, margin, yPosition, { align: 'justify' });\r\n      yPosition += 5;\r\n    });\r\n    yPosition += 10;\r\n\r\n    // Keywords section\r\n    pdf.setFont('times', 'bold');\r\n    pdf.text('Keywords', margin, yPosition);\r\n    yPosition += 8;\r\n\r\n    pdf.setFont('times', 'normal');\r\n    const keywordLines = pdf.splitTextToSize(form.keywords, contentWidth);\r\n    keywordLines.forEach(line => {\r\n      if (yPosition > pageHeight - margin) {\r\n        pdf.addPage();\r\n        yPosition = margin;\r\n      }\r\n      pdf.text(line, margin, yPosition, { align: 'justify' });\r\n      yPosition += 5;\r\n    });\r\n    yPosition += 15;\r\n\r\n    // Main Content section with 1.5 line spacing\r\n    pdf.setFont('times', 'bold');\r\n    pdf.text('Main Content', margin, yPosition);\r\n    yPosition += 8;\r\n\r\n    pdf.setFont('times', 'normal');\r\n    const contentLines = pdf.splitTextToSize(form.mainContent, contentWidth);\r\n    contentLines.forEach(line => {\r\n      if (yPosition > pageHeight - margin) {\r\n        pdf.addPage();\r\n        yPosition = margin;\r\n      }\r\n      pdf.text(line, margin, yPosition, { align: 'justify' });\r\n      yPosition += 7.5; // 1.5 line spacing\r\n    });\r\n    yPosition += 15;\r\n\r\n    // Conclusion section\r\n    pdf.setFont('times', 'bold');\r\n    pdf.text('Conclusion', margin, yPosition);\r\n    yPosition += 8;\r\n\r\n    pdf.setFont('times', 'normal');\r\n    const conclusionLines = pdf.splitTextToSize(form.conclusion, contentWidth);\r\n    conclusionLines.forEach(line => {\r\n      if (yPosition > pageHeight - margin) {\r\n        pdf.addPage();\r\n        yPosition = margin;\r\n      }\r\n      pdf.text(line, margin, yPosition, { align: 'justify' });\r\n      yPosition += 5;\r\n    });\r\n    yPosition += 15;\r\n\r\n    // References section\r\n    pdf.setFont('times', 'bold');\r\n    pdf.text('References', margin, yPosition);\r\n    yPosition += 8;\r\n\r\n    pdf.setFont('times', 'normal');\r\n    const refLines = form.references.split('\\n');\r\n    refLines.forEach((ref, index) => {\r\n      if (ref.trim()) {\r\n        if (yPosition > pageHeight - margin) {\r\n          pdf.addPage();\r\n          yPosition = margin;\r\n        }\r\n        pdf.text(`${index + 1}. ${ref}`, margin, yPosition);\r\n        yPosition += 5;\r\n      }\r\n    });\r\n\r\n    // Add page numbers\r\n    const pageCount = pdf.internal.getNumberOfPages();\r\n    for (let i = 1; i <= pageCount; i++) {\r\n      pdf.setPage(i);\r\n      pdf.setFontSize(10);\r\n      pdf.text(`${i}`, pageWidth / 2, pageHeight - 10, { align: 'center' });\r\n    }\r\n\r\n    return pdf;\r\n  };\r\n\r\n  // Handle form submission to generate PDF preview\r\n  const handleFormSubmit = async (e) => {\r\n    e.preventDefault();\r\n    if (!user) {\r\n      setError('You must be logged in to submit a publication.');\r\n      return;\r\n    }\r\n\r\n    // Validate required fields\r\n    if (!form.title || !form.authors || !form.abstract || !form.mainContent) {\r\n      setError('Please fill in all required fields.');\r\n      return;\r\n    }\r\n\r\n    setError('');\r\n    try {\r\n      const pdf = await generatePDF();\r\n      const pdfBlob = pdf.output('blob');\r\n      setPdfBlob(pdfBlob);\r\n      setGeneratedPdf(pdf.output('datauristring'));\r\n      setStep(2); // Move to preview step\r\n    } catch (err) {\r\n      setError('Failed to generate PDF preview.');\r\n      console.error('PDF generation error:', err);\r\n    }\r\n  };\r\n\r\n  // Handle final submission\r\n  const handleFinalSubmit = async () => {\r\n    setSubmitting(true);\r\n    setError('');\r\n\r\n    try {\r\n      // Generate filename\r\n      const authorName = form.authors.split(',')[0].trim().replace(/\\s+/g, '_');\r\n      const titleShort = form.title.substring(0, 30).replace(/\\s+/g, '_');\r\n      const fileName = `${authorName}_${titleShort}_${Date.now()}.pdf`;\r\n\r\n      // Upload PDF to Supabase Storage\r\n      const { data, error: uploadError } = await supabase.storage\r\n        .from('pdf')\r\n        .upload(fileName, pdfBlob);\r\n\r\n      if (uploadError) {\r\n        setError('Failed to upload PDF.');\r\n        setSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      // Get public URL\r\n      const { data: urlData } = supabase.storage.from('pdf').getPublicUrl(fileName);\r\n      const pdf_url = urlData.publicUrl;\r\n\r\n      // Insert into request table\r\n      const { error: insertError } = await supabase.from('request').insert([\r\n        {\r\n          ...form,\r\n          username: user.username || user.full_name || user.email,\r\n          email: user.email,\r\n          pdf_url\r\n        }\r\n      ]);\r\n\r\n      if (insertError) {\r\n        setError('Failed to submit publication. ' + (insertError.message || ''));\r\n        setSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      setSubmitted(true);\r\n      setSubmitting(false);\r\n      setStep(3); // Move to confirmation step\r\n    } catch (err) {\r\n      setError('Failed to submit publication.');\r\n      setSubmitting(false);\r\n      console.error('Submission error:', err);\r\n    }\r\n  };\r\n\r\n  // Handle edit button\r\n  const handleEdit = () => {\r\n    setStep(1);\r\n    setGeneratedPdf(null);\r\n    setPdfBlob(null);\r\n  };\r\n\r\n  return (\r\n    <div className=\"submit-page\">\r\n      <Helmet>\r\n        <title>Submit - darsgah-e-ahlebait</title>\r\n        <meta name=\"description\" content=\"Submit your research paper to darsgah-e-ahlebait journals.\" />\r\n      </Helmet>\r\n      <div className=\"row justify-content-center\">\r\n        <div className=\"col-md-9\">\r\n          <div className=\"card shadow-sm border-0\">\r\n            <div className=\"card-body\">\r\n              <h2 className=\"mb-4 text-center\">Submit a Publication</h2>\r\n              {submitted ? (\r\n                <div className=\"alert alert-success text-center\">Your submission has been received!</div>\r\n              ) : (\r\n                !user ? (\r\n                  <div className=\"alert alert-danger text-center my-5\">You must be logged in to submit a publication.</div>\r\n                ) : (\r\n                  <form onSubmit={handleFormSubmit}>\r\n                    <div className=\"row\">\r\n                      <div className=\"col-md-6 mb-3\">\r\n                        <label className=\"form-label\">Title</label>\r\n                        <input type=\"text\" className=\"form-control\" name=\"title\" value={form.title} onChange={handleChange} required />\r\n                      </div>\r\n                      <div className=\"col-md-6 mb-3\">\r\n                        <label className=\"form-label\">Organization</label>\r\n                        <input type=\"text\" className=\"form-control\" name=\"organization\" value={form.organization} onChange={handleChange} />\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"row\">\r\n                      <div className=\"col-md-6 mb-3\">\r\n                        <label className=\"form-label\">Country</label>\r\n                        <input type=\"text\" className=\"form-control\" name=\"country\" value={form.country} onChange={handleChange} />\r\n                      </div>\r\n                      <div className=\"col-md-6 mb-3\">\r\n                        <label className=\"form-label\">Journal</label>\r\n                        <select className=\"form-control\" name=\"journal\" value={form.journal} onChange={handleChange} required>\r\n                          <option value=\"\">Select Journal</option>\r\n                          <option value=\"JBS\">JBS</option>\r\n                          <option value=\"JNS\">JNS</option>\r\n                        </select>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"mb-3\">\r\n                      <label className=\"form-label\">Abstract</label>\r\n                      <textarea className=\"form-control\" name=\"abstract\" rows=\"3\" value={form.abstract} onChange={handleChange} required></textarea>\r\n                    </div>\r\n                    <div className=\"row\">\r\n                      <div className=\"col-md-6 mb-3\">\r\n                        <label className=\"form-label\">Authors</label>\r\n                        <input type=\"text\" className=\"form-control\" name=\"authors\" value={form.authors} onChange={handleChange} required />\r\n                      </div>\r\n                      <div className=\"col-md-6 mb-3\">\r\n                        <label className=\"form-label\">Keywords</label>\r\n                        <input type=\"text\" className=\"form-control\" name=\"keywords\" value={form.keywords} onChange={handleChange} />\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"mb-3\">\r\n                      <label className=\"form-label\">Main Content (support paragraphs and headers) *</label>\r\n                      <textarea\r\n                        className=\"form-control\"\r\n                        name=\"mainContent\"\r\n                        value={form.mainContent}\r\n                        onChange={handleChange}\r\n                        rows=\"10\"\r\n                        placeholder=\"Enter your main content here. Use line breaks for paragraphs.\"\r\n                        required\r\n                      ></textarea>\r\n                    </div>\r\n\r\n                    <div className=\"mb-3\">\r\n                      <label className=\"form-label\">Conclusion *</label>\r\n                      <textarea\r\n                        className=\"form-control\"\r\n                        name=\"conclusion\"\r\n                        value={form.conclusion}\r\n                        onChange={handleChange}\r\n                        rows=\"4\"\r\n                        required\r\n                      ></textarea>\r\n                    </div>\r\n\r\n                    <div className=\"mb-3\">\r\n                      <label className=\"form-label\">References</label>\r\n                      <textarea\r\n                        className=\"form-control\"\r\n                        name=\"references\"\r\n                        value={form.references}\r\n                        onChange={handleChange}\r\n                        rows=\"6\"\r\n                        placeholder=\"Enter each reference on a new line\"\r\n                      ></textarea>\r\n                    </div>\r\n                    {submitting && <div className=\"alert alert-info text-center\">Submitting...</div>}\r\n                    {error && <div className=\"alert alert-danger text-center\">{error}</div>}\r\n                    <div className=\"d-grid\">\r\n                      <button type=\"submit\" className=\"btn btn-success\">Submit Publication</button>\r\n                    </div>\r\n                  </form>\r\n                )\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Submit; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,cAAc;AACrC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB;EACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC;EAC/D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAC;IAC/B;IACAkB,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMwC,MAAM,GAAGvC,MAAM,CAAC,CAAC;EAEvB,MAAMwC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAE9B,MAAMuC,YAAY,GAAGC,CAAC,IAAI;IACxB1B,OAAO,CAAC;MAAE,GAAGD,IAAI;MAAE,CAAC2B,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAMC,GAAG,GAAG,IAAI3C,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;IACtC,MAAM4C,SAAS,GAAGD,GAAG,CAACE,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,CAAC;IAClD,MAAMC,UAAU,GAAGL,GAAG,CAACE,QAAQ,CAACC,QAAQ,CAACG,SAAS,CAAC,CAAC;IACpD,MAAMC,MAAM,GAAG,IAAI,CAAC,CAAC;IACrB,MAAMC,YAAY,GAAGP,SAAS,GAAI,CAAC,GAAGM,MAAO;IAC7C,IAAIE,SAAS,GAAGF,MAAM;;IAEtB;IACAP,GAAG,CAACU,WAAW,CAAC,EAAE,CAAC;IACnBV,GAAG,CAACW,IAAI,CAAC,4BAA4B,EAAEV,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE;MAAEW,KAAK,EAAE;IAAS,CAAC,CAAC;IAC9EH,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IAC5Bb,GAAG,CAACU,WAAW,CAAC,EAAE,CAAC;IACnB,MAAMI,UAAU,GAAGd,GAAG,CAACe,eAAe,CAAC/C,IAAI,CAACE,KAAK,EAAEsC,YAAY,CAAC;IAChEM,UAAU,CAACE,OAAO,CAACC,IAAI,IAAI;MACzBjB,GAAG,CAACW,IAAI,CAACM,IAAI,EAAEhB,SAAS,GAAG,CAAC,EAAEQ,SAAS,EAAE;QAAEG,KAAK,EAAE;MAAS,CAAC,CAAC;MAC7DH,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC9Bb,GAAG,CAACU,WAAW,CAAC,EAAE,CAAC;IACnB,MAAMQ,WAAW,GAAGlB,GAAG,CAACe,eAAe,CAAC/C,IAAI,CAACG,OAAO,EAAEqC,YAAY,CAAC;IACnEU,WAAW,CAACF,OAAO,CAACC,IAAI,IAAI;MAC1BjB,GAAG,CAACW,IAAI,CAACM,IAAI,EAAEhB,SAAS,GAAG,CAAC,EAAEQ,SAAS,EAAE;QAAEG,KAAK,EAAE;MAAS,CAAC,CAAC;MAC7DH,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IAC5Bb,GAAG,CAACU,WAAW,CAAC,EAAE,CAAC;IACnBV,GAAG,CAACW,IAAI,CAAC,UAAU,EAAEJ,MAAM,EAAEE,SAAS,CAAC;IACvCA,SAAS,IAAI,CAAC;IAEdT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC9B,MAAMM,aAAa,GAAGnB,GAAG,CAACe,eAAe,CAAC/C,IAAI,CAACI,QAAQ,EAAEoC,YAAY,CAAC;IACtEW,aAAa,CAACH,OAAO,CAACC,IAAI,IAAI;MAC5B,IAAIR,SAAS,GAAGJ,UAAU,GAAGE,MAAM,EAAE;QACnCP,GAAG,CAACoB,OAAO,CAAC,CAAC;QACbX,SAAS,GAAGF,MAAM;MACpB;MACAP,GAAG,CAACW,IAAI,CAACM,IAAI,EAAEV,MAAM,EAAEE,SAAS,EAAE;QAAEG,KAAK,EAAE;MAAU,CAAC,CAAC;MACvDH,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IAC5Bb,GAAG,CAACW,IAAI,CAAC,UAAU,EAAEJ,MAAM,EAAEE,SAAS,CAAC;IACvCA,SAAS,IAAI,CAAC;IAEdT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC9B,MAAMQ,YAAY,GAAGrB,GAAG,CAACe,eAAe,CAAC/C,IAAI,CAACK,QAAQ,EAAEmC,YAAY,CAAC;IACrEa,YAAY,CAACL,OAAO,CAACC,IAAI,IAAI;MAC3B,IAAIR,SAAS,GAAGJ,UAAU,GAAGE,MAAM,EAAE;QACnCP,GAAG,CAACoB,OAAO,CAAC,CAAC;QACbX,SAAS,GAAGF,MAAM;MACpB;MACAP,GAAG,CAACW,IAAI,CAACM,IAAI,EAAEV,MAAM,EAAEE,SAAS,EAAE;QAAEG,KAAK,EAAE;MAAU,CAAC,CAAC;MACvDH,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IAC5Bb,GAAG,CAACW,IAAI,CAAC,cAAc,EAAEJ,MAAM,EAAEE,SAAS,CAAC;IAC3CA,SAAS,IAAI,CAAC;IAEdT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC9B,MAAMS,YAAY,GAAGtB,GAAG,CAACe,eAAe,CAAC/C,IAAI,CAACM,WAAW,EAAEkC,YAAY,CAAC;IACxEc,YAAY,CAACN,OAAO,CAACC,IAAI,IAAI;MAC3B,IAAIR,SAAS,GAAGJ,UAAU,GAAGE,MAAM,EAAE;QACnCP,GAAG,CAACoB,OAAO,CAAC,CAAC;QACbX,SAAS,GAAGF,MAAM;MACpB;MACAP,GAAG,CAACW,IAAI,CAACM,IAAI,EAAEV,MAAM,EAAEE,SAAS,EAAE;QAAEG,KAAK,EAAE;MAAU,CAAC,CAAC;MACvDH,SAAS,IAAI,GAAG,CAAC,CAAC;IACpB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IAC5Bb,GAAG,CAACW,IAAI,CAAC,YAAY,EAAEJ,MAAM,EAAEE,SAAS,CAAC;IACzCA,SAAS,IAAI,CAAC;IAEdT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC9B,MAAMU,eAAe,GAAGvB,GAAG,CAACe,eAAe,CAAC/C,IAAI,CAACO,UAAU,EAAEiC,YAAY,CAAC;IAC1Ee,eAAe,CAACP,OAAO,CAACC,IAAI,IAAI;MAC9B,IAAIR,SAAS,GAAGJ,UAAU,GAAGE,MAAM,EAAE;QACnCP,GAAG,CAACoB,OAAO,CAAC,CAAC;QACbX,SAAS,GAAGF,MAAM;MACpB;MACAP,GAAG,CAACW,IAAI,CAACM,IAAI,EAAEV,MAAM,EAAEE,SAAS,EAAE;QAAEG,KAAK,EAAE;MAAU,CAAC,CAAC;MACvDH,SAAS,IAAI,CAAC;IAChB,CAAC,CAAC;IACFA,SAAS,IAAI,EAAE;;IAEf;IACAT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IAC5Bb,GAAG,CAACW,IAAI,CAAC,YAAY,EAAEJ,MAAM,EAAEE,SAAS,CAAC;IACzCA,SAAS,IAAI,CAAC;IAEdT,GAAG,CAACa,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC9B,MAAMW,QAAQ,GAAGxD,IAAI,CAACQ,UAAU,CAACiD,KAAK,CAAC,IAAI,CAAC;IAC5CD,QAAQ,CAACR,OAAO,CAAC,CAACU,GAAG,EAAEC,KAAK,KAAK;MAC/B,IAAID,GAAG,CAACE,IAAI,CAAC,CAAC,EAAE;QACd,IAAInB,SAAS,GAAGJ,UAAU,GAAGE,MAAM,EAAE;UACnCP,GAAG,CAACoB,OAAO,CAAC,CAAC;UACbX,SAAS,GAAGF,MAAM;QACpB;QACAP,GAAG,CAACW,IAAI,CAAC,GAAGgB,KAAK,GAAG,CAAC,KAAKD,GAAG,EAAE,EAAEnB,MAAM,EAAEE,SAAS,CAAC;QACnDA,SAAS,IAAI,CAAC;MAChB;IACF,CAAC,CAAC;;IAEF;IACA,MAAMoB,SAAS,GAAG7B,GAAG,CAACE,QAAQ,CAAC4B,gBAAgB,CAAC,CAAC;IACjD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,SAAS,EAAEE,CAAC,EAAE,EAAE;MACnC/B,GAAG,CAACgC,OAAO,CAACD,CAAC,CAAC;MACd/B,GAAG,CAACU,WAAW,CAAC,EAAE,CAAC;MACnBV,GAAG,CAACW,IAAI,CAAC,GAAGoB,CAAC,EAAE,EAAE9B,SAAS,GAAG,CAAC,EAAEI,UAAU,GAAG,EAAE,EAAE;QAAEO,KAAK,EAAE;MAAS,CAAC,CAAC;IACvE;IAEA,OAAOZ,GAAG;EACZ,CAAC;;EAED;EACA,MAAMiC,gBAAgB,GAAG,MAAOtC,CAAC,IAAK;IACpCA,CAAC,CAACuC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACvE,IAAI,EAAE;MACT4B,QAAQ,CAAC,gDAAgD,CAAC;MAC1D;IACF;;IAEA;IACA,IAAI,CAACvB,IAAI,CAACE,KAAK,IAAI,CAACF,IAAI,CAACG,OAAO,IAAI,CAACH,IAAI,CAACI,QAAQ,IAAI,CAACJ,IAAI,CAACM,WAAW,EAAE;MACvEiB,QAAQ,CAAC,qCAAqC,CAAC;MAC/C;IACF;IAEAA,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,MAAMS,GAAG,GAAG,MAAMD,WAAW,CAAC,CAAC;MAC/B,MAAMf,OAAO,GAAGgB,GAAG,CAACmC,MAAM,CAAC,MAAM,CAAC;MAClClD,UAAU,CAACD,OAAO,CAAC;MACnBD,eAAe,CAACiB,GAAG,CAACmC,MAAM,CAAC,eAAe,CAAC,CAAC;MAC5CtD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,OAAOuD,GAAG,EAAE;MACZ7C,QAAQ,CAAC,iCAAiC,CAAC;MAC3C8C,OAAO,CAAC/C,KAAK,CAAC,uBAAuB,EAAE8C,GAAG,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCjD,aAAa,CAAC,IAAI,CAAC;IACnBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,MAAMgD,UAAU,GAAGvE,IAAI,CAACG,OAAO,CAACsD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,CAACY,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;MACzE,MAAMC,UAAU,GAAGzE,IAAI,CAACE,KAAK,CAACwE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAACF,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;MACnE,MAAMG,QAAQ,GAAG,GAAGJ,UAAU,IAAIE,UAAU,IAAIG,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;;MAEhE;MACA,MAAM;QAAEC,IAAI;QAAExD,KAAK,EAAEyD;MAAY,CAAC,GAAG,MAAM7F,QAAQ,CAAC8F,OAAO,CACxDC,IAAI,CAAC,KAAK,CAAC,CACXC,MAAM,CAACP,QAAQ,EAAE3D,OAAO,CAAC;MAE5B,IAAI+D,WAAW,EAAE;QACfxD,QAAQ,CAAC,uBAAuB,CAAC;QACjCF,aAAa,CAAC,KAAK,CAAC;QACpB;MACF;;MAEA;MACA,MAAM;QAAEyD,IAAI,EAAEK;MAAQ,CAAC,GAAGjG,QAAQ,CAAC8F,OAAO,CAACC,IAAI,CAAC,KAAK,CAAC,CAACG,YAAY,CAACT,QAAQ,CAAC;MAC7E,MAAMU,OAAO,GAAGF,OAAO,CAACG,SAAS;;MAEjC;MACA,MAAM;QAAEhE,KAAK,EAAEiE;MAAY,CAAC,GAAG,MAAMrG,QAAQ,CAAC+F,IAAI,CAAC,SAAS,CAAC,CAACO,MAAM,CAAC,CACnE;QACE,GAAGxF,IAAI;QACPyF,QAAQ,EAAE9F,IAAI,CAAC8F,QAAQ,IAAI9F,IAAI,CAAC+F,SAAS,IAAI/F,IAAI,CAACgG,KAAK;QACvDA,KAAK,EAAEhG,IAAI,CAACgG,KAAK;QACjBN;MACF,CAAC,CACF,CAAC;MAEF,IAAIE,WAAW,EAAE;QACfhE,QAAQ,CAAC,gCAAgC,IAAIgE,WAAW,CAACK,OAAO,IAAI,EAAE,CAAC,CAAC;QACxEvE,aAAa,CAAC,KAAK,CAAC;QACpB;MACF;MAEAF,YAAY,CAAC,IAAI,CAAC;MAClBE,aAAa,CAAC,KAAK,CAAC;MACpBR,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,OAAOuD,GAAG,EAAE;MACZ7C,QAAQ,CAAC,+BAA+B,CAAC;MACzCF,aAAa,CAAC,KAAK,CAAC;MACpBgD,OAAO,CAAC/C,KAAK,CAAC,mBAAmB,EAAE8C,GAAG,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMyB,UAAU,GAAGA,CAAA,KAAM;IACvBhF,OAAO,CAAC,CAAC,CAAC;IACVE,eAAe,CAAC,IAAI,CAAC;IACrBE,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,oBACEzB,OAAA;IAAKsG,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BvG,OAAA,CAACJ,MAAM;MAAA2G,QAAA,gBACLvG,OAAA;QAAAuG,QAAA,EAAO;MAA2B;QAAApB,QAAA,EAAAqB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1C1G,OAAA;QAAMqC,IAAI,EAAC,aAAa;QAACsE,OAAO,EAAC;MAA4D;QAAAxB,QAAA,EAAAqB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAvB,QAAA,EAAAqB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1F,CAAC,eACT1G,OAAA;MAAKsG,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzCvG,OAAA;QAAKsG,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBvG,OAAA;UAAKsG,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtCvG,OAAA;YAAKsG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBvG,OAAA;cAAIsG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAoB;cAAApB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACzDhF,SAAS,gBACR1B,OAAA;cAAKsG,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAkC;cAAApB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEzF,CAACvG,IAAI,gBACHH,OAAA;cAAKsG,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAA8C;cAAApB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAEzG1G,OAAA;cAAM4G,QAAQ,EAAEnC,gBAAiB;cAAA8B,QAAA,gBAC/BvG,OAAA;gBAAKsG,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBvG,OAAA;kBAAKsG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BvG,OAAA;oBAAOsG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAK;oBAAApB,QAAA,EAAAqB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3C1G,OAAA;oBAAO6G,IAAI,EAAC,MAAM;oBAACP,SAAS,EAAC,cAAc;oBAACjE,IAAI,EAAC,OAAO;oBAACC,KAAK,EAAE9B,IAAI,CAACE,KAAM;oBAACoG,QAAQ,EAAE5E,YAAa;oBAAC6E,QAAQ;kBAAA;oBAAA5B,QAAA,EAAAqB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAvB,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5G,CAAC,eACN1G,OAAA;kBAAKsG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BvG,OAAA;oBAAOsG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAY;oBAAApB,QAAA,EAAAqB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClD1G,OAAA;oBAAO6G,IAAI,EAAC,MAAM;oBAACP,SAAS,EAAC,cAAc;oBAACjE,IAAI,EAAC,cAAc;oBAACC,KAAK,EAAE9B,IAAI,CAACS,YAAa;oBAAC6F,QAAQ,EAAE5E;kBAAa;oBAAAiD,QAAA,EAAAqB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAvB,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjH,CAAC;cAAA;gBAAAvB,QAAA,EAAAqB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1G,OAAA;gBAAKsG,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBvG,OAAA;kBAAKsG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BvG,OAAA;oBAAOsG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAO;oBAAApB,QAAA,EAAAqB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7C1G,OAAA;oBAAO6G,IAAI,EAAC,MAAM;oBAACP,SAAS,EAAC,cAAc;oBAACjE,IAAI,EAAC,SAAS;oBAACC,KAAK,EAAE9B,IAAI,CAACU,OAAQ;oBAAC4F,QAAQ,EAAE5E;kBAAa;oBAAAiD,QAAA,EAAAqB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAvB,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG,CAAC,eACN1G,OAAA;kBAAKsG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BvG,OAAA;oBAAOsG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAO;oBAAApB,QAAA,EAAAqB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7C1G,OAAA;oBAAQsG,SAAS,EAAC,cAAc;oBAACjE,IAAI,EAAC,SAAS;oBAACC,KAAK,EAAE9B,IAAI,CAACW,OAAQ;oBAAC2F,QAAQ,EAAE5E,YAAa;oBAAC6E,QAAQ;oBAAAR,QAAA,gBACnGvG,OAAA;sBAAQsC,KAAK,EAAC,EAAE;sBAAAiE,QAAA,EAAC;oBAAc;sBAAApB,QAAA,EAAAqB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxC1G,OAAA;sBAAQsC,KAAK,EAAC,KAAK;sBAAAiE,QAAA,EAAC;oBAAG;sBAAApB,QAAA,EAAAqB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAChC1G,OAAA;sBAAQsC,KAAK,EAAC,KAAK;sBAAAiE,QAAA,EAAC;oBAAG;sBAAApB,QAAA,EAAAqB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAvB,QAAA,EAAAqB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAvB,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAvB,QAAA,EAAAqB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1G,OAAA;gBAAKsG,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBvG,OAAA;kBAAOsG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAQ;kBAAApB,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9C1G,OAAA;kBAAUsG,SAAS,EAAC,cAAc;kBAACjE,IAAI,EAAC,UAAU;kBAAC2E,IAAI,EAAC,GAAG;kBAAC1E,KAAK,EAAE9B,IAAI,CAACI,QAAS;kBAACkG,QAAQ,EAAE5E,YAAa;kBAAC6E,QAAQ;gBAAA;kBAAA5B,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAvB,QAAA,EAAAqB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3H,CAAC,eACN1G,OAAA;gBAAKsG,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBvG,OAAA;kBAAKsG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BvG,OAAA;oBAAOsG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAO;oBAAApB,QAAA,EAAAqB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7C1G,OAAA;oBAAO6G,IAAI,EAAC,MAAM;oBAACP,SAAS,EAAC,cAAc;oBAACjE,IAAI,EAAC,SAAS;oBAACC,KAAK,EAAE9B,IAAI,CAACG,OAAQ;oBAACmG,QAAQ,EAAE5E,YAAa;oBAAC6E,QAAQ;kBAAA;oBAAA5B,QAAA,EAAAqB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAvB,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChH,CAAC,eACN1G,OAAA;kBAAKsG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BvG,OAAA;oBAAOsG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAQ;oBAAApB,QAAA,EAAAqB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9C1G,OAAA;oBAAO6G,IAAI,EAAC,MAAM;oBAACP,SAAS,EAAC,cAAc;oBAACjE,IAAI,EAAC,UAAU;oBAACC,KAAK,EAAE9B,IAAI,CAACK,QAAS;oBAACiG,QAAQ,EAAE5E;kBAAa;oBAAAiD,QAAA,EAAAqB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAvB,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzG,CAAC;cAAA;gBAAAvB,QAAA,EAAAqB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1G,OAAA;gBAAKsG,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBvG,OAAA;kBAAOsG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAA+C;kBAAApB,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrF1G,OAAA;kBACEsG,SAAS,EAAC,cAAc;kBACxBjE,IAAI,EAAC,aAAa;kBAClBC,KAAK,EAAE9B,IAAI,CAACM,WAAY;kBACxBgG,QAAQ,EAAE5E,YAAa;kBACvB8E,IAAI,EAAC,IAAI;kBACTC,WAAW,EAAC,+DAA+D;kBAC3EF,QAAQ;gBAAA;kBAAA5B,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAvB,QAAA,EAAAqB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEN1G,OAAA;gBAAKsG,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBvG,OAAA;kBAAOsG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAY;kBAAApB,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClD1G,OAAA;kBACEsG,SAAS,EAAC,cAAc;kBACxBjE,IAAI,EAAC,YAAY;kBACjBC,KAAK,EAAE9B,IAAI,CAACO,UAAW;kBACvB+F,QAAQ,EAAE5E,YAAa;kBACvB8E,IAAI,EAAC,GAAG;kBACRD,QAAQ;gBAAA;kBAAA5B,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAvB,QAAA,EAAAqB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEN1G,OAAA;gBAAKsG,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBvG,OAAA;kBAAOsG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAApB,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChD1G,OAAA;kBACEsG,SAAS,EAAC,cAAc;kBACxBjE,IAAI,EAAC,YAAY;kBACjBC,KAAK,EAAE9B,IAAI,CAACQ,UAAW;kBACvB8F,QAAQ,EAAE5E,YAAa;kBACvB8E,IAAI,EAAC,GAAG;kBACRC,WAAW,EAAC;gBAAoC;kBAAA9B,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA;gBAAAvB,QAAA,EAAAqB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACL9E,UAAU,iBAAI5B,OAAA;gBAAKsG,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAa;gBAAApB,QAAA,EAAAqB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAC/E5E,KAAK,iBAAI9B,OAAA;gBAAKsG,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAEzE;cAAK;gBAAAqD,QAAA,EAAAqB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvE1G,OAAA;gBAAKsG,SAAS,EAAC,QAAQ;gBAAAC,QAAA,eACrBvG,OAAA;kBAAQ6G,IAAI,EAAC,QAAQ;kBAACP,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAApB,QAAA,EAAAqB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAvB,QAAA,EAAAqB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAvB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAET;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAvB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAvB,QAAA,EAAAqB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAvB,QAAA,EAAAqB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAvB,QAAA,EAAAqB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACxG,EAAA,CAlWQD,MAAM;EAAA,QAwBIN,WAAW;AAAA;AAAAuH,EAAA,GAxBrBjH,MAAM;AAoWf,eAAeA,MAAM;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}