{"name": "check-types", "version": "11.2.3", "description": "A little library for asserting types and values, with zero dependencies.", "homepage": "https://gitlab.com/philbooth/check-types.js", "bugs": "https://gitlab.com/philbooth/check-types.js/issues", "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://philbooth.me/)", "main": "./src/check-types", "repository": {"type": "git", "url": "https://gitlab.com/philbooth/check-types.js.git"}, "keywords": ["type", "types", "type-check", "type-checking", "duck-typing", "arguments", "parameters", "values", "data", "contract", "assert", "check", "verify", "safe", "safety"], "devDependencies": {"chai": "^4.3.7", "jshint": "^2.13.6", "mocha": "^10.1.0", "please-release-me": "^2.1.4", "uglify-js": "^3.17.4"}, "scripts": {"lint": "jshint ./src/check-types.js", "test": "mocha --ui tdd --reporter spec --colors ./test/check-types.js", "minify": "uglifyjs ./src/check-types.js --compress --mangle --output ./src/check-types.min.js"}, "files": ["src"]}