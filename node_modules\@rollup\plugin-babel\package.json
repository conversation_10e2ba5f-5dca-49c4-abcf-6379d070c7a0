{"name": "@rollup/plugin-babel", "version": "5.3.1", "publishConfig": {"access": "public"}, "description": "Seamless integration between Rollup and Babel.", "license": "MIT", "repository": {"url": "rollup/plugins", "directory": "packages/babel"}, "author": "<PERSON>", "homepage": "https://github.com/rollup/plugins/tree/master/packages/babel#readme", "bugs": "https://github.com/rollup/plugins/issues", "main": "dist/index.js", "module": "dist/index.es.js", "engines": {"node": ">= 10.0.0"}, "scripts": {"build": "rollup -c", "ci:coverage": "nyc pnpm test && nyc report --reporter=text-lcov > coverage.lcov", "ci:lint": "pnpm build && pnpm lint", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "ci:test": "pnpm test -- --verbose", "prebuild": "del-cli dist", "prerelease": "pnpm build", "pretest": "pnpm build", "release": "pnpm plugin:release --workspace-root -- --pkg $npm_package_name", "test": "ava", "test:ts": "tsc types/index.d.ts test/types.ts --noEmit"}, "files": ["dist", "types", "README.md", "LICENSE"], "keywords": ["rollup", "plugin", "rollup-plugin", "babel", "es2015", "es6"], "peerDependencies": {"@babel/core": "^7.0.0", "@types/babel__core": "^7.1.9", "rollup": "^1.20.0||^2.0.0"}, "peerDependenciesMeta": {"@types/babel__core": {"optional": true}}, "dependencies": {"@babel/helper-module-imports": "^7.10.4", "@rollup/pluginutils": "^3.1.0"}, "devDependencies": {"@babel/core": "^7.10.5", "@babel/plugin-external-helpers": "^7.10.4", "@babel/plugin-proposal-decorators": "^7.10.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.10.5", "@babel/preset-env": "^7.10.4", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^10.0.0", "@types/babel__core": "^7.1.9", "rollup": "^2.67.3", "source-map": "^0.7.3"}, "types": "types/index.d.ts", "ava": {"babel": {"compileEnhancements": false}, "files": ["!**/fixtures/**", "!**/helpers/**", "!**/recipes/**", "!**/types.ts"]}, "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/Andarist)"]}