{"name": "@babel/plugin-transform-runtime", "version": "7.28.0", "description": "Externalise references to helpers and builtins, automatically polyfilling your code without polluting globals", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-runtime"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "browser": {"./lib/get-runtime-path/index.js": "./lib/get-runtime-path/browser.js", "./src/get-runtime-path/index.ts": "./src/get-runtime-path/browser.ts"}, "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "babel-plugin-polyfill-corejs2": "^0.4.14", "babel-plugin-polyfill-corejs3": "^0.13.0", "babel-plugin-polyfill-regenerator": "^0.6.5", "semver": "^6.3.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/helpers": "^7.27.6", "@babel/preset-env": "^7.28.0", "@babel/runtime": "^7.27.6", "@babel/runtime-corejs3": "^7.28.0", "babel-plugin-polyfill-corejs3": "^0.13.0", "make-dir": "^2.1.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-runtime", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}