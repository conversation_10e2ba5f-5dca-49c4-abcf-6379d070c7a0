import React, { useEffect, useState } from 'react';
import { supabase } from './supabaseClient';
import EditorialBoardManager from './EditorialBoardManager';
import BlogAdminList from './admin/BlogAdminList';
import UsersAdmin from './admin/UsersAdmin';
import CitationManager from './admin/CitationManager';
import CrawlingStats from './admin/CrawlingStats';
import Modal from 'react-bootstrap/Modal';
import Button from 'react-bootstrap/Button';
import emailjs from 'emailjs-com';
import { Link } from 'react-router-dom';

const STATUS_OPTIONS = [
  'pending',
  'in_review',
  'submitted',
  'published',
  'accepted',
];

export default function AdminPanel({ user }) {
  const [activePage, setActivePage] = useState('submissions');
  const [requests, setRequests] = useState([]);
  const [publications, setPublications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(null);
  const [editPub, setEditPub] = useState(null);
  const [editForm, setEditForm] = useState({});
  const [savingPub, setSavingPub] = useState(false);
  const [editors, setEditors] = useState([]);
  const [assigning, setAssigning] = useState(null);
  const [showEditorDropdown, setShowEditorDropdown] = useState(null);
  const [emailStatusMap, setEmailStatusMap] = useState({});
  const [emailMessageMap, setEmailMessageMap] = useState({});
  const [sendingEmailId, setSendingEmailId] = useState(null);
  const [userFeeMap, setUserFeeMap] = useState({});

  useEffect(() => {
    if (user && user.role === 'admin') {
      if (activePage === 'submissions') fetchRequests();
      if (activePage === 'publications') fetchPublications();
      fetchEditors();
    }
    // eslint-disable-next-line
  }, [user, activePage]);

  async function fetchRequests() {
    setLoading(true);
    const { data } = await supabase
      .from('request')
      .select('*');
    setRequests(data || []);
    setLoading(false);
  }

  async function fetchPublications() {
    setLoading(true);
    const { data } = await supabase
      .from('publications')
      .select('*');
    setPublications(data || []);
    setLoading(false);
  }

  async function fetchEditors() {
    const { data } = await supabase
      .from('users')
      .select('id, full_name, username, email')
      .eq('role', 'editor');
    setEditors(data || []);
  }

  async function handleStatusChange(id, newStatus) {
    setUpdating(id);
    await supabase
      .from('request')
      .update({ status: newStatus })
      .eq('id', id);
    await fetchRequests();
    setUpdating(null);
  }

  async function handleSendToEditor(requestId, editorId) {
    setAssigning(requestId);
    await supabase
      .from('request')
      .update({ editor_id: editorId, sent_to_editor: true })
      .eq('id', requestId);
    await fetchRequests();
    setAssigning(null);
    setShowEditorDropdown(null);
  }

  function openEditModal(pub) {
    setEditPub(pub);
    setEditForm({ ...pub });
  }

  function closeEditModal() {
    setEditPub(null);
    setEditForm({});
  }

  function handleEditChange(e) {
    setEditForm({ ...editForm, [e.target.name]: e.target.value });
  }

  async function handleEditSave() {
    setSavingPub(true);
    await supabase
      .from('publications')
      .update(editForm)
      .eq('id', editPub.id);
    setSavingPub(false);
    closeEditModal();
    fetchPublications();
  }

  const handleOpenEmailModal = (submission) => {
    // This function is no longer needed as email status is handled inline
  };

  const handleCloseEmailModal = () => {
    // This function is no longer needed
  };
  
  const handleEmailStatusChange = (submission, value) => {
    setEmailStatusMap(prev => ({ ...prev, [submission.id]: value }));
    if (value !== 'other') {
      sendEmail(submission, value, '');
    }
  };

  const handleEmailMessageChange = (submission, value) => {
    setEmailMessageMap(prev => ({ ...prev, [submission.id]: value }));
  };

  const EMAILJS_SERVICE_ID = 'service_ixh82mv';
  const EMAILJS_TEMPLATE_ID = 'template_zvja4m4';
  const EMAILJS_USER_ID = 'aalloHCPJqnEy3gLK'; // <-- Replace with your EmailJS public key

  // Helper to fetch and calculate the actual fee for a user
  async function fetchUserFee(submission) {
    // Try to get user by email or username
    let userId = null;
    let userEmail = submission.email;
    let username = submission.username;
    let userRes = null;
    if (userEmail) {
      userRes = await supabase.from('users').select('id').eq('email', userEmail).single();
      if (userRes.data && userRes.data.id) userId = userRes.data.id;
    }
    if (!userId && username) {
      userRes = await supabase.from('users').select('id').eq('username', username).single();
      if (userRes.data && userRes.data.id) userId = userRes.data.id;
    }
    if (!userId) return 40; // fallback
    // Fetch all blogs for this user
    const { data: blogs } = await supabase.from('blog_posts').select('id').eq('author_id', userId);
    if (!blogs || blogs.length === 0) return 40;
    const blogIds = blogs.map(b => b.id);
    // Fetch all likes for these blogs
    const { data: likesData } = await supabase.from('blog_likes').select('post_id').in('post_id', blogIds);
    const totalLikes = likesData ? likesData.length : 0;
    const baseFee = 40;
    const statusPercent = Math.min(totalLikes * 0.01, 100);
    const discount = baseFee * (statusPercent / 100);
    const finalFee = Math.max(baseFee - discount, 0).toFixed(2);
    return finalFee;
  }

  // Fetch and cache fee for all requests on load
  useEffect(() => {
    async function fetchAllFees() {
      const feeMap = {};
      for (const req of requests) {
        feeMap[req.id] = await fetchUserFee(req);
      }
      setUserFeeMap(feeMap);
    }
    if (requests.length > 0) fetchAllFees();
  }, [requests]);

  const sendEmail = async (submission, status, customMessage) => {
    setSendingEmailId(submission.id);
    let toEmail = submission.email;
    // If email is missing, try to fetch from users table using username
    if (!toEmail && submission.username) {
      const { data: userData, error: userError } = await supabase.from('users').select('email').eq('username', submission.username).single();
      if (userData && userData.email) {
        toEmail = userData.email;
      }
    }
    if (!toEmail) {
      alert('This submission does not have a valid email address and could not be found.');
      setSendingEmailId(null);
      return;
    }
    // Use the cached fee if available
    const finalFee = userFeeMap[submission.id] || 40;
    let message = '';
    if (status === 'accepted') {
      message = `Your publication is accepted. You have to pay $${finalFee}.`;
    } else if (status === 'rejected') {
      message = 'You need improvement.';
    } else {
      message = customMessage || 'Status update regarding your publication.';
    }
    // Build the full message string (all details)
    const fullMessage = `Title: ${submission.title}\nSubmitter: ${submission.username || toEmail}\nAuthors: <AUTHORS>
    const templateParams = {
      message: fullMessage,
      to_email: toEmail
    };
    try {
      await emailjs.send(
        EMAILJS_SERVICE_ID,
        EMAILJS_TEMPLATE_ID,
        templateParams,
        EMAILJS_USER_ID
      );
      setSendingEmailId(null);
      setEmailStatusMap(prev => ({ ...prev, [submission.id]: '' }));
      setEmailMessageMap(prev => ({ ...prev, [submission.id]: '' }));
      alert('Email sent successfully!');
    } catch (err) {
      setSendingEmailId(null);
      alert('Failed to send email.');
      console.error('EmailJS error:', err, err?.text);
    }
  };

  if (!user || user.role !== 'admin') {
    return (
      <div className="container mt-5">
        <div className="alert alert-danger">You are not authorized to access the admin panel.</div>
      </div>
    );
  }

  return (
    <div className="container py-5">
      <h2 className="fw-bold mb-4">Admin Panel</h2>
      <div className="mb-4 d-flex gap-3">
        {/* Sidebar */}
        <div className="col-md-3 col-lg-2 mb-4">
          <div className="card shadow-sm border-0 rounded-4">
            <div className="card-body p-0">
              <ul className="nav flex-column nav-pills p-2 gap-2">
                <li className="nav-item">
                  <button className={`nav-link w-100 text-start ${activePage === 'submissions' ? 'active' : ''}`} onClick={() => setActivePage('submissions')}>
                    View Submissions
                  </button>
                </li>
                <li className="nav-item">
                  <button className={`nav-link w-100 text-start ${activePage === 'publications' ? 'active' : ''}`} onClick={() => setActivePage('publications')}>
                    Publications
                  </button>
                </li>
                <li className="nav-item">
                  <button className={`nav-link w-100 text-start ${activePage === 'editorial' ? 'active' : ''}`} onClick={() => setActivePage('editorial')}>
                    Editorial Board Manager
                  </button>
                </li>
                <li className="nav-item">
                  <button className={`nav-link w-100 text-start ${activePage === 'blog' ? 'active' : ''}`} onClick={() => setActivePage('blog')}>
                    Blog Management
                  </button>
                </li>
                <li className="nav-item">
                  <button className={`nav-link w-100 text-start ${activePage === 'users' ? 'active' : ''}`} onClick={() => setActivePage('users')}>
                    Users Management
                  </button>
                </li>
                <li className="nav-item">
                  <button className={`nav-link w-100 text-start ${activePage === 'crawling-stats' ? 'active' : ''}`} onClick={() => setActivePage('crawling-stats')}>
                    Crawling Statistics
                  </button>
                </li>
                <li className="nav-item">
                  <button className={`nav-link w-100 text-start ${activePage === 'citations' ? 'active' : ''}`} onClick={() => setActivePage('citations')}>
                    Citation Manager (Tab)
                  </button>
                </li>
                <li className="nav-item">
                  <Link className="nav-link w-100 text-start" to="/admin/citations">
                    Citation Manager (Page)
                  </Link>
                </li>
                <li className="nav-item">
                  <Link className="nav-link w-100 text-start" to="/admin/editorial-board-applications">
                    Editorial Board Applications
                  </Link>
                </li>
                <li className="nav-item">
                  <a className="nav-link w-100 text-start" href="/admin/events">Event Management</a>
                </li>
                <li className="nav-item">
                  <a className="nav-link w-100 text-start" href="/admin/announcements">Announcements</a>
                </li>
                {/* Add more admin links here */}
              </ul>
            </div>
          </div>
        </div>
        {/* Main Content */}
        <div className="col-md-9 col-lg-10">
          {activePage === 'submissions' && (
            <div className="card shadow-lg border-0 rounded-4">
              <div className="card-body p-4">
                <h3 className="mb-4">All Submissions</h3>
                {loading ? (
                  <div className="text-center my-5"><div className="spinner-border text-primary" role="status"><span className="visually-hidden">Loading...</span></div></div>
                ) : requests.length === 0 ? (
                  <div className="alert alert-info">No submissions found.</div>
                ) : (
                  <div className="table-responsive">
                    <table className="table table-bordered align-middle">
                      <thead className="table-light">
                        <tr>
                          <th>Title</th>
                          <th>Submitter</th>
                          <th>Status</th>
                          <th>Submitted At</th>
                          <th>PDF</th>
                          <th>Send to Editor</th>
                          <th>Send Email</th>
                        </tr>
                      </thead>
                      <tbody>
                        {requests.map(req => (
                          <tr key={req.id}>
                            <td>{req.title}</td>
                            <td>{req.username || req.email || '-'}</td>
                            <td>
                              <select
                                className="form-select"
                                value={req.status || ''}
                                disabled={updating === req.id}
                                onChange={e => handleStatusChange(req.id, e.target.value)}
                              >
                                {STATUS_OPTIONS.map(opt => (
                                  <option value={opt} key={opt}>{opt.charAt(0).toUpperCase() + opt.slice(1).replace('_', ' ')}</option>
                                ))}
                              </select>
                            </td>
                            <td>{req.submitted_at ? new Date(req.submitted_at).toLocaleString() : ''}</td>
                            <td>
                              {req.pdf_url ? (
                                <a href={req.pdf_url} target="_blank" rel="noopener noreferrer" className="btn btn-outline-danger btn-sm">Download PDF</a>
                              ) : (
                                <span className="text-muted">No PDF</span>
                              )}
                            </td>
                            <td>
                              {req.editor_id ? (
                                <span className="badge bg-success">Sent</span>
                              ) : showEditorDropdown === req.id ? (
                                <div className="d-flex align-items-center gap-2">
                                  <select className="form-select form-select-sm" style={{minWidth: 120}} id={`editor-select-${req.id}`}
                                    defaultValue="" onChange={e => handleSendToEditor(req.id, e.target.value)} disabled={assigning === req.id}>
                                    <option value="" disabled>Select Editor</option>
                                    {editors.map(r => (
                                      <option value={r.id} key={r.id}>{r.full_name || r.username || r.email}</option>
                                    ))}
                                  </select>
                                  <button className="btn btn-secondary btn-sm" onClick={() => setShowEditorDropdown(null)}>Cancel</button>
                                </div>
                              ) : (
                                <button className="btn btn-outline-primary btn-sm" onClick={() => setShowEditorDropdown(req.id)} disabled={assigning === req.id}>
                                  Send
                                </button>
                              )}
                            </td>
                            <td>
                              <select
                                className="form-select form-select-sm"
                                value={emailStatusMap[req.id] || ''}
                                onChange={e => handleEmailStatusChange(req, e.target.value)}
                                style={{ minWidth: 120 }}
                              >
                                <option value="">Send Email</option>
                                <option value="accepted">Accepted</option>
                                <option value="rejected">Rejected</option>
                                <option value="other">Other</option>
                              </select>
                              <div className="small text-success mt-1">Fee: ${userFeeMap[req.id] || 40}</div>
                              {emailStatusMap[req.id] === 'other' && (
                                <div className="mt-2 d-flex align-items-center gap-2">
                                  <input
                                    type="text"
                                    className="form-control form-control-sm"
                                    placeholder="Custom message"
                                    value={emailMessageMap[req.id] || ''}
                                    onChange={e => handleEmailMessageChange(req, e.target.value)}
                                    style={{ maxWidth: 200 }}
                                  />
                                  <button
                                    className="btn btn-sm btn-warning"
                                    disabled={sendingEmailId === req.id || !(emailMessageMap[req.id] && emailMessageMap[req.id].trim())}
                                    onClick={() => sendEmail(req, 'other', emailMessageMap[req.id])}
                                  >
                                    {sendingEmailId === req.id ? 'Sending...' : 'Send'}
                                  </button>
                                </div>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          )}
          {activePage === 'publications' && (
            <div className="card shadow-lg border-0 rounded-4">
              <div className="card-body p-4">
                <h3 className="mb-4">All Publications</h3>
                {loading ? (
                  <div className="text-center my-5"><div className="spinner-border text-primary" role="status"><span className="visually-hidden">Loading...</span></div></div>
                ) : publications.length === 0 ? (
                  <div className="alert alert-info">No publications found.</div>
                ) : (
                  <div className="table-responsive">
                    <table className="table table-bordered align-middle">
                      <thead className="table-light">
                        <tr>
                          <th>Title</th>
                          <th>Authors</th>
                          <th>Journal</th>
                          <th>Year</th>
                          <th>PDF</th>
                          <th>Edit</th>
                        </tr>
                      </thead>
                      <tbody>
                        {publications.map(pub => (
                          <tr key={pub.id}>
                            <td>{pub.title}</td>
                            <td>{pub.authors}</td>
                            <td>{pub.journal}</td>
                            <td>{pub.year}</td>
                            <td>{pub.pdf_url ? <a href={pub.pdf_url} target="_blank" rel="noopener noreferrer" className="btn btn-outline-danger btn-sm">Download PDF</a> : <span className="text-muted">No PDF</span>}</td>
                            <td><button className="btn btn-outline-primary btn-sm" onClick={() => openEditModal(pub)}>Edit</button></td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          )}
          {activePage === 'editorial' && <EditorialBoardManager />}
          {activePage === 'blog' && <BlogAdminList />}
          {activePage === 'users' && <UsersAdmin />}
          {activePage === 'crawling-stats' && <CrawlingStats />}
          {activePage === 'citations' && <CitationManager />}
          {/* Edit Publication Modal */}
          {editPub && (
            <div className="modal fade show" style={{display:'block'}} tabIndex="-1" role="dialog">
              <div className="modal-dialog modal-lg" role="document">
                <div className="modal-content">
                  <div className="modal-header">
                    <h5 className="modal-title">Edit Publication</h5>
                    <button type="button" className="btn-close" onClick={closeEditModal}></button>
                  </div>
                  <div className="modal-body">
                    <div className="mb-3">
                      <label className="form-label">Title</label>
                      <input type="text" className="form-control" name="title" value={editForm.title || ''} onChange={handleEditChange} />
                    </div>
                    <div className="mb-3">
                      <label className="form-label">Authors</label>
                      <input type="text" className="form-control" name="authors" value={editForm.authors || ''} onChange={handleEditChange} />
                    </div>
                    <div className="mb-3">
                      <label className="form-label">Journal</label>
                      <input type="text" className="form-control" name="journal" value={editForm.journal || ''} onChange={handleEditChange} />
                    </div>
                    <div className="mb-3">
                      <label className="form-label">Year</label>
                      <input type="number" className="form-control" name="year" value={editForm.year || ''} onChange={handleEditChange} />
                    </div>
                    <div className="mb-3">
                      <label className="form-label">PDF URL</label>
                      <input type="text" className="form-control" name="pdf_url" value={editForm.pdf_url || ''} onChange={handleEditChange} />
                    </div>
                  </div>
                  <div className="modal-footer">
                    <button type="button" className="btn btn-secondary" onClick={closeEditModal}>Cancel</button>
                    <button type="button" className="btn btn-primary" onClick={handleEditSave} disabled={savingPub}>{savingPub ? 'Saving...' : 'Save Changes'}</button>
                  </div>
                </div>
              </div>
              <div className="modal-backdrop fade show"></div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 