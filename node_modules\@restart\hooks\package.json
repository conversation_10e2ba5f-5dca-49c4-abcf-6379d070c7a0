{"name": "@restart/hooks", "version": "0.6.2", "type": "module", "exports": {"./*": {"require": {"types": "./cjs/*.d.ts", "default": "./cjs/*.js"}, "import": {"types": "./lib/*.d.ts", "default": "./lib/*.js"}}}, "files": ["cjs", "lib", "CHANGELOG.md"], "repository": {"type": "git", "url": "git+https://github.com/react-restart/hooks.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/react-restart/hooks/issues"}, "homepage": "https://github.com/react-restart/hooks#readme", "scripts": {"bootstrap": "yarn && yarn --cwd www", "test": "vitest run --coverage", "tdd": "vitest", "build": "rimraf lib cjs && concurrently --names 'esm,cjs' 'yarn build:esm' 'yarn build:cjs' && concurrently --names 'esm types,cjs types' 'yarn build:esm:types' 'yarn build:cjs:types'", "build:esm": "babel src --env-name esm --out-dir lib --extensions '.ts' --ignore='**/*.d.ts'", "build:esm:types": "tsc -p . --emitDeclarationOnly --declaration --outDir lib", "build:cjs": "babel src --out-dir cjs --extensions '.ts'  --ignore='**/*.d.ts' && echo '{\"type\": \"commonjs\"}' > cjs/package.json", "build:cjs:types": "tsc -p . --emitDeclarationOnly --declaration --outDir cjs --module commonjs --moduleResolution node", "deploy-docs": "yarn --cwd www build --prefix-paths && gh-pages -d www/public", "prepublishOnly": "yarn build", "typecheck": "tsc -p . --noEmit", "release": "rollout"}, "jest": {"preset": "@4c", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["./test/setup.js"]}, "prettier": {"singleQuote": true, "semi": false, "trailingComma": "all"}, "publishConfig": {"access": "public"}, "release": {"conventionalCommits": true}, "peerDependencies": {"react": ">=16.8.0"}, "devDependencies": {"@4c/babel-preset": "^10.2.1", "@4c/cli": "^4.0.4", "@4c/rollout": "^4.0.2", "@4c/tsconfig": "^0.4.1", "@babel/cli": "^7.26.4", "@babel/core": "^7.26.0", "@babel/preset-typescript": "^7.26.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.1.0", "@types/lodash": "^4.14.195", "@types/react": "^19.0.2", "@vitest/coverage-v8": "2.1.8", "babel-plugin-transform-rename-import": "^2.3.0", "cherry-pick": "^0.5.0", "codecov": "^3.8.3", "concurrently": "^9.1.2", "eslint": "^8.44.0", "gh-pages": "^3.1.0", "husky": "^4.3.6", "jsdom": "^25.0.1", "lint-staged": "^13.2.3", "mq-polyfill": "^1.1.8", "prettier": "^3.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "rimraf": "^5.0.1", "typescript": "^5.1.6", "vitest": "^2.1.8"}, "dependencies": {"dequal": "^2.0.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}