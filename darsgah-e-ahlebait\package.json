{"name": "darsga<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "dependencies": {"@editorjs/editorjs": "^2.30.8", "@editorjs/embed": "^2.7.6", "@editorjs/header": "^2.8.8", "@editorjs/image": "^2.10.3", "@editorjs/link": "^2.6.2", "@editorjs/list": "^2.0.8", "@supabase/supabase-js": "^2.52.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.10.0", "bootstrap": "^5.3.7", "cheerio": "^1.1.0", "concurrently": "^8.2.2", "editorjs-html": "^4.0.5", "emailjs-com": "^3.2.0", "express": "^4.18.2", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jszip": "^3.10.1", "node-cron": "^4.2.1", "pdfjs-dist": "^2.16.105", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "GENERATE_SOURCEMAP=false react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node basic-server.js", "dev": "concurrently \"npm run server\" \"npm start\"", "start:production": "npm run build && npm run server"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-snap": "^1.23.0"}, "proxy": "http://localhost:3001"}