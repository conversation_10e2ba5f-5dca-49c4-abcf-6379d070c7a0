"use strict";var e=require("@react-pdf-viewer/core");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var o=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,o.get?o:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var n,o=t(require("react")),c=function(){return o.createElement(e.Icon,{size:16},o.createElement("path",{d:"M11.5,5.5v-2C11.5,2.672,12.172,2,13,2s1.5,0.672,1.5,1.5v2 M14.5,11.5v-6C14.5,4.672,15.172,4,16,4\n            c0.828,0,1.5,0.672,1.5,1.5v3 M17.5,13V8.5C17.5,7.672,18.172,7,19,7s1.5,0.672,1.5,1.5v10c0,2.761-2.239,5-5,5h-3.335\n            c-1.712-0.001-3.305-0.876-4.223-2.321C6.22,18.467,4.083,14,4.083,14c-0.378-0.545-0.242-1.292,0.303-1.67\n            c0.446-0.309,1.044-0.281,1.458,0.07L8.5,15.5v-10C8.5,4.672,9.172,4,10,4s1.5,0.672,1.5,1.5v6"}))},r=function(){return r=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var c in t=arguments[n])Object.prototype.hasOwnProperty.call(t,c)&&(e[c]=t[c]);return e},r.apply(this,arguments)};exports.SelectionMode=void 0,(n=exports.SelectionMode||(exports.SelectionMode={})).Hand="Hand",n.Text="Text";var i=function(){return o.createElement(e.Icon,{size:16},o.createElement("path",{d:"M13.675,11.671l2.941-2.941c0.195-0.196,0.195-0.512-0.001-0.707C16.563,7.971,16.5,7.931,16.43,7.906\n            L4.168,3.527C3.908,3.434,3.622,3.57,3.529,3.83c-0.039,0.109-0.039,0.228,0,0.336l4.379,12.262\n            c0.093,0.26,0.379,0.396,0.639,0.303c0.07-0.025,0.133-0.065,0.185-0.117l2.943-2.943l6.146,6.146c0.195,0.195,0.512,0.195,0.707,0\n            l1.293-1.293c0.195-0.195,0.195-0.512,0-0.707L13.675,11.671z"}))},l=function(t){var n=t.children,r=t.mode,l=t.onClick,s=o.useContext(e.LocalizationContext).l10n,u="",a=o.createElement(i,null);switch(r){case exports.SelectionMode.Hand:u=s&&s.selectionMode?s.selectionMode.handTool:"Hand tool",a=o.createElement(c,null);break;case exports.SelectionMode.Text:default:u=s&&s.selectionMode?s.selectionMode.textSelectionTool:"Text selection tool",a=o.createElement(i,null)}return n({icon:a,label:u,onClick:l})},s={left:0,top:8},u=function(t){var n=t.isSelected,c=t.mode,r=t.onClick,i="";switch(c){case exports.SelectionMode.Hand:i="selection-mode__hand-button";break;case exports.SelectionMode.Text:default:i="selection-mode__text-button"}return o.createElement(l,{mode:c,onClick:r},(function(t){return o.createElement(e.Tooltip,{ariaControlsSuffix:"selection-mode-switch",position:e.Position.BottomCenter,target:o.createElement(e.MinimalButton,{ariaLabel:t.label,isSelected:n,testId:i,onClick:t.onClick},t.icon),content:function(){return t.label},offset:s})}))},a=function(e){var t=e.children,n=e.mode,c=e.store,r=n===c.get("selectionMode");return(t||function(e){return o.createElement(u,{isSelected:r,mode:e.mode,onClick:e.onClick})})({isSelected:r,mode:n,onClick:function(){return c.update("selectionMode",n)}})},d=function(t){var n=t.isSelected,c=t.mode,r=t.onClick,i="";switch(c){case exports.SelectionMode.Hand:i="selection-mode__hand-menu";break;case exports.SelectionMode.Text:default:i="selection-mode__text-menu"}return o.createElement(l,{mode:c,onClick:r},(function(t){return o.createElement(e.MenuItem,{checked:n,icon:t.icon,testId:i,onClick:t.onClick},t.label)}))},m=function(e){var t=e.store,n=o.useRef(null),c=o.useState(exports.SelectionMode.Text),r=c[0],i=c[1],l=o.useRef({top:0,left:0,x:0,y:0}),s=function(e){var t=n.current;t&&(t.scrollTop=l.current.top-(e.clientY-l.current.y),t.scrollLeft=l.current.left-(e.clientX-l.current.x))},u=function(){var e=n.current;e&&(e.classList.add("rpv-selection-mode__grab"),e.classList.remove("rpv-selection-mode__grabbing"),document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",u))},a=function(e){var t=n.current;t&&r!==exports.SelectionMode.Text&&(t.classList.remove("rpv-selection-mode__grab"),t.classList.add("rpv-selection-mode__grabbing"),e.preventDefault(),e.stopPropagation(),l.current={left:t.scrollLeft,top:t.scrollTop,x:e.clientX,y:e.clientY},document.addEventListener("mousemove",s),document.addEventListener("mouseup",u))},d=function(e){n.current=e()},m=function(e){i(e)};return o.useEffect((function(){var e=n.current;if(e)return r===exports.SelectionMode.Hand?e.classList.add("rpv-selection-mode__grab"):e.classList.remove("rpv-selection-mode__grab"),e.addEventListener("mousedown",a),function(){e.removeEventListener("mousedown",a)}}),[r]),o.useEffect((function(){return t.subscribe("getPagesContainer",d),t.subscribe("selectionMode",m),function(){t.unsubscribe("getPagesContainer",d),t.unsubscribe("selectionMode",m)}}),[]),o.createElement(o.Fragment,null)};exports.HandToolIcon=c,exports.TextSelectionIcon=i,exports.selectionModePlugin=function(t){var n=o.useMemo((function(){return e.createStore()}),[]),c=function(e){return o.createElement(a,r({},e,{store:n}))};return{install:function(e){n.update("selectionMode",t&&t.selectionMode?t.selectionMode:exports.SelectionMode.Text),n.update("getPagesContainer",e.getPagesContainer)},renderViewer:function(e){var t=e.slot;return t.subSlot&&t.subSlot.children&&(t.subSlot.children=o.createElement(o.Fragment,null,o.createElement(m,{store:n}),t.subSlot.children)),t},SwitchSelectionMode:c,SwitchSelectionModeButton:function(e){return o.createElement(c,{mode:e.mode},(function(e){return o.createElement(u,{isSelected:e.isSelected,mode:e.mode,onClick:function(){e.onClick()}})}))},SwitchSelectionModeMenuItem:function(e){return o.createElement(c,{mode:e.mode},(function(t){return o.createElement(d,{isSelected:t.isSelected,mode:t.mode,onClick:function(){t.onClick(),e.onClick()}})}))}}};
