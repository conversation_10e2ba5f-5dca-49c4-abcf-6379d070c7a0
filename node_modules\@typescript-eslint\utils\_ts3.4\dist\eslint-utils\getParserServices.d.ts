import * as TSESLint from '../ts-eslint';
import { ParserServices } from '../ts-estree';
/**
 * Try to retrieve typescript parser service from context
 */
declare function getParserServices<TMessageIds extends string, TOptions extends readonly unknown[]>(context: Readonly<TSESLint.RuleContext<TMessageIds, TOptions>>, allowWithoutFullTypeInformation?: boolean): ParserServices;
export { getParserServices };
//# sourceMappingURL=getParserServices.d.ts.map
