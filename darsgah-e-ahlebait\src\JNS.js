import React, { useEffect, useState } from 'react';
import { supabase } from './supabaseClient';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { FaBookOpen, FaSearch, FaCalendarAlt, FaUser, FaFlask, FaHashtag, FaListUl, FaUsers, FaArrowRight } from 'react-icons/fa';
import { getJournalStats, formatImpactFactor, debugImpactFactor } from './utils/impactFactor';

function JNS() {
  const [publications, setPublications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [yearFilter, setYearFilter] = useState('');
  const [authorFilter, setAuthorFilter] = useState('');
const [keywordFilter, setKeywordFilter] = useState('');
const [volumeFilter, setVolumeFilter] = useState('');
const [issueFilter, setIssueFilter] = useState('');
  const [years, setYears] = useState([]);
const [authors, setAuthors] = useState([]);
const [keywords, setKeywords] = useState([]);
const [volumes, setVolumes] = useState([]);
const [issues, setIssues] = useState([]);
const [journalStats, setJournalStats] = useState(null);

  useEffect(() => {
    async function fetchPublications() {
      setLoading(true);
      let query = supabase
        .from('publications')
        .select('id, title, authors, year, volume, issue_number, citation_count, external_citation_count, keywords, abstract, featured')
        .eq('journal', 'JNS')
        .order('created_at', { ascending: false });
      const { data, error } = await query;

      // Debug: Check what we actually got from database
      console.log('🔍 JNS Database Query Result:');
      console.log('   Error:', error);
      console.log('   Data:', data);
      console.log('   Data length:', data ? data.length : 0);

      // Also check all publications to see journal names
      const { data: allPubs } = await supabase
        .from('publications')
        .select('id, title, journal, citation_count, external_citation_count');

      console.log('📚 All publications in database:');
      console.log('   Total publications:', allPubs ? allPubs.length : 0);
      if (allPubs) {
        const journalCounts = {};
        allPubs.forEach(pub => {
          const journal = pub.journal || 'NULL';
          journalCounts[journal] = (journalCounts[journal] || 0) + 1;
        });
        console.log('   Publications by journal:', journalCounts);
      }

      if (!error && data) {
        setPublications(data);

        // Calculate journal statistics (now async to fetch external citations)
        const stats = await getJournalStats(data, 'JNS', supabase);
        console.log('📊 Final stats for JNS:', stats);
        setJournalStats(stats);

        // If stats are 0, fetch all publications and calculate manually
        if (stats.totalCitations === 0) {
          console.log('⚠️ JNS Stats showing 0, fetching all publications...');
          const { data: allPubs } = await supabase
            .from('publications')
            .select('id, journal, citation_count, external_citation_count')
            .eq('journal', 'JNS');

          if (allPubs && allPubs.length > 0) {
            const internalTotal = allPubs.reduce((sum, pub) => sum + (pub.citation_count || 0), 0);
            const externalTotal = allPubs.reduce((sum, pub) => sum + (pub.external_citation_count || 0), 0);
            const totalCitations = internalTotal + externalTotal;

            console.log(`📊 Manual JNS calculation: ${allPubs.length} pubs, ${totalCitations} citations`);
            setJournalStats({
              ...stats,
              totalCitations: totalCitations,
              totalPublications: allPubs.length,
              internalCitations: internalTotal,
              externalCitations: externalTotal
            });
          }
        }

        // Debug impact factor calculation
        await debugImpactFactor(data, 'JNS', supabase);
        
        // Extract unique years, authors, keywords, volumes, issues for filters
        setYears(Array.from(new Set(data.map(pub => pub.year))).sort((a, b) => b - a));
        setAuthors(Array.from(new Set(data.flatMap(pub => pub.authors ? pub.authors.split(',').map(a => a.trim()) : []))).sort());
        setKeywords(Array.from(new Set(data.flatMap(pub => pub.keywords ? pub.keywords.split(',').map(k => k.trim()) : []))).sort());
        setVolumes(Array.from(new Set(data.map(pub => pub.volume).filter(Boolean))).sort((a, b) => b - a));
        setIssues(Array.from(new Set(data.map(pub => pub.issue_number).filter(Boolean))).sort((a, b) => b - a));
      }
      setLoading(false);
    }
    fetchPublications();
  }, []);

  // Filter publications
  const filtered = publications.filter(pub => {
    const matchesYear = !yearFilter || pub.year === Number(yearFilter);
    const matchesSearch =
      search === '' ||
      pub.title.toLowerCase().includes(search.toLowerCase()) ||
      (pub.authors && pub.authors.toLowerCase().includes(search.toLowerCase()));
    const matchesAuthor = !authorFilter || (pub.authors && pub.authors.split(',').map(a => a.trim()).includes(authorFilter));
    const matchesKeyword = !keywordFilter || (pub.keywords && pub.keywords.split(',').map(k => k.trim()).includes(keywordFilter));
    const matchesVolume = !volumeFilter || pub.volume === volumeFilter;
    const matchesIssue = !issueFilter || pub.issue_number === issueFilter;
    return matchesYear && matchesSearch && matchesAuthor && matchesKeyword && matchesVolume && matchesIssue;
  });

  // Define featured and recent arrays
  const featured = filtered.filter(pub => pub.featured).slice(0, 6);
  const recent = filtered.slice(0, 6);

  return (
    <div className="journal-page bg-light min-vh-100">
      <Helmet>
        <title>JNS - darsgah-e-ahlebait</title>
        <meta name="description" content="Journal of Natural Sciences (JNS) - A leading journal in natural sciences research and innovation." />
      </Helmet>
      {/* Hero Section */}
      <section className="jns-hero-section position-relative mb-5" style={{width: '100vw', position: 'relative', left: '50%', right: '50%', marginLeft: '-50vw', marginRight: '-50vw', background: 'linear-gradient(120deg, #0d1b4d 0%, #1976d2 100%)', color: '#fff', minHeight: '370px', padding: '56px 0'}}>
        <div className="d-flex flex-column flex-md-row align-items-center justify-content-between gap-4 px-4 px-md-5" style={{maxWidth: '1400px', margin: '0 auto'}}>
            <div className="flex-fill">
              <h1 className="display-4 fw-bold mb-2" style={{color: '#fff'}}>Journal of Natural Sciences (JNS)</h1>
              <div className="mb-2 d-flex flex-wrap gap-3 align-items-center">
                <span className="badge bg-light text-primary fs-6">ISSN: 2308-5061</span>
                <span className="badge bg-light text-primary fs-6">
                  Citations: {journalStats ? journalStats.totalCitations : 'Calculating...'}
                </span>
              </div>
              <p className="lead mb-2" style={{color: '#fff'}}>A premier journal for research in natural sciences, publishing high-quality, peer-reviewed articles across all areas of natural sciences.</p>
              <div className="mb-2" style={{color: '#fff'}}>
                <b>Aims & Scope:</b> JNS aims to promote the advancement of natural sciences by publishing original research, reviews, and perspectives in physics, chemistry, biology, and related fields.
              </div>
              <div className="d-flex flex-wrap gap-2 mt-3">
                <Link to="/editorial-board/jns" className="btn btn-outline-primary rounded-pill px-4"><FaUsers className="me-2" />Editorial Board</Link>
                <a href="#submission-guidelines" className="btn btn-outline-info rounded-pill px-4"><FaListUl className="me-2" />Submission Guidelines</a>
                <a href="#contact" className="btn btn-outline-secondary rounded-pill px-4"><FaArrowRight className="me-2" />Contact</a>
              </div>
            </div>
            <div className="d-none d-md-block flex-shrink-0">
              <img src="https://cdn-icons-png.flaticon.com/512/2721/2721297.png" alt="Natural Science" style={{width: '160px', opacity: 0.95}} />
            </div>
          </div>
        </section>
      {/* Featured Publications */}
      <section className="container mb-5">
        <h3 className="fw-bold mb-3 text-primary">Featured Publications</h3>
        {loading ? (
          <div className="text-center my-4">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : featured.length === 0 ? (
          <div className="alert alert-info text-center">No featured publications found for JNS.</div>
        ) : (
          <div className="row g-4 mb-4">
            {featured.map(pub => (
              <div className="col-12 col-md-6 col-lg-4" key={pub.id}>
                <div className="card h-100 border-0 rounded-5 pub-card-featured position-relative bg-white" style={{boxShadow: '0 8px 32px rgba(13,110,253,0.15)', background: 'linear-gradient(120deg, #e0f0ff 0%, #eaf6ff 100%)', border: '2.5px solid #0d6efd', minHeight: '340px'}}>
                  <span className="badge bg-primary fs-6 shadow-lg position-absolute" style={{top: '0', right: '0', zIndex: 2, borderRadius: '1rem 1rem 1rem 0', padding: '0.6em 1.2em', transform: 'translateY(-50%) translateX(-20%)'}}>★ Featured</span>
                  <div className="card-body pt-5 p-4 d-flex flex-column justify-content-between">
          <div>
                      <h5 className="fw-bold mb-2 text-primary" style={{fontSize: '1.25rem'}}>
                        <Link to={`/publication/${pub.id}`} className="text-decoration-none text-primary">{pub.title}</Link>
                      </h5>
                      <div className="mb-2 text-secondary" style={{fontSize: '1.05rem'}}><FaUser className="me-2 text-primary" />{pub.authors}</div>
                      <div className="mb-2 d-flex flex-wrap gap-2 align-items-center">
                        <span className="badge bg-primary bg-opacity-10 text-primary fw-normal"><FaCalendarAlt className="me-1" />{pub.year}</span>
                        {pub.volume && <span className="badge bg-info bg-opacity-10 text-info fw-normal"><FaBookOpen className="me-1" />Vol. {pub.volume}</span>}
                        {pub.issue_number && <span className="badge bg-success bg-opacity-10 text-success fw-normal">Issue {pub.issue_number}</span>}
                        {pub.citation_count !== undefined && <span className="badge bg-warning bg-opacity-10 text-warning fw-normal"><FaHashtag className="me-1" />{pub.citation_count} Citations</span>}
                      </div>
                      {pub.keywords && <div className="mb-2"><b>Keywords:</b> <span className="text-primary">{pub.keywords}</span></div>}
                      {pub.abstract && <div className="mb-2"><b>Abstract:</b> <span className="text-muted">{pub.abstract.slice(0, 120)}{pub.abstract.length > 120 ? '...' : ''}</span></div>}
                    </div>
                    <div className="mt-2">
                      <Link to={`/publication/${pub.id}`} className="btn btn-primary btn-lg rounded-pill px-4">View Details</Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        <style>{`
          .pub-card-featured {
            transition: box-shadow 0.22s, transform 0.22s;
          }
          .pub-card-featured:hover {
            box-shadow: 0 12px 40px rgba(13,110,253,0.22);
            transform: translateY(-6px) scale(1.035);
          }
        `}</style>
      </section>
      {/* Recent Publications */}
      <section className="container mb-5">
        <h3 className="fw-bold mb-3 text-primary">Recent Publications</h3>
        {loading ? (
          <div className="text-center my-4">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : recent.length === 0 ? (
          <div className="alert alert-info text-center">No recent publications found for JNS.</div>
        ) : (
          <div className="row g-4 mb-4">
            {recent.map(pub => (
              <div className="col-12 col-md-6 col-lg-4" key={pub.id}>
                <div className="card h-100 border-primary border-2 rounded-4 pub-card-recent position-relative bg-white" style={{boxShadow: '0 4px 18px rgba(13,110,253,0.10)', border: '2px solid #0d6efd', minHeight: '320px'}}>
                  <span className="badge bg-primary fs-6 shadow position-absolute" style={{top: '0', left: '0', zIndex: 2, borderRadius: '1rem 1rem 0 1rem', padding: '0.5em 1.1em', transform: 'translateY(-50%) translateX(20%)'}}>New</span>
                  <div className="card-body pt-5 p-4 d-flex flex-column justify-content-between">
                    <div>
                      <h5 className="fw-bold mb-2 text-primary" style={{fontSize: '1.13rem'}}>
                        <Link to={`/publication/${pub.id}`} className="text-decoration-none text-primary">{pub.title}</Link>
                      </h5>
                      <div className="mb-2 text-secondary" style={{fontSize: '1.01rem'}}><FaUser className="me-2 text-primary" />{pub.authors}</div>
                      <div className="mb-2 d-flex flex-wrap gap-2 align-items-center">
                        <span className="badge bg-primary bg-opacity-10 text-primary fw-normal"><FaCalendarAlt className="me-1" />{pub.year}</span>
                        {pub.volume && <span className="badge bg-info bg-opacity-10 text-info fw-normal"><FaBookOpen className="me-1" />Vol. {pub.volume}</span>}
                        {pub.issue_number && <span className="badge bg-success bg-opacity-10 text-success fw-normal">Issue {pub.issue_number}</span>}
                        {pub.citation_count !== undefined && <span className="badge bg-warning bg-opacity-10 text-warning fw-normal"><FaHashtag className="me-1" />{pub.citation_count} Citations</span>}
                      </div>
                      {pub.keywords && <div className="mb-2"><b>Keywords:</b> <span className="text-primary">{pub.keywords}</span></div>}
                      {pub.abstract && <div className="mb-2"><b>Abstract:</b> <span className="text-muted">{pub.abstract.slice(0, 120)}{pub.abstract.length > 120 ? '...' : ''}</span></div>}
                    </div>
                    <div className="mt-2">
                      <Link to={`/publication/${pub.id}`} className="btn btn-outline-primary btn-sm rounded-pill">View Details</Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        <style>{`
          .pub-card-recent {
            transition: box-shadow 0.18s, transform 0.18s;
          }
          .pub-card-recent:hover {
            box-shadow: 0 8px 32px rgba(13,110,253,0.18);
            transform: translateY(-4px) scale(1.025);
          }
        `}</style>
      </section>
      {/* Filters */}
      <section className="container mb-4">
        <div className="row g-3 align-items-center justify-content-between mb-2">
          <div className="col-12 col-md-4 mb-2 mb-md-0">
            <div className="input-group">
              <span className="input-group-text bg-white border-end-0"><FaSearch className="text-secondary" /></span>
              <input
                type="text"
                className="form-control border-start-0"
                placeholder="Search by title or author..."
                value={search}
                onChange={e => setSearch(e.target.value)}
              />
            </div>
          </div>
          <div className="col-6 col-md-2">
            <select className="form-select" value={yearFilter} onChange={e => setYearFilter(e.target.value)}>
              <option value="">All Years</option>
              {years.map(y => (
                <option key={y} value={y}>{y}</option>
              ))}
            </select>
          </div>
          <div className="col-6 col-md-2">
            <select className="form-select" value={authorFilter} onChange={e => setAuthorFilter(e.target.value)}>
              <option value="">All Authors</option>
              {authors.map(a => (
                <option key={a} value={a}>{a}</option>
              ))}
            </select>
          </div>
          <div className="col-6 col-md-2">
            <select className="form-select" value={keywordFilter} onChange={e => setKeywordFilter(e.target.value)}>
              <option value="">All Keywords</option>
              {keywords.map(k => (
                <option key={k} value={k}>{k}</option>
              ))}
            </select>
          </div>
          <div className="col-6 col-md-1">
            <select className="form-select" value={volumeFilter} onChange={e => setVolumeFilter(e.target.value)}>
              <option value="">Volume</option>
              {volumes.map(v => (
                <option key={v} value={v}>{v}</option>
              ))}
            </select>
          </div>
          <div className="col-6 col-md-1">
            <select className="form-select" value={issueFilter} onChange={e => setIssueFilter(e.target.value)}>
              <option value="">Issue</option>
              {issues.map(i => (
                <option key={i} value={i}>{i}</option>
              ))}
            </select>
          </div>
        </div>
        <div className="row">
          <div className="col-12 text-end">
            <span className="badge bg-primary bg-opacity-10 text-primary fs-6">{filtered.length} Result{filtered.length !== 1 ? 's' : ''}</span>
          </div>
        </div>
      </section>
      {/* All Publications */}
      <section className="container mb-5">
        <h3 className="fw-bold mb-3 text-primary">All Publications</h3>
        {loading ? (
          <div className="text-center my-4">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : filtered.length === 0 ? (
          <div className="alert alert-info text-center">No publications found for JNS.</div>
        ) : (
          <div className="row g-4 mb-4">
            {filtered.map(pub => (
              <div className="col-12 col-md-6 col-lg-4" key={pub.id}>
                <div className="card h-100 shadow-sm border-0 rounded-4 pub-card position-relative bg-white">
                  <div className="card-body p-4 d-flex flex-column justify-content-between">
                    <div>
                      <h5 className="fw-bold mb-2 text-primary" style={{fontSize: '1.18rem'}}>
                        <Link to={`/publication/${pub.id}`} className="text-decoration-none text-primary">{pub.title}</Link>
                      </h5>
                      <div className="mb-2 text-secondary" style={{fontSize: '0.98rem'}}><FaUser className="me-2 text-primary" />{pub.authors}</div>
                      <div className="mb-2 d-flex flex-wrap gap-2 align-items-center">
                        <span className="badge bg-primary bg-opacity-10 text-primary fw-normal"><FaCalendarAlt className="me-1" />{pub.year}</span>
                        {pub.volume && <span className="badge bg-info bg-opacity-10 text-info fw-normal"><FaBookOpen className="me-1" />Vol. {pub.volume}</span>}
                        {pub.issue_number && <span className="badge bg-success bg-opacity-10 text-success fw-normal">Issue {pub.issue_number}</span>}
                        {pub.citation_count !== undefined && <span className="badge bg-warning bg-opacity-10 text-warning fw-normal"><FaHashtag className="me-1" />{pub.citation_count} Citations</span>}
                      </div>
                      {pub.keywords && <div className="mb-2"><b>Keywords:</b> <span className="text-primary">{pub.keywords}</span></div>}
                      {pub.abstract && <div className="mb-2"><b>Abstract:</b> <span className="text-muted">{pub.abstract.slice(0, 120)}{pub.abstract.length > 120 ? '...' : ''}</span></div>}
                    </div>
                    <div className="mt-2">
                      <Link to={`/publication/${pub.id}`} className="btn btn-outline-primary btn-sm rounded-pill">View Details</Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </section>
    </div>
  );
}

export default JNS; 