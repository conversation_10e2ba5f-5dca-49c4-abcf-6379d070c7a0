"use strict";var e=require("@react-pdf-viewer/core");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var a=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,a.get?a:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var n=t(require("react")),a=function(){return n.createElement(e.Icon,{size:16},n.createElement("path",{d:"M2.32,2.966h19.452c0.552,0.001,1,0.449,0.999,1.001c0,0.182-0.05,0.36-0.144,0.516L12.9,20.552\n            c-0.286,0.472-0.901,0.624-1.373,0.338c-0.138-0.084-0.254-0.2-0.338-0.338L1.465,4.483C1.179,4.01,1.331,3.396,1.804,3.11\n            C1.96,3.016,2.138,2.966,2.32,2.966z"}))},r=function(){return n.createElement(e.Icon,{size:16},n.createElement("path",{d:"M0.541,5.627L11.666,18.2c0.183,0.207,0.499,0.226,0.706,0.043c0.015-0.014,0.03-0.028,0.043-0.043\n            L23.541,5.627"}))},o=function(){return o=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},o.apply(this,arguments)},i=function(t){var a=n.useState(t.get("currentPage")||0),r=a[0],o=a[1],i=function(e){o(e)};return e.useIsomorphicLayoutEffect((function(){return t.subscribe("currentPage",i),function(){t.unsubscribe("currentPage",i)}}),[]),{currentPage:r}},u=function(e){var t=n.useState(e.get("numberOfPages")||0),a=t[0],r=t[1],o=function(e){r(e)};return n.useEffect((function(){return e.subscribe("numberOfPages",o),function(){e.unsubscribe("numberOfPages",o)}}),[]),{numberOfPages:a}},c=function(t){var a=t.store,r=n.useContext(e.LocalizationContext).l10n,o=n.useState("1"),c=o[0],l=o[1],s=i(a).currentPage,g=u(a).numberOfPages;n.useEffect((function(){return l("".concat(s+1))}),[s]);var f=function(e){var t=a.get("jumpToPage");t&&t(e)},m=r&&r.pageNavigation?r.pageNavigation.enterPageNumber:"Enter a page number";return n.createElement("span",{className:"rpv-page-navigation__current-page-input"},n.createElement(e.TextBox,{ariaLabel:m,testId:"page-navigation__current-page-input",type:"text",value:c,onChange:l,onKeyDown:function(e){switch(e.key){case"ArrowUp":(a=s-1)>=0&&(l("".concat(a+1)),f(a));break;case"ArrowDown":(n=s+1)<g&&(l("".concat(n+1)),f(n));break;case"Enter":t=parseInt(c,10),""===c||t<1||t>g?l("".concat(s+1)):f(t-1)}var t,n,a}}))},l=function(t){var a=t.children,r=t.doc,o=e.useIsMounted(),i=n.useState({loading:!0,labels:[]}),u=i[0],c=i[1];return n.useEffect((function(){r.getPageLabels().then((function(e){o.current&&c({loading:!1,labels:e||[]})}))}),[r.loadingTask.docId]),u.loading?n.createElement(n.Fragment,null):a(u.labels)},s=function(e){var t=e.children,a=e.store,r=function(e){var t=n.useState(e.get("doc")),a=t[0],r=t[1],o=function(e){r(e)};return n.useEffect((function(){return e.subscribe("doc",o),function(){e.unsubscribe("doc",o)}}),[]),a}(a),o=i(a).currentPage,c=u(a).numberOfPages,s=t||function(e){return n.createElement(n.Fragment,null,e.currentPage+1)};return r?n.createElement(l,{doc:r},(function(e){var t=e.length===c&&c>0?e[o]:"";return s({currentPage:o,numberOfPages:c,pageLabel:t})})):n.createElement(n.Fragment,null)},g=function(){return n.createElement(e.Icon,{size:16},n.createElement("path",{d:"M21.783,21.034H2.332c-0.552,0-1-0.448-1-1c0-0.182,0.05-0.361,0.144-0.517L11.2,3.448\n            c0.286-0.472,0.901-0.624,1.373-0.338c0.138,0.084,0.254,0.2,0.338,0.338l9.726,16.069c0.286,0.473,0.134,1.087-0.339,1.373\n            C22.143,20.984,21.965,21.034,21.783,21.034z"}))},f={left:0,top:8},m=function(t){var a=t.isDisabled,r=t.onClick,o=n.useContext(e.LocalizationContext).l10n,i=o&&o.pageNavigation?o.pageNavigation.goToFirstPage:"First page";return n.createElement(e.Tooltip,{ariaControlsSuffix:"page-navigation-first",position:e.Position.BottomCenter,target:n.createElement(e.MinimalButton,{ariaLabel:i,isDisabled:a,testId:"page-navigation__first-button",onClick:r},n.createElement(g,null)),content:function(){return i},offset:f})},p=function(e){var t=e.children,a=e.store;return(t||function(e){return n.createElement(m,{isDisabled:e.isDisabled,onClick:e.onClick})})({isDisabled:0===i(a).currentPage,onClick:function(){var e=a.get("jumpToPage");e&&e(0)}})},v=function(t){var a=t.isDisabled,r=t.onClick,o=n.useContext(e.LocalizationContext).l10n,i=o&&o.pageNavigation?o.pageNavigation.goToFirstPage:"First page";return n.createElement(e.MenuItem,{icon:n.createElement(g,null),isDisabled:a,testId:"page-navigation__first-menu",onClick:r},i)},d={left:0,top:8},P=function(t){var r=t.isDisabled,o=t.onClick,i=n.useContext(e.LocalizationContext).l10n,u=i&&i.pageNavigation?i.pageNavigation.goToLastPage:"Last page";return n.createElement(e.Tooltip,{ariaControlsSuffix:"page-navigation-last",position:e.Position.BottomCenter,target:n.createElement(e.MinimalButton,{ariaLabel:u,isDisabled:r,testId:"page-navigation__last-button",onClick:o},n.createElement(a,null)),content:function(){return u},offset:d})},b=function(e){var t=e.children,a=e.store,r=i(a).currentPage,o=u(a).numberOfPages;return(t||function(e){return n.createElement(P,{isDisabled:e.isDisabled,onClick:e.onClick})})({isDisabled:r+1>=o,onClick:function(){var e=a.get("jumpToPage");e&&e(o-1)}})},E=function(t){var r=t.isDisabled,o=t.onClick,i=n.useContext(e.LocalizationContext).l10n,u=i&&i.pageNavigation?i.pageNavigation.goToLastPage:"Last page";return n.createElement(e.MenuItem,{icon:n.createElement(a,null),isDisabled:r,testId:"page-navigation__last-menu",onClick:o},u)},C={left:0,top:8},D=function(t){var a=t.isDisabled,o=t.onClick,i=n.useContext(e.LocalizationContext).l10n,u=i&&i.pageNavigation?i.pageNavigation.goToNextPage:"Next page";return n.createElement(e.Tooltip,{ariaControlsSuffix:"page-navigation-next",position:e.Position.BottomCenter,target:n.createElement(e.MinimalButton,{ariaLabel:u,isDisabled:a,testId:"page-navigation__next-button",onClick:o},n.createElement(r,null)),content:function(){return u},offset:C})},k=function(e){var t=e.children,a=e.store;return(t||function(e){return n.createElement(D,{onClick:e.onClick,isDisabled:e.isDisabled})})({isDisabled:i(a).currentPage+1>=u(a).numberOfPages,onClick:function(){var e=a.get("jumpToNextPage");e&&e()}})},T=function(t){var a=t.isDisabled,o=t.onClick,i=n.useContext(e.LocalizationContext).l10n,u=i&&i.pageNavigation?i.pageNavigation.goToNextPage:"Next page";return n.createElement(e.MenuItem,{icon:n.createElement(r,null),isDisabled:a,testId:"page-navigation__next-menu",onClick:o},u)},x=function(){return n.createElement(e.Icon,{size:16},n.createElement("path",{d:"M23.535,18.373L12.409,5.8c-0.183-0.207-0.499-0.226-0.706-0.043C11.688,5.77,11.674,5.785,11.66,5.8\n            L0.535,18.373"}))},L={left:0,top:8},N=function(t){var a=t.isDisabled,r=t.onClick,o=n.useContext(e.LocalizationContext).l10n,i=o&&o.pageNavigation?o.pageNavigation.goToPreviousPage:"Previous page";return n.createElement(e.Tooltip,{ariaControlsSuffix:"page-navigation-previous",position:e.Position.BottomCenter,target:n.createElement(e.MinimalButton,{ariaLabel:i,isDisabled:a,testId:"page-navigation__previous-button",onClick:r},n.createElement(x,null)),content:function(){return i},offset:L})},j=function(e){var t=e.store;return(e.children||function(e){return n.createElement(N,{isDisabled:e.isDisabled,onClick:e.onClick})})({isDisabled:i(t).currentPage<=0,onClick:function(){var e=t.get("jumpToPreviousPage");e&&e()}})},I=function(t){var a=t.isDisabled,r=t.onClick,o=n.useContext(e.LocalizationContext).l10n,i=o&&o.pageNavigation?o.pageNavigation.goToPreviousPage:"Previous page";return n.createElement(e.MenuItem,{icon:n.createElement(x,null),isDisabled:a,testId:"page-navigation__previous-menu",onClick:r},i)},h=function(e){var t=e.children,a=e.store,r=u(a).numberOfPages;return t?t({numberOfPages:r}):n.createElement(n.Fragment,null,r)},y=function(t){var a=t.containerRef,r=t.numPages,o=t.store,u=i(o).currentPage,c=n.useRef(u);c.current=u;var l=n.useRef(!1),s=function(){l.current=!0},g=function(){l.current=!1},f=function(t){var n=a.current,i=l.current||document.activeElement&&n.contains(document.activeElement);if(n&&i){var u,s,g=t.altKey&&"ArrowDown"===t.key||!t.shiftKey&&!t.altKey&&"PageDown"===t.key,f=t.altKey&&"ArrowUp"===t.key||!t.shiftKey&&!t.altKey&&"PageUp"===t.key;if(g)return t.preventDefault(),u=o.get("jumpToPage"),s=c.current+1,void(u&&s<r&&u(s));if(f)return t.preventDefault(),void function(){var e=o.get("jumpToPage"),t=c.current-1;e&&t>=0&&e(t)}();if(e.isMac()?t.metaKey&&!t.ctrlKey:t.altKey)switch(t.key){case"ArrowLeft":t.preventDefault(),function(){var e=o.get("jumpToPreviousDestination");e&&e()}();break;case"ArrowRight":t.preventDefault(),function(){var e=o.get("jumpToNextDestination");e&&e()}()}}};return n.useEffect((function(){var e=a.current;if(e)return document.addEventListener("keydown",f),e.addEventListener("mouseenter",s),e.addEventListener("mouseleave",g),function(){document.removeEventListener("keydown",f),e.removeEventListener("mouseenter",s),e.removeEventListener("mouseleave",g)}}),[a.current]),n.createElement(n.Fragment,null)};exports.DownArrowIcon=a,exports.NextIcon=r,exports.PreviousIcon=x,exports.UpArrowIcon=g,exports.pageNavigationPlugin=function(t){var a=n.useMemo((function(){return Object.assign({},{enableShortcuts:!0},t)}),[]),r=n.useMemo((function(){return e.createStore()}),[]),i=function(e){return n.createElement(p,o({},e,{store:r}))},u=function(e){return n.createElement(b,o({},e,{store:r}))},l=function(e){return n.createElement(k,o({},e,{store:r}))},g=function(e){return n.createElement(j,o({},e,{store:r}))};return{install:function(e){r.update("jumpToDestination",e.jumpToDestination),r.update("jumpToNextDestination",e.jumpToNextDestination),r.update("jumpToNextPage",e.jumpToNextPage),r.update("jumpToPage",e.jumpToPage),r.update("jumpToPreviousDestination",e.jumpToPreviousDestination),r.update("jumpToPreviousPage",e.jumpToPreviousPage)},renderViewer:function(e){var t=e.slot;if(!a.enableShortcuts)return t;var i={children:n.createElement(n.Fragment,null,n.createElement(y,{containerRef:e.containerRef,numPages:e.doc.numPages,store:r}),t.children)};return o(o({},t),i)},onDocumentLoad:function(e){r.update("doc",e.doc),r.update("numberOfPages",e.doc.numPages)},onViewerStateChange:function(e){return r.update("currentPage",e.pageIndex),e},jumpToNextPage:function(){var e=r.get("jumpToNextPage");e&&e()},jumpToPage:function(e){var t=r.get("jumpToPage");t&&t(e)},jumpToPreviousPage:function(){var e=r.get("jumpToPreviousPage");e&&e()},CurrentPageInput:function(){return n.createElement(c,{store:r})},CurrentPageLabel:function(e){return n.createElement(s,o({},e,{store:r}))},GoToFirstPage:i,GoToFirstPageButton:function(){return n.createElement(i,null,(function(e){return n.createElement(m,o({},e))}))},GoToFirstPageMenuItem:function(e){return n.createElement(i,null,(function(t){return n.createElement(v,{isDisabled:t.isDisabled,onClick:function(){t.onClick(),e.onClick()}})}))},GoToLastPage:u,GoToLastPageButton:function(){return n.createElement(u,null,(function(e){return n.createElement(P,o({},e))}))},GoToLastPageMenuItem:function(e){return n.createElement(u,null,(function(t){return n.createElement(E,{isDisabled:t.isDisabled,onClick:function(){t.onClick(),e.onClick()}})}))},GoToNextPage:l,GoToNextPageButton:function(){return n.createElement(l,null,(function(e){return n.createElement(D,o({},e))}))},GoToNextPageMenuItem:function(e){return n.createElement(l,null,(function(t){return n.createElement(T,{isDisabled:t.isDisabled,onClick:function(){t.onClick(),e.onClick()}})}))},GoToPreviousPage:g,GoToPreviousPageButton:function(){return n.createElement(g,null,(function(e){return n.createElement(N,o({},e))}))},GoToPreviousPageMenuItem:function(e){return n.createElement(g,null,(function(t){return n.createElement(I,{isDisabled:t.isDisabled,onClick:function(){t.onClick(),e.onClick()}})}))},NumberOfPages:function(e){return n.createElement(h,o({},e,{store:r}))}}};
