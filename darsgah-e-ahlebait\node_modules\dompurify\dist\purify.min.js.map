{"version": 3, "file": "purify.min.js", "sources": ["../src/utils.ts", "../src/tags.ts", "../src/attrs.ts", "../src/regexp.ts", "../src/purify.ts"], "sourcesContent": ["const {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayLastIndexOf = unapply(Array.prototype.lastIndexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\nconst arraySplice = unapply(Array.prototype.splice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\n/**\n * Creates a new function that calls the given function with a specified thisArg and arguments.\n *\n * @param func - The function to be wrapped and called.\n * @returns A new function that calls the given function with a specified thisArg and arguments.\n */\nfunction unapply<T>(\n  func: (thisArg: any, ...args: any[]) => T\n): (thisArg: any, ...args: any[]) => T {\n  return (thisArg: any, ...args: any[]): T => {\n    if (thisArg instanceof RegExp) {\n      thisArg.lastIndex = 0;\n    }\n\n    return apply(func, thisArg, args);\n  };\n}\n\n/**\n * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n *\n * @param func - The constructor function to be wrapped and called.\n * @returns A new function that constructs an instance of the given constructor function with the provided arguments.\n */\nfunction unconstruct<T>(func: (...args: any[]) => T): (...args: any[]) => T {\n  return (...args: any[]): T => construct(func, args);\n}\n\n/**\n * Add properties to a lookup table\n *\n * @param set - The set to which elements will be added.\n * @param array - The array containing elements to be added to the set.\n * @param transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n * @returns The modified set with added elements.\n */\nfunction addToSet(\n  set: Record<string, any>,\n  array: readonly any[],\n  transformCaseFunc: ReturnType<typeof unapply<string>> = stringToLowerCase\n): Record<string, any> {\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          (array as any[])[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/**\n * Clean up an array to harden against CSPP\n *\n * @param array - The array to be cleaned.\n * @returns The cleaned version of the array\n */\nfunction cleanArray<T>(array: T[]): Array<T | null> {\n  for (let index = 0; index < array.length; index++) {\n    const isPropertyExist = objectHasOwnProperty(array, index);\n\n    if (!isPropertyExist) {\n      array[index] = null;\n    }\n  }\n\n  return array;\n}\n\n/**\n * Shallow clone an object\n *\n * @param object - The object to be cloned.\n * @returns A new object that copies the original.\n */\nfunction clone<T extends Record<string, any>>(object: T): T {\n  const newObject = create(null);\n\n  for (const [property, value] of entries(object)) {\n    const isPropertyExist = objectHasOwnProperty(object, property);\n\n    if (isPropertyExist) {\n      if (Array.isArray(value)) {\n        newObject[property] = cleanArray(value);\n      } else if (\n        value &&\n        typeof value === 'object' &&\n        value.constructor === Object\n      ) {\n        newObject[property] = clone(value);\n      } else {\n        newObject[property] = value;\n      }\n    }\n  }\n\n  return newObject;\n}\n\n/**\n * This method automatically checks if the prop is function or getter and behaves accordingly.\n *\n * @param object - The object to look up the getter function in its prototype chain.\n * @param prop - The property name for which to find the getter function.\n * @returns The getter function found in the prototype chain or a fallback function.\n */\nfunction lookupGetter<T extends Record<string, any>>(\n  object: T,\n  prop: string\n): ReturnType<typeof unapply<any>> | (() => null) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(): null {\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayLastIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  arraySplice,\n  // Object\n  entries,\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  clone,\n  create,\n  objectHasOwnProperty,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n  addToSet,\n  // Reflect\n  unapply,\n  unconstruct,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n] as const);\n\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n] as const);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n] as const);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n] as const);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n  'mprescripts',\n] as const);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n] as const);\n\nexport const text = freeze(['#text'] as const);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'popover',\n  'popovertarget',\n  'popovertargetaction',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'wrap',\n  'xmlns',\n  'slot',\n] as const);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'amplitude',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'exponent',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'intercept',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'slope',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'tablevalues',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n] as const);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n] as const);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\$\\{[\\w\\W]*/gm); // eslint-disable-line unicorn/better-regex\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\nexport const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n", "/* eslint-disable @typescript-eslint/indent */\n\nimport type { TrustedHTML, TrustedTypesWindow } from 'trusted-types/lib';\nimport type { Config, UseProfilesConfig } from './config';\nimport * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  entries,\n  freeze,\n  arrayForEach,\n  arrayLastIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySplice,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n  create,\n  objectHasOwnProperty,\n} from './utils.js';\n\nexport type { Config } from './config';\n\ndeclare const VERSION: string;\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\nconst NODE_TYPE = {\n  element: 1,\n  attribute: 2,\n  text: 3,\n  cdataSection: 4,\n  entityReference: 5, // Deprecated\n  entityNode: 6, // Deprecated\n  progressingInstruction: 7,\n  comment: 8,\n  document: 9,\n  documentType: 10,\n  documentFragment: 11,\n  notation: 12, // Deprecated\n};\n\nconst getGlobal = function (): WindowLike {\n  return typeof window === 'undefined' ? null : window;\n};\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param trustedTypes The policy factory.\n * @param purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\nconst _createTrustedTypesPolicy = function (\n  trustedTypes: TrustedTypePolicyFactory,\n  purifyHostElement: HTMLScriptElement\n) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nconst _createHooksMap = function (): HooksMap {\n  return {\n    afterSanitizeAttributes: [],\n    afterSanitizeElements: [],\n    afterSanitizeShadowDOM: [],\n    beforeSanitizeAttributes: [],\n    beforeSanitizeElements: [],\n    beforeSanitizeShadowDOM: [],\n    uponSanitizeAttribute: [],\n    uponSanitizeElement: [],\n    uponSanitizeShadowNode: [],\n  };\n};\n\nfunction createDOMPurify(window: WindowLike = getGlobal()): DOMPurify {\n  const DOMPurify: DOMPurify = (root: WindowLike) => createDOMPurify(root);\n\n  DOMPurify.version = VERSION;\n\n  DOMPurify.removed = [];\n\n  if (\n    !window ||\n    !window.document ||\n    window.document.nodeType !== NODE_TYPE.document ||\n    !window.Element\n  ) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  let { document } = window;\n\n  const originalDocument = document;\n  const currentScript: HTMLScriptElement =\n    originalDocument.currentScript as HTMLScriptElement;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || (window as any).MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const remove = lookupGetter(ElementPrototype, 'remove');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let hooks = _createHooksMap();\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof entries === 'function' &&\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPurify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES: UseProfilesConfig | false = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  let MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  let HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE: null | DOMParserSupportedType = null;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc: null | Parameters<typeof addToSet>[2] = null;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG: Config | null = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (\n    testValue: unknown\n  ): testValue is Function | RegExp {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg: Config = {}): void {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? DEFAULT_PARSER_MEDIA_TYPE\n        : cfg.PARSER_MEDIA_TYPE;\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS = objectHasOwnProperty(cfg, 'ALLOWED_TAGS')\n      ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n      : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = objectHasOwnProperty(cfg, 'ALLOWED_ATTR')\n      ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n      : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, 'ALLOWED_NAMESPACES')\n      ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n      : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, 'ADD_URI_SAFE_ATTR')\n      ? addToSet(\n          clone(DEFAULT_URI_SAFE_ATTRIBUTES),\n          cfg.ADD_URI_SAFE_ATTR,\n          transformCaseFunc\n        )\n      : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = objectHasOwnProperty(cfg, 'ADD_DATA_URI_TAGS')\n      ? addToSet(\n          clone(DEFAULT_DATA_URI_TAGS),\n          cfg.ADD_DATA_URI_TAGS,\n          transformCaseFunc\n        )\n      : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = objectHasOwnProperty(cfg, 'FORBID_CONTENTS')\n      ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n      : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = objectHasOwnProperty(cfg, 'FORBID_TAGS')\n      ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n      : clone({});\n    FORBID_ATTR = objectHasOwnProperty(cfg, 'FORBID_ATTR')\n      ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n      : clone({});\n    USE_PROFILES = objectHasOwnProperty(cfg, 'USE_PROFILES')\n      ? cfg.USE_PROFILES\n      : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || EXPRESSIONS.IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    MATHML_TEXT_INTEGRATION_POINTS =\n      cfg.MATHML_TEXT_INTEGRATION_POINTS || MATHML_TEXT_INTEGRATION_POINTS;\n    HTML_INTEGRATION_POINTS =\n      cfg.HTML_INTEGRATION_POINTS || HTML_INTEGRATION_POINTS;\n\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, TAGS.text);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.'\n        );\n      }\n\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.'\n        );\n      }\n\n      // Overwrite existing TrustedTypes policy.\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n\n      // Sign local variables required by `sanitize`.\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(\n          trustedTypes,\n          currentScript\n        );\n      }\n\n      // If creating the internal policy succeeded sign internal variables.\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, [\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.svgDisallowed,\n  ]);\n  const ALL_MATHML_TAGS = addToSet({}, [\n    ...TAGS.mathMl,\n    ...TAGS.mathMlDisallowed,\n  ]);\n\n  /**\n   * @param element a DOM element whose namespace is being checked\n   * @returns Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element: Element): boolean {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param node a DOM node\n   */\n  const _forceRemove = function (node: Node): void {\n    arrayPush(DOMPurify.removed, { element: node });\n\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      getParentNode(node).removeChild(node);\n    } catch (_) {\n      remove(node);\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param name an Attribute name\n   * @param element a DOM node\n   */\n  const _removeAttribute = function (name: string, element: Element): void {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: element.getAttributeNode(name),\n        from: element,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: element,\n      });\n    }\n\n    element.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\" attributes\n    if (name === 'is') {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(element);\n        } catch (_) {}\n      } else {\n        try {\n          element.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param dirty - a string of dirty markup\n   * @return a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty: string): Document {\n    /* Create a HTML document */\n    let doc = null;\n    let leadingWhitespace = null;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n   *\n   * @param root The root element or node to start traversing on.\n   * @return The created NodeIterator\n   */\n  const _createNodeIterator = function (root: Node): NodeIterator {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT |\n        NodeFilter.SHOW_COMMENT |\n        NodeFilter.SHOW_TEXT |\n        NodeFilter.SHOW_PROCESSING_INSTRUCTION |\n        NodeFilter.SHOW_CDATA_SECTION,\n      null\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param element element to check for clobbering attacks\n   * @return true if clobbered, false if safe\n   */\n  const _isClobbered = function (element: Element): boolean {\n    return (\n      element instanceof HTMLFormElement &&\n      (typeof element.nodeName !== 'string' ||\n        typeof element.textContent !== 'string' ||\n        typeof element.removeChild !== 'function' ||\n        !(element.attributes instanceof NamedNodeMap) ||\n        typeof element.removeAttribute !== 'function' ||\n        typeof element.setAttribute !== 'function' ||\n        typeof element.namespaceURI !== 'string' ||\n        typeof element.insertBefore !== 'function' ||\n        typeof element.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * Checks whether the given object is a DOM node.\n   *\n   * @param value object to check whether it's a DOM node\n   * @return true is object is a DOM node\n   */\n  const _isNode = function (value: unknown): value is Node {\n    return typeof Node === 'function' && value instanceof Node;\n  };\n\n  function _executeHooks<\n    T extends\n      | NodeHook\n      | ElementHook\n      | DocumentFragmentHook\n      | UponSanitizeElementHook\n      | UponSanitizeAttributeHook\n  >(hooks: T[], currentNode: Parameters<T>[0], data: Parameters<T>[1]): void {\n    arrayForEach(hooks, (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  }\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   * @param currentNode to check for permission to exist\n   * @return true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode: any): boolean {\n    let content = null;\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeElements, currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.uponSanitizeElement, currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      regExpTest(/<[/\\w!]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w!]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any occurrence of processing instructions */\n    if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any kind of possibly harmful comments */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.nodeType === NODE_TYPE.comment &&\n      regExpTest(/<[/\\w]/g, currentNode.data)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        ) {\n          return false;\n        }\n\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        ) {\n          return false;\n        }\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        content = stringReplace(content, expr, ' ');\n      });\n\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeElements, currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param lcTag Lowercase tag name of containing element.\n   * @param lcName Lowercase attribute name.\n   * @param value Attribute value.\n   * @return Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (\n    lcTag: string,\n    lcName: string,\n    value: string\n  ): boolean {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_isBasicCustomElement(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _isBasicCustomElement\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   *\n   * @param tagName name of the tag of the node to sanitize\n   * @returns Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n   */\n  const _isBasicCustomElement = function (tagName: string): RegExpMatchArray {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode: Element): void {\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeAttributes, currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n      forceKeepAttr: undefined,\n    };\n    let l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      const attr = attributes[l];\n      const { name, namespaceURI, value: attrValue } = attr;\n      const lcName = transformCaseFunc(name);\n\n      const initValue = attrValue;\n      let value = name === 'value' ? initValue : stringTrim(initValue);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHooks(hooks.uponSanitizeAttribute, currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n          value = stringReplace(value, expr, ' ');\n        });\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      if (value !== initValue) {\n        try {\n          if (namespaceURI) {\n            currentNode.setAttributeNS(namespaceURI, name, value);\n          } else {\n            /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n            currentNode.setAttribute(name, value);\n          }\n\n          if (_isClobbered(currentNode)) {\n            _forceRemove(currentNode);\n          } else {\n            arrayPop(DOMPurify.removed);\n          }\n        } catch (_) {\n          _removeAttribute(name, currentNode);\n        }\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeAttributes, currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment: DocumentFragment): void {\n    let shadowNode = null;\n    const shadowIterator = _createNodeIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeShadowDOM, fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHooks(hooks.uponSanitizeShadowNode, shadowNode, null);\n\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeShadowDOM, fragment, null);\n  };\n\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body = null;\n    let importedNode = null;\n    let currentNode = null;\n    let returnNode = null;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Return dirty HTML if DOMPurify cannot run */\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if ((dirty as Node).nodeName) {\n        const tagName = transformCaseFunc((dirty as Node).nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (\n        importedNode.nodeType === NODE_TYPE.element &&\n        importedNode.nodeName === 'BODY'\n      ) {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n    }\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        serializedHTML = stringReplace(serializedHTML, expr, ' ');\n      });\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  DOMPurify.setConfig = function (cfg = {}) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  DOMPurify.removeHook = function (entryPoint, hookFunction) {\n    if (hookFunction !== undefined) {\n      const index = arrayLastIndexOf(hooks[entryPoint], hookFunction);\n\n      return index === -1\n        ? undefined\n        : arraySplice(hooks[entryPoint], index, 1)[0];\n    }\n\n    return arrayPop(hooks[entryPoint]);\n  };\n\n  DOMPurify.removeHooks = function (entryPoint) {\n    hooks[entryPoint] = [];\n  };\n\n  DOMPurify.removeAllHooks = function () {\n    hooks = _createHooksMap();\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n\nexport interface DOMPurify {\n  /**\n   * Creates a DOMPurify instance using the given window-like object. Defaults to `window`.\n   */\n  (root?: WindowLike): DOMPurify;\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  version: string;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  removed: Array<RemovedElement | RemovedAttribute>;\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  isSupported: boolean;\n\n  /**\n   * Set the configuration once.\n   *\n   * @param cfg configuration object\n   */\n  setConfig(cfg?: Config): void;\n\n  /**\n   * Removes the configuration.\n   */\n  clearConfig(): void;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized TrustedHTML.\n   */\n  sanitize(\n    dirty: string | Node,\n    cfg: Config & { RETURN_TRUSTED_TYPE: true }\n  ): TrustedHTML;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty DOM node\n   * @param cfg object\n   * @returns Sanitized DOM node.\n   */\n  sanitize(dirty: Node, cfg: Config & { IN_PLACE: true }): Node;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized DOM node.\n   */\n  sanitize(dirty: string | Node, cfg: Config & { RETURN_DOM: true }): Node;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized document fragment.\n   */\n  sanitize(\n    dirty: string | Node,\n    cfg: Config & { RETURN_DOM_FRAGMENT: true }\n  ): DocumentFragment;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized string.\n   */\n  sanitize(dirty: string | Node, cfg?: Config): string;\n\n  /**\n   * Checks if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   *\n   * @param tag Tag name of containing element.\n   * @param attr Attribute name.\n   * @param value Attribute value.\n   * @returns Returns true if `value` is valid. Otherwise, returns false.\n   */\n  isValidAttribute(tag: string, attr: string, value: string): boolean;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(entryPoint: BasicHookName, hookFunction: NodeHook): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(entryPoint: ElementHookName, hookFunction: ElementHook): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: DocumentFragmentHookName,\n    hookFunction: DocumentFragmentHook\n  ): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: 'uponSanitizeElement',\n    hookFunction: UponSanitizeElementHook\n  ): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: 'uponSanitizeAttribute',\n    hookFunction: UponSanitizeAttributeHook\n  ): void;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: BasicHookName,\n    hookFunction?: NodeHook\n  ): NodeHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: ElementHookName,\n    hookFunction?: ElementHook\n  ): ElementHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: DocumentFragmentHookName,\n    hookFunction?: DocumentFragmentHook\n  ): DocumentFragmentHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: 'uponSanitizeElement',\n    hookFunction?: UponSanitizeElementHook\n  ): UponSanitizeElementHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: 'uponSanitizeAttribute',\n    hookFunction?: UponSanitizeAttributeHook\n  ): UponSanitizeAttributeHook | undefined;\n\n  /**\n   * Removes all DOMPurify hooks at a given entryPoint\n   *\n   * @param entryPoint entry point for the hooks to remove\n   */\n  removeHooks(entryPoint: HookName): void;\n\n  /**\n   * Removes all DOMPurify hooks.\n   */\n  removeAllHooks(): void;\n}\n\n/**\n * An element removed by DOMPurify.\n */\nexport interface RemovedElement {\n  /**\n   * The element that was removed.\n   */\n  element: Node;\n}\n\n/**\n * An element removed by DOMPurify.\n */\nexport interface RemovedAttribute {\n  /**\n   * The attribute that was removed.\n   */\n  attribute: Attr | null;\n\n  /**\n   * The element that the attribute was removed.\n   */\n  from: Node;\n}\n\ntype BasicHookName =\n  | 'beforeSanitizeElements'\n  | 'afterSanitizeElements'\n  | 'uponSanitizeShadowNode';\ntype ElementHookName = 'beforeSanitizeAttributes' | 'afterSanitizeAttributes';\ntype DocumentFragmentHookName =\n  | 'beforeSanitizeShadowDOM'\n  | 'afterSanitizeShadowDOM';\ntype UponSanitizeElementHookName = 'uponSanitizeElement';\ntype UponSanitizeAttributeHookName = 'uponSanitizeAttribute';\n\ninterface HooksMap {\n  beforeSanitizeElements: NodeHook[];\n  afterSanitizeElements: NodeHook[];\n  beforeSanitizeShadowDOM: DocumentFragmentHook[];\n  uponSanitizeShadowNode: NodeHook[];\n  afterSanitizeShadowDOM: DocumentFragmentHook[];\n  beforeSanitizeAttributes: ElementHook[];\n  afterSanitizeAttributes: ElementHook[];\n  uponSanitizeElement: UponSanitizeElementHook[];\n  uponSanitizeAttribute: UponSanitizeAttributeHook[];\n}\n\nexport type HookName =\n  | BasicHookName\n  | ElementHookName\n  | DocumentFragmentHookName\n  | UponSanitizeElementHookName\n  | UponSanitizeAttributeHookName;\n\nexport type NodeHook = (\n  this: DOMPurify,\n  currentNode: Node,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type ElementHook = (\n  this: DOMPurify,\n  currentNode: Element,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type DocumentFragmentHook = (\n  this: DOMPurify,\n  currentNode: DocumentFragment,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type UponSanitizeElementHook = (\n  this: DOMPurify,\n  currentNode: Node,\n  hookEvent: UponSanitizeElementHookEvent,\n  config: Config\n) => void;\n\nexport type UponSanitizeAttributeHook = (\n  this: DOMPurify,\n  currentNode: Element,\n  hookEvent: UponSanitizeAttributeHookEvent,\n  config: Config\n) => void;\n\nexport interface UponSanitizeElementHookEvent {\n  tagName: string;\n  allowedTags: Record<string, boolean>;\n}\n\nexport interface UponSanitizeAttributeHookEvent {\n  attrName: string;\n  attrValue: string;\n  keepAttr: boolean;\n  allowedAttributes: Record<string, boolean>;\n  forceKeepAttr: boolean | undefined;\n}\n\n/**\n * A `Window`-like object containing the properties and types that DOMPurify requires.\n */\nexport type WindowLike = Pick<\n  typeof globalThis,\n  | 'DocumentFragment'\n  | 'HTMLTemplateElement'\n  | 'Node'\n  | 'Element'\n  | 'NodeFilter'\n  | 'NamedNodeMap'\n  | 'HTMLFormElement'\n  | 'DOMParser'\n> & {\n  document?: Document;\n  MozNamedAttrMap?: typeof window.NamedNodeMap;\n} & Pick<TrustedTypesWindow, 'trustedTypes'>;\n"], "names": ["entries", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "Object", "freeze", "seal", "create", "apply", "construct", "Reflect", "x", "fun", "thisValue", "args", "Func", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayLastIndexOf", "lastIndexOf", "arrayPop", "pop", "arrayPush", "push", "arraySplice", "splice", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "objectHasOwnProperty", "hasOwnProperty", "regExpTest", "RegExp", "test", "typeErrorCreate", "func", "TypeError", "_len2", "arguments", "length", "_key2", "thisArg", "lastIndex", "_len", "_key", "addToSet", "set", "array", "transformCaseFunc", "l", "element", "lcElement", "cleanArray", "index", "clone", "object", "newObject", "property", "value", "isArray", "constructor", "lookupGetter", "prop", "desc", "get", "html", "svg", "svgFilters", "svgDisallowed", "mathMl", "mathMlDisallowed", "text", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "NODE_TYPE", "getGlobal", "window", "purify", "createDOMPurify", "undefined", "DOMPurify", "root", "version", "VERSION", "removed", "document", "nodeType", "Element", "isSupported", "originalDocument", "currentScript", "DocumentFragment", "HTMLTemplateElement", "Node", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trustedTypes", "ElementPrototype", "cloneNode", "remove", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "hooks", "afterSanitizeAttributes", "afterSanitizeElements", "afterSanitizeShadowDOM", "beforeSanitizeAttributes", "beforeSanitizeElements", "beforeSanitizeShadowDOM", "uponSanitizeAttribute", "uponSanitizeElement", "uponSanitizeShadowNode", "createHTMLDocument", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "TRUSTED_TYPES_POLICY", "createHTML", "createScriptURL", "purifyHostElement", "createPolicy", "suffix", "ATTR_NAME", "hasAttribute", "getAttribute", "policyName", "scriptUrl", "_", "console", "warn", "_createTrustedTypesPolicy", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_forceRemove", "node", "<PERSON><PERSON><PERSON><PERSON>", "_removeAttribute", "name", "attribute", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createNodeIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "nodeName", "textContent", "attributes", "namespaceURI", "hasChildNodes", "_isNode", "_executeHooks", "currentNode", "data", "hook", "_sanitizeElements", "tagName", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_isBasicCustomElement", "parentNode", "i", "child<PERSON>lone", "__removalCount", "parent", "parentTagName", "Boolean", "_checkValidNamespace", "expr", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "forceKeepAttr", "attr", "initValue", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "returnNode", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmode", "serializedHTML", "outerHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "entryPoint", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";0OAAA,MAAMA,QACJA,EAAOC,eACPA,EAAcC,SACdA,EAAQC,eACRA,EAAcC,yBACdA,GACEC,OAEJ,IAAIC,OAAEA,EAAMC,KAAEA,EAAIC,OAAEA,GAAWH,QAC3BI,MAAEA,EAAKC,UAAEA,GAAiC,oBAAZC,SAA2BA,QAExDL,IACHA,EAAS,SAAUM,GACjB,OAAOA,IAINL,IACHA,EAAO,SAAUK,GACf,OAAOA,IAINH,IACHA,EAAQ,SAAUI,EAAKC,EAAWC,GAChC,OAAOF,EAAIJ,MAAMK,EAAWC,KAI3BL,IACHA,EAAY,SAAUM,EAAMD,GAC1B,OAAO,IAAIC,KAAQD,KAIvB,MAAME,EAAeC,EAAQC,MAAMC,UAAUC,SAEvCC,EAAmBJ,EAAQC,MAAMC,UAAUG,aAC3CC,EAAWN,EAAQC,MAAMC,UAAUK,KACnCC,EAAYR,EAAQC,MAAMC,UAAUO,MAEpCC,EAAcV,EAAQC,MAAMC,UAAUS,QAEtCC,EAAoBZ,EAAQa,OAAOX,UAAUY,aAC7CC,EAAiBf,EAAQa,OAAOX,UAAUc,UAC1CC,EAAcjB,EAAQa,OAAOX,UAAUgB,OACvCC,EAAgBnB,EAAQa,OAAOX,UAAUkB,SACzCC,EAAgBrB,EAAQa,OAAOX,UAAUoB,SACzCC,EAAavB,EAAQa,OAAOX,UAAUsB,MAEtCC,EAAuBzB,EAAQb,OAAOe,UAAUwB,gBAEhDC,EAAa3B,EAAQ4B,OAAO1B,UAAU2B,MAEtCC,GA0BkBC,EA1BYC,UA2B3B,WAAA,IAAA,IAAAC,EAAAC,UAAAC,OAAItC,EAAWI,IAAAA,MAAAgC,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAXvC,EAAWuC,GAAAF,UAAAE,GAAA,OAAQ5C,EAAUuC,EAAMlC,EAAK,GADrD,IAAwBkC,EAlBxB,SAAS/B,EACP+B,GAEA,OAAO,SAACM,GACFA,aAAmBT,SACrBS,EAAQC,UAAY,GACrB,IAAAC,IAAAA,EAAAL,UAAAC,OAHsBtC,MAAWI,MAAAsC,EAAAA,EAAAA,OAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAX3C,EAAW2C,EAAAN,GAAAA,UAAAM,GAKlC,OAAOjD,EAAMwC,EAAMM,EAASxC,GAEhC,CAoBA,SAAS4C,EACPC,EACAC,GACyE,IAAzEC,yDAAwDhC,EAEpD7B,GAIFA,EAAe2D,EAAK,MAGtB,IAAIG,EAAIF,EAAMR,OACd,KAAOU,KAAK,CACV,IAAIC,EAAUH,EAAME,GACpB,GAAuB,iBAAZC,EAAsB,CAC/B,MAAMC,EAAYH,EAAkBE,GAChCC,IAAcD,IAEX9D,EAAS2D,KACXA,EAAgBE,GAAKE,GAGxBD,EAAUC,EAEd,CAEAL,EAAII,IAAW,CACjB,CAEA,OAAOJ,CACT,CAQA,SAASM,EAAcL,GACrB,IAAK,IAAIM,EAAQ,EAAGA,EAAQN,EAAMR,OAAQc,IAAS,CACzBxB,EAAqBkB,EAAOM,KAGlDN,EAAMM,GAAS,KAEnB,CAEA,OAAON,CACT,CAQA,SAASO,EAAqCC,GAC5C,MAAMC,EAAY9D,EAAO,MAEzB,IAAK,MAAO+D,EAAUC,KAAUxE,EAAQqE,GAAS,CACvB1B,EAAqB0B,EAAQE,KAG/CpD,MAAMsD,QAAQD,GAChBF,EAAUC,GAAYL,EAAWM,GAEjCA,GACiB,iBAAVA,GACPA,EAAME,cAAgBrE,OAEtBiE,EAAUC,GAAYH,EAAMI,GAE5BF,EAAUC,GAAYC,EAG5B,CAEA,OAAOF,CACT,CASA,SAASK,EACPN,EACAO,GAEA,KAAkB,OAAXP,GAAiB,CACtB,MAAMQ,EAAOzE,EAAyBiE,EAAQO,GAE9C,GAAIC,EAAM,CACR,GAAIA,EAAKC,IACP,OAAO5D,EAAQ2D,EAAKC,KAGtB,GAA0B,mBAAfD,EAAKL,MACd,OAAOtD,EAAQ2D,EAAKL,MAExB,CAEAH,EAASlE,EAAekE,EAC1B,CAMA,OAJA,WACE,OAAO,IACT,CAGF,CC3MO,MAAMU,EAAOzE,EAAO,CACzB,IACA,OACA,UACA,UACA,OACA,UACA,QACA,QACA,IACA,MACA,MACA,MACA,QACA,aACA,OACA,KACA,SACA,SACA,UACA,SACA,OACA,OACA,MACA,WACA,UACA,OACA,WACA,KACA,YACA,MACA,UACA,MACA,SACA,MACA,MACA,KACA,KACA,UACA,KACA,WACA,aACA,SACA,OACA,SACA,OACA,KACA,KACA,KACA,KACA,KACA,KACA,OACA,SACA,SACA,KACA,OACA,IACA,MACA,QACA,MACA,MACA,QACA,SACA,KACA,OACA,MACA,OACA,UACA,OACA,WACA,QACA,MACA,OACA,KACA,WACA,SACA,SACA,IACA,UACA,MACA,WACA,IACA,KACA,KACA,OACA,IACA,OACA,UACA,SACA,SACA,QACA,SACA,SACA,OACA,SACA,SACA,QACA,MACA,UACA,MACA,QACA,QACA,KACA,WACA,WACA,QACA,KACA,QACA,OACA,KACA,QACA,KACA,IACA,KACA,MACA,QACA,QAGW0E,EAAM1E,EAAO,CACxB,MACA,IACA,WACA,cACA,eACA,eACA,gBACA,mBACA,SACA,WACA,OACA,OACA,UACA,SACA,OACA,IACA,QACA,WACA,QACA,QACA,OACA,iBACA,SACA,OACA,WACA,QACA,OACA,UACA,UACA,WACA,iBACA,OACA,OACA,QACA,SACA,SACA,OACA,WACA,QACA,OACA,QACA,OACA,UAGW2E,EAAa3E,EAAO,CAC/B,UACA,gBACA,sBACA,cACA,mBACA,oBACA,oBACA,iBACA,eACA,UACA,UACA,UACA,UACA,UACA,iBACA,UACA,UACA,cACA,eACA,WACA,eACA,qBACA,cACA,SACA,iBAOW4E,EAAgB5E,EAAO,CAClC,UACA,gBACA,SACA,UACA,YACA,mBACA,iBACA,gBACA,gBACA,gBACA,QACA,YACA,OACA,eACA,YACA,UACA,gBACA,SACA,MACA,aACA,UACA,QAGW6E,EAAS7E,EAAO,CAC3B,OACA,WACA,SACA,UACA,QACA,SACA,KACA,aACA,gBACA,KACA,KACA,QACA,UACA,WACA,QACA,OACA,KACA,SACA,QACA,SACA,OACA,OACA,UACA,SACA,MACA,QACA,MACA,SACA,aACA,gBAKW8E,EAAmB9E,EAAO,CACrC,UACA,cACA,aACA,WACA,YACA,UACA,UACA,SACA,SACA,QACA,YACA,aACA,iBACA,cACA,SAGW+E,EAAO/E,EAAO,CAAC,UCpRfyE,EAAOzE,EAAO,CACzB,SACA,SACA,QACA,MACA,iBACA,eACA,uBACA,WACA,aACA,UACA,SACA,UACA,cACA,cACA,UACA,OACA,QACA,QACA,QACA,OACA,UACA,WACA,eACA,SACA,cACA,WACA,WACA,UACA,MACA,WACA,0BACA,wBACA,WACA,YACA,UACA,eACA,OACA,MACA,UACA,SACA,SACA,OACA,OACA,WACA,KACA,YACA,YACA,QACA,OACA,QACA,OACA,OACA,UACA,OACA,MACA,MACA,YACA,QACA,SACA,MACA,YACA,WACA,QACA,OACA,QACA,UACA,aACA,SACA,OACA,UACA,UACA,cACA,cACA,UACA,gBACA,sBACA,SACA,UACA,UACA,aACA,WACA,MACA,WACA,MACA,WACA,OACA,OACA,UACA,aACA,QACA,WACA,QACA,OACA,QACA,OACA,UACA,QACA,MACA,SACA,OACA,QACA,UACA,WACA,QACA,YACA,OACA,SACA,SACA,QACA,QACA,OACA,QACA,SAGW0E,EAAM1E,EAAO,CACxB,gBACA,aACA,WACA,qBACA,YACA,SACA,gBACA,gBACA,UACA,gBACA,iBACA,QACA,OACA,KACA,QACA,OACA,gBACA,YACA,YACA,QACA,sBACA,8BACA,gBACA,kBACA,KACA,KACA,IACA,KACA,KACA,kBACA,YACA,UACA,UACA,MACA,WACA,YACA,MACA,WACA,OACA,eACA,YACA,SACA,cACA,cACA,gBACA,cACA,YACA,mBACA,eACA,aACA,eACA,cACA,KACA,KACA,KACA,KACA,aACA,WACA,gBACA,oBACA,SACA,OACA,KACA,kBACA,KACA,MACA,YACA,IACA,KACA,KACA,KACA,KACA,UACA,YACA,aACA,WACA,OACA,eACA,iBACA,eACA,mBACA,iBACA,QACA,aACA,aACA,eACA,eACA,cACA,cACA,mBACA,YACA,MACA,OACA,QACA,SACA,OACA,MACA,OACA,aACA,SACA,WACA,UACA,QACA,SACA,cACA,SACA,WACA,cACA,OACA,aACA,sBACA,mBACA,eACA,SACA,gBACA,sBACA,iBACA,IACA,KACA,KACA,SACA,OACA,OACA,cACA,YACA,UACA,SACA,SACA,QACA,OACA,kBACA,QACA,mBACA,mBACA,eACA,cACA,eACA,cACA,aACA,eACA,mBACA,oBACA,iBACA,kBACA,oBACA,iBACA,SACA,eACA,QACA,eACA,iBACA,WACA,cACA,UACA,UACA,YACA,mBACA,cACA,kBACA,iBACA,aACA,OACA,KACA,KACA,UACA,SACA,UACA,aACA,UACA,aACA,gBACA,gBACA,QACA,eACA,OACA,eACA,mBACA,mBACA,IACA,KACA,KACA,QACA,IACA,KACA,KACA,IACA,eAGW6E,EAAS7E,EAAO,CAC3B,SACA,cACA,QACA,WACA,QACA,eACA,cACA,aACA,aACA,QACA,MACA,UACA,eACA,WACA,QACA,QACA,SACA,OACA,KACA,UACA,SACA,gBACA,SACA,SACA,iBACA,YACA,WACA,cACA,UACA,UACA,gBACA,WACA,WACA,OACA,WACA,WACA,aACA,UACA,SACA,SACA,cACA,gBACA,uBACA,YACA,YACA,aACA,WACA,iBACA,iBACA,YACA,UACA,QACA,UAGWgF,EAAMhF,EAAO,CACxB,aACA,SACA,cACA,YACA,gBC9WWiF,EAAgBhF,EAAK,6BACrBiF,EAAWjF,EAAK,yBAChBkF,EAAclF,EAAK,iBACnBmF,EAAYnF,EAAK,gCACjBoF,EAAYpF,EAAK,kBACjBqF,EAAiBrF,EAC5B,oGAEWsF,EAAoBtF,EAAK,yBACzBuF,EAAkBvF,EAC7B,+DAEWwF,EAAexF,EAAK,WACpByF,EAAiBzF,EAAK,0NCmBnC,MAAM0F,EACK,EADLA,EAGE,EAHFA,GAOoB,EAPpBA,GAQK,EARLA,GASM,EAMNC,GAAY,WAChB,MAAyB,oBAAXC,OAAyB,KAAOA,MAChD,EAilDA,IAAAC,GA/gDA,SAASC,IAAgD,IAAhCF,EAAqB/C,UAAAC,OAAAD,QAAAkD,IAAAlD,UAAAkD,GAAAlD,UAAA8C,GAAAA,KAC5C,MAAMK,EAAwBC,GAAqBH,EAAgBG,GAMnE,GAJAD,EAAUE,QAAUC,QAEpBH,EAAUI,QAAU,IAGjBR,IACAA,EAAOS,UACRT,EAAOS,SAASC,WAAaZ,KAC5BE,EAAOW,QAMR,OAFAP,EAAUQ,aAAc,EAEjBR,EAGT,IAAIK,SAAEA,GAAaT,EAEnB,MAAMa,EAAmBJ,EACnBK,EACJD,EAAiBC,eACbC,iBACJA,EAAgBC,oBAChBA,EAAmBC,KACnBA,EAAIN,QACJA,EAAOO,WACPA,EAAUC,aACVA,EAAenB,EAAOmB,cAAiBnB,EAAeoB,gBAAeC,gBACrEA,EAAeC,UACfA,EAASC,aACTA,GACEvB,EAEEwB,EAAmBb,EAAQ1F,UAE3BwG,EAAYjD,EAAagD,EAAkB,aAC3CE,EAASlD,EAAagD,EAAkB,UACxCG,GAAiBnD,EAAagD,EAAkB,eAChDI,GAAgBpD,EAAagD,EAAkB,cAC/CK,GAAgBrD,EAAagD,EAAkB,cAQrD,GAAmC,mBAAxBR,EAAoC,CAC7C,MAAMc,EAAWrB,EAASsB,cAAc,YACpCD,EAASE,SAAWF,EAASE,QAAQC,gBACvCxB,EAAWqB,EAASE,QAAQC,cAEhC,CAEA,IAAIC,GACAC,GAAY,GAEhB,MAAMC,eACJA,GAAcC,mBACdA,GAAkBC,uBAClBA,GAAsBC,qBACtBA,IACE9B,GACE+B,WAAEA,IAAe3B,EAEvB,IAAI4B,GAlFG,CACLC,wBAAyB,GACzBC,sBAAuB,GACvBC,uBAAwB,GACxBC,yBAA0B,GAC1BC,uBAAwB,GACxBC,wBAAyB,GACzBC,sBAAuB,GACvBC,oBAAqB,GACrBC,uBAAwB,IA8E1B9C,EAAUQ,YACW,mBAAZ/G,GACkB,mBAAlBgI,IACPO,SACsCjC,IAAtCiC,GAAee,mBAEjB,MAAM/D,cACJA,GAAaC,SACbA,GAAQC,YACRA,GAAWC,UACXA,GAASC,UACTA,GAASE,kBACTA,GAAiBC,gBACjBA,GAAeE,eACfA,IACEuD,EAEJ,IAAM3D,eAAAA,IAAmB2D,EAQrBC,GAAe,KACnB,MAAMC,GAAuB9F,EAAS,GAAI,IACrC+F,KACAA,KACAA,KACAA,KACAA,IAIL,IAAIC,GAAe,KACnB,MAAMC,GAAuBjG,EAAS,CAAE,EAAE,IACrCkG,KACAA,KACAA,KACAA,IASL,IAAIC,GAA0BzJ,OAAOE,KACnCC,EAAO,KAAM,CACXuJ,aAAc,CACZC,UAAU,EACVC,cAAc,EACdC,YAAY,EACZ1F,MAAO,MAET2F,mBAAoB,CAClBH,UAAU,EACVC,cAAc,EACdC,YAAY,EACZ1F,MAAO,MAET4F,+BAAgC,CAC9BJ,UAAU,EACVC,cAAc,EACdC,YAAY,EACZ1F,OAAO,MAMT6F,GAAc,KAGdC,GAAc,KAGdC,IAAkB,EAGlBC,IAAkB,EAGlBC,IAA0B,EAI1BC,IAA2B,EAK3BC,IAAqB,EAKrBC,IAAe,EAGfC,IAAiB,EAGjBC,IAAa,EAIbC,IAAa,EAMbC,IAAa,EAIbC,IAAsB,EAItBC,IAAsB,EAKtBC,IAAe,EAefC,IAAuB,EAIvBC,IAAe,EAIfC,IAAW,EAGXC,GAA0C,CAAA,EAG1CC,GAAkB,KACtB,MAAMC,GAA0B9H,EAAS,CAAE,EAAE,CAC3C,iBACA,QACA,WACA,OACA,gBACA,OACA,SACA,OACA,KACA,KACA,KACA,KACA,QACA,UACA,WACA,WACA,YACA,SACA,QACA,MACA,WACA,QACA,QACA,QACA,QAIF,IAAI+H,GAAgB,KACpB,MAAMC,GAAwBhI,EAAS,CAAE,EAAE,CACzC,QACA,QACA,MACA,SACA,QACA,UAIF,IAAIiI,GAAsB,KAC1B,MAAMC,GAA8BlI,EAAS,GAAI,CAC/C,MACA,QACA,MACA,KACA,QACA,OACA,UACA,cACA,OACA,UACA,QACA,QACA,QACA,UAGImI,GAAmB,qCACnBC,GAAgB,6BAChBC,GAAiB,+BAEvB,IAAIC,GAAYD,GACZE,IAAiB,EAGjBC,GAAqB,KACzB,MAAMC,GAA6BzI,EACjC,GACA,CAACmI,GAAkBC,GAAeC,IAClC/J,GAGF,IAAIoK,GAAiC1I,EAAS,CAAA,EAAI,CAChD,KACA,KACA,KACA,KACA,UAGE2I,GAA0B3I,EAAS,CAAE,EAAE,CAAC,mBAM5C,MAAM4I,GAA+B5I,EAAS,CAAA,EAAI,CAChD,QACA,QACA,OACA,IACA,WAIF,IAAI6I,GAAmD,KACvD,MAAMC,GAA+B,CAAC,wBAAyB,aAE/D,IAAI3I,GAA2D,KAG3D4I,GAAwB,KAK5B,MAAMC,GAAc/F,EAASsB,cAAc,QAErC0E,GAAoB,SACxBC,GAEA,OAAOA,aAAqB/J,QAAU+J,aAAqBC,UASvDC,GAAe,WAA0B,IAAhBC,EAAA5J,UAAAC,OAAA,QAAAiD,IAAAlD,UAAA,GAAAA,UAAA,GAAc,CAAA,EAC3C,IAAIsJ,IAAUA,KAAWM,EAAzB,CA6LA,GAxLKA,GAAsB,iBAARA,IACjBA,EAAM,CAAA,GAIRA,EAAM5I,EAAM4I,GAEZR,IAEmE,IAAjEC,GAA6BjK,QAAQwK,EAAIR,mBAtCX,YAwC1BQ,EAAIR,kBAGV1I,GACwB,0BAAtB0I,GACIvK,EACAH,EAGN0H,GAAe7G,EAAqBqK,EAAK,gBACrCrJ,EAAS,CAAE,EAAEqJ,EAAIxD,aAAc1F,IAC/B2F,GACJE,GAAehH,EAAqBqK,EAAK,gBACrCrJ,EAAS,CAAE,EAAEqJ,EAAIrD,aAAc7F,IAC/B8F,GACJuC,GAAqBxJ,EAAqBqK,EAAK,sBAC3CrJ,EAAS,CAAE,EAAEqJ,EAAIb,mBAAoBlK,GACrCmK,GACJR,GAAsBjJ,EAAqBqK,EAAK,qBAC5CrJ,EACES,EAAMyH,IACNmB,EAAIC,kBACJnJ,IAEF+H,GACJH,GAAgB/I,EAAqBqK,EAAK,qBACtCrJ,EACES,EAAMuH,IACNqB,EAAIE,kBACJpJ,IAEF6H,GACJH,GAAkB7I,EAAqBqK,EAAK,mBACxCrJ,EAAS,CAAE,EAAEqJ,EAAIxB,gBAAiB1H,IAClC2H,GACJpB,GAAc1H,EAAqBqK,EAAK,eACpCrJ,EAAS,CAAA,EAAIqJ,EAAI3C,YAAavG,IAC9BM,EAAM,CAAE,GACZkG,GAAc3H,EAAqBqK,EAAK,eACpCrJ,EAAS,CAAA,EAAIqJ,EAAI1C,YAAaxG,IAC9BM,EAAM,CAAE,GACZmH,KAAe5I,EAAqBqK,EAAK,iBACrCA,EAAIzB,aAERhB,IAA0C,IAAxByC,EAAIzC,gBACtBC,IAA0C,IAAxBwC,EAAIxC,gBACtBC,GAA0BuC,EAAIvC,0BAA2B,EACzDC,IAA4D,IAAjCsC,EAAItC,yBAC/BC,GAAqBqC,EAAIrC,qBAAsB,EAC/CC,IAAoC,IAArBoC,EAAIpC,aACnBC,GAAiBmC,EAAInC,iBAAkB,EACvCG,GAAagC,EAAIhC,aAAc,EAC/BC,GAAsB+B,EAAI/B,sBAAuB,EACjDC,GAAsB8B,EAAI9B,sBAAuB,EACjDH,GAAaiC,EAAIjC,aAAc,EAC/BI,IAAoC,IAArB6B,EAAI7B,aACnBC,GAAuB4B,EAAI5B,uBAAwB,EACnDC,IAAoC,IAArB2B,EAAI3B,aACnBC,GAAW0B,EAAI1B,WAAY,EAC3B1F,GAAiBoH,EAAIG,oBAAsB5D,EAC3C0C,GAAYe,EAAIf,WAAaD,GAC7BK,GACEW,EAAIX,gCAAkCA,GACxCC,GACEU,EAAIV,yBAA2BA,GAEjCxC,GAA0BkD,EAAIlD,yBAA2B,GAEvDkD,EAAIlD,yBACJ8C,GAAkBI,EAAIlD,wBAAwBC,gBAE9CD,GAAwBC,aACtBiD,EAAIlD,wBAAwBC,cAI9BiD,EAAIlD,yBACJ8C,GAAkBI,EAAIlD,wBAAwBK,sBAE9CL,GAAwBK,mBACtB6C,EAAIlD,wBAAwBK,oBAI9B6C,EAAIlD,yBAEF,kBADKkD,EAAIlD,wBAAwBM,iCAGnCN,GAAwBM,+BACtB4C,EAAIlD,wBAAwBM,gCAG5BO,KACFH,IAAkB,GAGhBS,KACFD,IAAa,GAIXO,KACF/B,GAAe7F,EAAS,GAAI+F,GAC5BC,GAAe,IACW,IAAtB4B,GAAaxG,OACfpB,EAAS6F,GAAcE,GACvB/F,EAASgG,GAAcE,KAGA,IAArB0B,GAAavG,MACfrB,EAAS6F,GAAcE,GACvB/F,EAASgG,GAAcE,GACvBlG,EAASgG,GAAcE,KAGO,IAA5B0B,GAAatG,aACftB,EAAS6F,GAAcE,GACvB/F,EAASgG,GAAcE,GACvBlG,EAASgG,GAAcE,KAGG,IAAxB0B,GAAapG,SACfxB,EAAS6F,GAAcE,GACvB/F,EAASgG,GAAcE,GACvBlG,EAASgG,GAAcE,KAKvBmD,EAAII,WACF5D,KAAiBC,KACnBD,GAAepF,EAAMoF,KAGvB7F,EAAS6F,GAAcwD,EAAII,SAAUtJ,KAGnCkJ,EAAIK,WACF1D,KAAiBC,KACnBD,GAAevF,EAAMuF,KAGvBhG,EAASgG,GAAcqD,EAAIK,SAAUvJ,KAGnCkJ,EAAIC,mBACNtJ,EAASiI,GAAqBoB,EAAIC,kBAAmBnJ,IAGnDkJ,EAAIxB,kBACFA,KAAoBC,KACtBD,GAAkBpH,EAAMoH,KAG1B7H,EAAS6H,GAAiBwB,EAAIxB,gBAAiB1H,KAI7CuH,KACF7B,GAAa,UAAW,GAItBqB,IACFlH,EAAS6F,GAAc,CAAC,OAAQ,OAAQ,SAItCA,GAAa8D,QACf3J,EAAS6F,GAAc,CAAC,iBACjBa,GAAYkD,OAGjBP,EAAIQ,qBAAsB,CAC5B,GAAmD,mBAAxCR,EAAIQ,qBAAqBC,WAClC,MAAMzK,EACJ,+EAIJ,GAAwD,mBAA7CgK,EAAIQ,qBAAqBE,gBAClC,MAAM1K,EACJ,oFAKJqF,GAAqB2E,EAAIQ,qBAGzBlF,GAAYD,GAAmBoF,WAAW,GAC5C,WAE6BnH,IAAvB+B,KACFA,GA5mB0B,SAChCX,EACAiG,GAEA,GAC0B,iBAAjBjG,GAC8B,mBAA9BA,EAAakG,aAEpB,OAAO,KAMT,IAAIC,EAAS,KACb,MAAMC,EAAY,wBACdH,GAAqBA,EAAkBI,aAAaD,KACtDD,EAASF,EAAkBK,aAAaF,IAG1C,MAAMG,EAAa,aAAeJ,EAAS,IAAMA,EAAS,IAE1D,IACE,OAAOnG,EAAakG,aAAaK,EAAY,CAC3CR,WAAW1I,GACFA,EAET2I,gBAAgBQ,GACPA,GAGZ,CAAC,MAAOC,GAOP,OAHAC,QAAQC,KACN,uBAAyBJ,EAAa,0BAEjC,IACT,CACF,CAokB6BK,CACnB5G,EACAT,IAKuB,OAAvBoB,IAAoD,iBAAdC,KACxCA,GAAYD,GAAmBoF,WAAW,KAM1CnN,GACFA,EAAO0M,GAGTN,GAASM,CAlOT,GAwOIuB,GAAe5K,EAAS,CAAA,EAAI,IAC7B+F,KACAA,KACAA,IAEC8E,GAAkB7K,EAAS,CAAE,EAAE,IAChC+F,KACAA,IAqHC+E,GAAe,SAAUC,GAC7BhN,EAAU6E,EAAUI,QAAS,CAAE3C,QAAS0K,IAExC,IAEE1G,GAAc0G,GAAMC,YAAYD,EACjC,CAAC,MAAOP,GACPtG,EAAO6G,EACT,GASIE,GAAmB,SAAUC,EAAc7K,GAC/C,IACEtC,EAAU6E,EAAUI,QAAS,CAC3BmI,UAAW9K,EAAQ+K,iBAAiBF,GACpCG,KAAMhL,GAET,CAAC,MAAOmK,GACPzM,EAAU6E,EAAUI,QAAS,CAC3BmI,UAAW,KACXE,KAAMhL,GAEV,CAKA,GAHAA,EAAQiL,gBAAgBJ,GAGX,OAATA,EACF,GAAI7D,IAAcC,GAChB,IACEwD,GAAazK,EACf,CAAE,MAAOmK,GAAI,MAEb,IACEnK,EAAQkL,aAAaL,EAAM,GAC7B,CAAE,MAAOV,GAAI,GAWbgB,GAAgB,SAAUC,GAE9B,IAAIC,EAAM,KACNC,EAAoB,KAExB,GAAIvE,GACFqE,EAAQ,oBAAsBA,MACzB,CAEL,MAAMG,EAAUpN,EAAYiN,EAAO,eACnCE,EAAoBC,GAAWA,EAAQ,EACzC,CAGwB,0BAAtB/C,IACAP,KAAcD,KAGdoD,EACE,iEACAA,EACA,kBAGJ,MAAMI,EAAenH,GACjBA,GAAmBoF,WAAW2B,GAC9BA,EAKJ,GAAInD,KAAcD,GAChB,IACEqD,GAAM,IAAI5H,GAAYgI,gBAAgBD,EAAchD,GACtD,CAAE,MAAO2B,GAAI,CAIf,IAAKkB,IAAQA,EAAIK,gBAAiB,CAChCL,EAAM9G,GAAeoH,eAAe1D,GAAW,WAAY,MAC3D,IACEoD,EAAIK,gBAAgBE,UAAY1D,GAC5B5D,GACAkH,CACL,CAAC,MAAOrB,GACP,CAEJ,CAEA,MAAM0B,EAAOR,EAAIQ,MAAQR,EAAIK,gBAU7B,OARIN,GAASE,GACXO,EAAKC,aACHlJ,EAASmJ,eAAeT,GACxBO,EAAKG,WAAW,IAAM,MAKtB/D,KAAcD,GACTtD,GAAqBuH,KAC1BZ,EACAxE,GAAiB,OAAS,QAC1B,GAGGA,GAAiBwE,EAAIK,gBAAkBG,GAS1CK,GAAsB,SAAU1J,GACpC,OAAOgC,GAAmByH,KACxBzJ,EAAK4B,eAAiB5B,EACtBA,EAEAa,EAAW8I,aACT9I,EAAW+I,aACX/I,EAAWgJ,UACXhJ,EAAWiJ,4BACXjJ,EAAWkJ,mBACb,OAUEC,GAAe,SAAUxM,GAC7B,OACEA,aAAmBwD,IACU,iBAArBxD,EAAQyM,UACiB,iBAAxBzM,EAAQ0M,aACgB,mBAAxB1M,EAAQ2K,eACb3K,EAAQ2M,sBAAsBrJ,IACG,mBAA5BtD,EAAQiL,iBACiB,mBAAzBjL,EAAQkL,cACiB,iBAAzBlL,EAAQ4M,cACiB,mBAAzB5M,EAAQ8L,cACkB,mBAA1B9L,EAAQ6M,gBAUfC,GAAU,SAAUtM,GACxB,MAAuB,mBAAT4C,GAAuB5C,aAAiB4C,GAGxD,SAAS2J,GAOPnI,EAAYoI,EAA+BC,GAC3ChQ,EAAa2H,GAAQsI,IACnBA,EAAKjB,KAAK1J,EAAWyK,EAAaC,EAAMvE,GAAO,GAEnD,CAWA,MAAMyE,GAAoB,SAAUH,GAClC,IAAI7I,EAAU,KAMd,GAHA4I,GAAcnI,GAAMK,uBAAwB+H,EAAa,MAGrDR,GAAaQ,GAEf,OADAvC,GAAauC,IACN,EAIT,MAAMI,EAAUtN,GAAkBkN,EAAYP,UAS9C,GANAM,GAAcnI,GAAMQ,oBAAqB4H,EAAa,CACpDI,UACAC,YAAa7H,KAKboB,IACAoG,EAAYH,kBACXC,GAAQE,EAAYM,oBACrBzO,EAAW,WAAYmO,EAAYpB,YACnC/M,EAAW,WAAYmO,EAAYN,aAGnC,OADAjC,GAAauC,IACN,EAIT,GAAIA,EAAYnK,WAAaZ,GAE3B,OADAwI,GAAauC,IACN,EAIT,GACEpG,IACAoG,EAAYnK,WAAaZ,IACzBpD,EAAW,UAAWmO,EAAYC,MAGlC,OADAxC,GAAauC,IACN,EAIT,IAAKxH,GAAa4H,IAAY/G,GAAY+G,GAAU,CAElD,IAAK/G,GAAY+G,IAAYG,GAAsBH,GAAU,CAC3D,GACEtH,GAAwBC,wBAAwBjH,QAChDD,EAAWiH,GAAwBC,aAAcqH,GAEjD,OAAO,EAGT,GACEtH,GAAwBC,wBAAwB+C,UAChDhD,GAAwBC,aAAaqH,GAErC,OAAO,CAEX,CAGA,GAAI/F,KAAiBG,GAAgB4F,GAAU,CAC7C,MAAMI,EAAaxJ,GAAcgJ,IAAgBA,EAAYQ,WACvDxB,EAAajI,GAAciJ,IAAgBA,EAAYhB,WAE7D,GAAIA,GAAcwB,EAAY,CAG5B,IAAK,IAAIC,EAFUzB,EAAW3M,OAEJ,EAAGoO,GAAK,IAAKA,EAAG,CACxC,MAAMC,EAAa9J,EAAUoI,EAAWyB,IAAI,GAC5CC,EAAWC,gBAAkBX,EAAYW,gBAAkB,GAAK,EAChEH,EAAW1B,aAAa4B,EAAY5J,GAAekJ,GACrD,CACF,CACF,CAGA,OADAvC,GAAauC,IACN,CACT,CAGA,OAAIA,aAAuBlK,IAvYA,SAAU9C,GACrC,IAAI4N,EAAS5J,GAAchE,GAItB4N,GAAWA,EAAOR,UACrBQ,EAAS,CACPhB,aAAc3E,GACdmF,QAAS,aAIb,MAAMA,EAAUtP,EAAkBkC,EAAQoN,SACpCS,EAAgB/P,EAAkB8P,EAAOR,SAE/C,QAAKjF,GAAmBnI,EAAQ4M,gBAI5B5M,EAAQ4M,eAAiB7E,GAIvB6F,EAAOhB,eAAiB5E,GACP,QAAZoF,EAMLQ,EAAOhB,eAAiB9E,GAEZ,QAAZsF,IACmB,mBAAlBS,GACCxF,GAA+BwF,IAM9BC,QAAQvD,GAAa6C,IAG1BpN,EAAQ4M,eAAiB9E,GAIvB8F,EAAOhB,eAAiB5E,GACP,SAAZoF,EAKLQ,EAAOhB,eAAiB7E,GACP,SAAZqF,GAAsB9E,GAAwBuF,GAKhDC,QAAQtD,GAAgB4C,IAG7BpN,EAAQ4M,eAAiB5E,KAKzB4F,EAAOhB,eAAiB7E,KACvBO,GAAwBuF,OAMzBD,EAAOhB,eAAiB9E,KACvBO,GAA+BwF,MAQ/BrD,GAAgB4C,KAChB7E,GAA6B6E,KAAa7C,GAAa6C,MAMpC,0BAAtB5E,KACAL,GAAmBnI,EAAQ4M,gBA4SUmB,CAAqBf,IAC1DvC,GAAauC,IACN,GAKM,aAAZI,GACa,YAAZA,GACY,aAAZA,IACFvO,EAAW,8BAA+BmO,EAAYpB,YAOpDjF,IAAsBqG,EAAYnK,WAAaZ,IAEjDkC,EAAU6I,EAAYN,YAEtBzP,EAAa,CAACsE,GAAeC,GAAUC,KAAeuM,IACpD7J,EAAU9F,EAAc8F,EAAS6J,EAAM,IAAI,IAGzChB,EAAYN,cAAgBvI,IAC9BzG,EAAU6E,EAAUI,QAAS,CAAE3C,QAASgN,EAAYpJ,cACpDoJ,EAAYN,YAAcvI,IAK9B4I,GAAcnI,GAAME,sBAAuBkI,EAAa,OAEjD,IAtBLvC,GAAauC,IACN,IAiCLiB,GAAoB,SACxBC,EACAC,EACA3N,GAGA,GACE2G,KACY,OAAXgH,GAA8B,SAAXA,KACnB3N,KAASoC,GAAYpC,KAASmI,IAE/B,OAAO,EAOT,GACEnC,KACCF,GAAY6H,IACbtP,EAAW6C,GAAWyM,SAGjB,GAAI5H,IAAmB1H,EAAW8C,GAAWwM,SAG7C,IAAKxI,GAAawI,IAAW7H,GAAY6H,IAC9C,KAIGZ,GAAsBW,KACnBpI,GAAwBC,wBAAwBjH,QAChDD,EAAWiH,GAAwBC,aAAcmI,IAChDpI,GAAwBC,wBAAwB+C,UAC/ChD,GAAwBC,aAAamI,MACvCpI,GAAwBK,8BAA8BrH,QACtDD,EAAWiH,GAAwBK,mBAAoBgI,IACtDrI,GAAwBK,8BAA8B2C,UACrDhD,GAAwBK,mBAAmBgI,KAGrC,OAAXA,GACCrI,GAAwBM,iCACtBN,GAAwBC,wBAAwBjH,QAChDD,EAAWiH,GAAwBC,aAAcvF,IAChDsF,GAAwBC,wBAAwB+C,UAC/ChD,GAAwBC,aAAavF,KAK3C,OAAO,OAGJ,GAAIoH,GAAoBuG,SAIxB,GACLtP,EAAW+C,GAAgBvD,EAAcmC,EAAOsB,GAAiB,WAK5D,GACO,QAAXqM,GAA+B,eAAXA,GAAsC,SAAXA,GACtC,WAAVD,GACkC,IAAlC3P,EAAciC,EAAO,WACrBkH,GAAcwG,IAMT,GACLzH,KACC5H,EAAWgD,GAAmBxD,EAAcmC,EAAOsB,GAAiB,WAIhE,GAAItB,EACT,OAAO,OAMT,OAAO,GAWH+M,GAAwB,SAAUH,GACtC,MAAmB,mBAAZA,GAAgCjP,EAAYiP,EAASpL,KAaxDoM,GAAsB,SAAUpB,GAEpCD,GAAcnI,GAAMI,yBAA0BgI,EAAa,MAE3D,MAAML,WAAEA,GAAeK,EAGvB,IAAKL,GAAcH,GAAaQ,GAC9B,OAGF,MAAMqB,EAAY,CAChBC,SAAU,GACVC,UAAW,GACXC,UAAU,EACVC,kBAAmB9I,GACnB+I,mBAAepM,GAEjB,IAAIvC,EAAI4M,EAAWtN,OAGnB,KAAOU,KAAK,CACV,MAAM4O,EAAOhC,EAAW5M,IAClB8K,KAAEA,EAAI+B,aAAEA,EAAcpM,MAAO+N,GAAcI,EAC3CR,EAASrO,GAAkB+K,GAE3B+D,EAAYL,EAClB,IAAI/N,EAAiB,UAATqK,EAAmB+D,EAAYnQ,EAAWmQ,GAsBtD,GAnBAP,EAAUC,SAAWH,EACrBE,EAAUE,UAAY/N,EACtB6N,EAAUG,UAAW,EACrBH,EAAUK,mBAAgBpM,EAC1ByK,GAAcnI,GAAMO,sBAAuB6H,EAAaqB,GACxD7N,EAAQ6N,EAAUE,WAKdnH,IAAoC,OAAX+G,GAA8B,SAAXA,IAE9CvD,GAAiBC,EAAMmC,GAGvBxM,EAt9B8B,gBAs9BQA,GAIpCoG,IAAgB/H,EAAW,gCAAiC2B,GAAQ,CACtEoK,GAAiBC,EAAMmC,GACvB,QACF,CAGA,GAAIqB,EAAUK,cACZ,SAIF,IAAKL,EAAUG,SAAU,CACvB5D,GAAiBC,EAAMmC,GACvB,QACF,CAGA,IAAKtG,IAA4B7H,EAAW,OAAQ2B,GAAQ,CAC1DoK,GAAiBC,EAAMmC,GACvB,QACF,CAGIrG,IACF1J,EAAa,CAACsE,GAAeC,GAAUC,KAAeuM,IACpDxN,EAAQnC,EAAcmC,EAAOwN,EAAM,IAAI,IAK3C,MAAME,EAAQpO,GAAkBkN,EAAYP,UAC5C,GAAKwB,GAAkBC,EAAOC,EAAQ3N,GAAtC,CAMA,GACE6D,IACwB,iBAAjBX,GACkC,mBAAlCA,EAAamL,iBAEpB,GAAIjC,QAGF,OAAQlJ,EAAamL,iBAAiBX,EAAOC,IAC3C,IAAK,cACH3N,EAAQ6D,GAAmBoF,WAAWjJ,GACtC,MAGF,IAAK,mBACHA,EAAQ6D,GAAmBqF,gBAAgBlJ,GAYnD,GAAIA,IAAUoO,EACZ,IACMhC,EACFI,EAAY8B,eAAelC,EAAc/B,EAAMrK,GAG/CwM,EAAY9B,aAAaL,EAAMrK,GAG7BgM,GAAaQ,GACfvC,GAAauC,GAEbxP,EAAS+E,EAAUI,QAEtB,CAAC,MAAOwH,GACPS,GAAiBC,EAAMmC,EACzB,CA9CF,MAFEpC,GAAiBC,EAAMmC,EAkD3B,CAGAD,GAAcnI,GAAMC,wBAAyBmI,EAAa,OAQtD+B,GAAqB,SAArBA,EAA+BC,GACnC,IAAIC,EAAa,KACjB,MAAMC,EAAiBhD,GAAoB8C,GAK3C,IAFAjC,GAAcnI,GAAMM,wBAAyB8J,EAAU,MAE/CC,EAAaC,EAAeC,YAElCpC,GAAcnI,GAAMS,uBAAwB4J,EAAY,MAGxD9B,GAAkB8B,GAGlBb,GAAoBa,GAGhBA,EAAW9K,mBAAmBjB,GAChC6L,EAAmBE,EAAW9K,SAKlC4I,GAAcnI,GAAMG,uBAAwBiK,EAAU,OAmOxD,OA/NAzM,EAAU6M,SAAW,SAAUhE,GAAe,IAARpC,EAAG5J,UAAAC,OAAA,QAAAiD,IAAAlD,UAAA,GAAAA,UAAA,GAAG,CAAA,EACtCyM,EAAO,KACPwD,EAAe,KACfrC,EAAc,KACdsC,EAAa,KAUjB,GANApH,IAAkBkD,EACdlD,KACFkD,EAAQ,eAIW,iBAAVA,IAAuB0B,GAAQ1B,GAAQ,CAChD,GAA8B,mBAAnBA,EAAMlN,SAMf,MAAMc,EAAgB,8BAJtB,GAAqB,iBADrBoM,EAAQA,EAAMlN,YAEZ,MAAMc,EAAgB,kCAK5B,CAGA,IAAKuD,EAAUQ,YACb,OAAOqI,EAgBT,GAZKtE,IACHiC,GAAaC,GAIfzG,EAAUI,QAAU,GAGC,iBAAVyI,IACT9D,IAAW,GAGTA,IAEF,GAAK8D,EAAeqB,SAAU,CAC5B,MAAMW,EAAUtN,GAAmBsL,EAAeqB,UAClD,IAAKjH,GAAa4H,IAAY/G,GAAY+G,GACxC,MAAMpO,EACJ,0DAGN,OACK,GAAIoM,aAAiBhI,EAG1ByI,EAAOV,GAAc,iBACrBkE,EAAexD,EAAKzH,cAAcO,WAAWyG,GAAO,GAElDiE,EAAaxM,WAAaZ,GACA,SAA1BoN,EAAa5C,UAIsB,SAA1B4C,EAAa5C,SADtBZ,EAAOwD,EAKPxD,EAAK0D,YAAYF,OAEd,CAEL,IACGrI,KACAL,KACAE,KAEuB,IAAxBuE,EAAM5M,QAAQ,KAEd,OAAO6F,IAAsB6C,GACzB7C,GAAmBoF,WAAW2B,GAC9BA,EAON,GAHAS,EAAOV,GAAcC,IAGhBS,EACH,OAAO7E,GAAa,KAAOE,GAAsB5C,GAAY,EAEjE,CAGIuH,GAAQ9E,IACV0D,GAAaoB,EAAK2D,YAIpB,MAAMC,EAAevD,GAAoB5E,GAAW8D,EAAQS,GAG5D,KAAQmB,EAAcyC,EAAaN,YAEjChC,GAAkBH,GAGlBoB,GAAoBpB,GAGhBA,EAAY7I,mBAAmBjB,GACjC6L,GAAmB/B,EAAY7I,SAKnC,GAAImD,GACF,OAAO8D,EAIT,GAAIpE,GAAY,CACd,GAAIC,GAGF,IAFAqI,EAAa7K,GAAuBwH,KAAKJ,EAAKzH,eAEvCyH,EAAK2D,YAEVF,EAAWC,YAAY1D,EAAK2D,iBAG9BF,EAAazD,EAcf,OAXIlG,GAAa+J,YAAc/J,GAAagK,kBAQ1CL,EAAa3K,GAAWsH,KAAKjJ,EAAkBsM,GAAY,IAGtDA,CACT,CAEA,IAAIM,EAAiB/I,GAAiBgF,EAAKgE,UAAYhE,EAAKD,UAsB5D,OAlBE/E,IACArB,GAAa,aACbqG,EAAKzH,eACLyH,EAAKzH,cAAc0L,SACnBjE,EAAKzH,cAAc0L,QAAQjF,MAC3BhM,EAAW0G,EAA0BsG,EAAKzH,cAAc0L,QAAQjF,QAEhE+E,EACE,aAAe/D,EAAKzH,cAAc0L,QAAQjF,KAAO,MAAQ+E,GAIzDjJ,IACF1J,EAAa,CAACsE,GAAeC,GAAUC,KAAeuM,IACpD4B,EAAiBvR,EAAcuR,EAAgB5B,EAAM,IAAI,IAItD3J,IAAsB6C,GACzB7C,GAAmBoF,WAAWmG,GAC9BA,GAGNrN,EAAUwN,UAAY,WACpBhH,GADiC3J,UAAAC,OAAA,QAAAiD,IAAAlD,UAAA,GAAAA,UAAA,GAAG,CAAA,GAEpC0H,IAAa,GAGfvE,EAAUyN,YAAc,WACtBtH,GAAS,KACT5B,IAAa,GAGfvE,EAAU0N,iBAAmB,SAAUC,EAAKvB,EAAMnO,GAE3CkI,IACHK,GAAa,CAAE,GAGjB,MAAMmF,EAAQpO,GAAkBoQ,GAC1B/B,EAASrO,GAAkB6O,GACjC,OAAOV,GAAkBC,EAAOC,EAAQ3N,IAG1C+B,EAAU4N,QAAU,SAAUC,EAAYC,GACZ,mBAAjBA,GAIX3S,EAAUkH,GAAMwL,GAAaC,IAG/B9N,EAAU+N,WAAa,SAAUF,EAAYC,GAC3C,QAAqB/N,IAAjB+N,EAA4B,CAC9B,MAAMlQ,EAAQ7C,EAAiBsH,GAAMwL,GAAaC,GAElD,OAAkB,IAAXlQ,OACHmC,EACA1E,EAAYgH,GAAMwL,GAAajQ,EAAO,GAAG,EAC/C,CAEA,OAAO3C,EAASoH,GAAMwL,KAGxB7N,EAAUgO,YAAc,SAAUH,GAChCxL,GAAMwL,GAAc,IAGtB7N,EAAUiO,eAAiB,WACzB5L,GAthDK,CACLC,wBAAyB,GACzBC,sBAAuB,GACvBC,uBAAwB,GACxBC,yBAA0B,GAC1BC,uBAAwB,GACxBC,wBAAyB,GACzBC,sBAAuB,GACvBC,oBAAqB,GACrBC,uBAAwB,KAghDnB9C,CACT,CAEeF"}