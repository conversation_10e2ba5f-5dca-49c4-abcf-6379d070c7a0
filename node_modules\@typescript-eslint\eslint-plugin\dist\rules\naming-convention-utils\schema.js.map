{"version": 3, "file": "schema.js", "sourceRoot": "", "sources": ["../../../src/rules/naming-convention-utils/schema.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,iDAAmC;AAKnC,mCAOiB;AAEjB,MAAM,iBAAiB,GAA2B;IAChD,IAAI,EAAE,QAAQ;IACd,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,yBAAiB,CAAC;CAC3C,CAAC;AACF,MAAM,oBAAoB,GAA2B;IACnD,IAAI,EAAE,OAAO;IACb,KAAK,EAAE;QACL,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE,CAAC;KACb;IACD,eAAe,EAAE,KAAK;CACvB,CAAC;AACF,MAAM,kBAAkB,GAA2B;IACjD,IAAI,EAAE,QAAQ;IACd,UAAU,EAAE;QACV,KAAK,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;QAC1B,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;KAC1B;IACD,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;CAC7B,CAAC;AAEF,MAAM,yBAAyB,GAAyB;IACtD,MAAM,EAAE;QACN,KAAK,EAAE;YACL;gBACE,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,yBAAiB,CAAC;iBAC3C;gBACD,eAAe,EAAE,KAAK;aACvB;YACD;gBACE,IAAI,EAAE,MAAM;aACb;SACF;KACF;IACD,MAAM,EAAE,kBAAkB;IAC1B,iBAAiB,EAAE,iBAAiB;IACpC,kBAAkB,EAAE,iBAAiB;IACrC,MAAM,EAAE,oBAAoB;IAC5B,MAAM,EAAE,oBAAoB;IAC5B,cAAc,EAAE;QACd,IAAI,EAAE,QAAQ;KACf;CACF,CAAC;AACF,SAAS,cAAc,CACrB,cAAgD,EAChD,SAAkB,EAClB,SAA6B;IAE7B,MAAM,QAAQ,GAAyB;QACrC,MAAM,EAAE;YACN,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,QAAQ;oBACd,SAAS,EAAE,CAAC;iBACb;gBACD,kBAAkB;aACnB;SACF;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,CAAC,cAAc,CAAC;SACvB;KACF,CAAC;IACF,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;QACrC,QAAQ,CAAC,SAAS,GAAG;YACnB,IAAI,EAAE,OAAO;YACb,KAAK,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,SAAS;aAChB;YACD,eAAe,EAAE,KAAK;SACvB,CAAC;KACH;IACD,IAAI,SAAS,EAAE;QACb,QAAQ,CAAC,KAAK,GAAG;YACf,IAAI,EAAE,OAAO;YACb,KAAK,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,qBAAa,CAAC;aACvC;YACD,eAAe,EAAE,KAAK;SACvB,CAAC;KACH;IAED,OAAO;QACL;YACE,IAAI,EAAE,QAAQ;YACd,UAAU,kCACL,yBAAyB,GACzB,QAAQ,CACZ;YACD,QAAQ,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;YAChC,oBAAoB,EAAE,KAAK;SAC5B;KACF,CAAC;AACJ,CAAC;AAED,SAAS,eAAe;IACtB,OAAO;QACL,IAAI,EAAE,QAAQ;QACd,UAAU,kCACL,yBAAyB,GACzB;YACD,MAAM,EAAE;gBACN,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,QAAQ;wBACd,SAAS,EAAE,CAAC;qBACb;oBACD,kBAAkB;iBACnB;aACF;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE;wBACJ,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAa,CAAC;wBACnC,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAS,CAAC;qBAChC;iBACF;gBACD,eAAe,EAAE,KAAK;aACvB;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,iBAAS,CAAC;iBACnC;gBACD,eAAe,EAAE,KAAK;aACvB;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,qBAAa,CAAC;iBACvC;gBACD,eAAe,EAAE,KAAK;aACvB;SACF,CACF;QACD,QAAQ,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;QAChC,oBAAoB,EAAE,KAAK;KAC5B,CAAC;AACJ,CAAC;AAED,MAAM,MAAM,GAA2B;IACrC,IAAI,EAAE,OAAO;IACb,KAAK,EAAE;QACL,KAAK,EAAE;YACL,eAAe,EAAE;YACjB,GAAG,cAAc,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,iBAAS,CAAC,CAAC;YAEjE,GAAG,cAAc,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC7D,GAAG,cAAc,CAAC,UAAU,EAAE,IAAI,EAAE;gBAClC,OAAO;gBACP,cAAc;gBACd,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,OAAO;aACR,CAAC;YACF,GAAG,cAAc,CAAC,UAAU,EAAE,KAAK,EAAE;gBACnC,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,OAAO;aACR,CAAC;YACF,GAAG,cAAc,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YAEhE,GAAG,cAAc,CAAC,YAAY,EAAE,KAAK,EAAE;gBACrC,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,gBAAgB;gBAChB,QAAQ;gBACR,UAAU;gBACV,OAAO;aACR,CAAC;YACF,GAAG,cAAc,CAAC,eAAe,EAAE,IAAI,EAAE;gBACvC,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,gBAAgB;gBAChB,QAAQ;gBACR,UAAU;aACX,CAAC;YACF,GAAG,cAAc,CAAC,uBAAuB,EAAE,IAAI,EAAE;gBAC/C,QAAQ;gBACR,gBAAgB;aACjB,CAAC;YACF,GAAG,cAAc,CAAC,cAAc,EAAE,IAAI,EAAE;gBACtC,QAAQ;gBACR,UAAU;gBACV,gBAAgB;aACjB,CAAC;YACF,GAAG,cAAc,CAAC,mBAAmB,EAAE,IAAI,EAAE;gBAC3C,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,UAAU;aACX,CAAC;YACF,GAAG,cAAc,CAAC,UAAU,EAAE,IAAI,EAAE;gBAClC,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,gBAAgB;gBAChB,QAAQ;gBACR,UAAU;gBACV,OAAO;aACR,CAAC;YAEF,GAAG,cAAc,CAAC,aAAa,EAAE,KAAK,EAAE;gBACtC,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,gBAAgB;gBAChB,QAAQ;gBACR,UAAU;gBACV,OAAO;aACR,CAAC;YACF,GAAG,cAAc,CAAC,qBAAqB,EAAE,KAAK,EAAE;gBAC9C,QAAQ;gBACR,gBAAgB;gBAChB,OAAO;aACR,CAAC;YACF,GAAG,cAAc,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YACpE,GAAG,cAAc,CAAC,QAAQ,EAAE,KAAK,EAAE;gBACjC,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,gBAAgB;gBAChB,QAAQ;gBACR,UAAU;gBACV,OAAO;aACR,CAAC;YACF,GAAG,cAAc,CAAC,UAAU,EAAE,IAAI,EAAE;gBAClC,UAAU;gBACV,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,gBAAgB;gBAChB,QAAQ;gBACR,UAAU;aACX,CAAC;YACF,GAAG,cAAc,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC,gBAAgB,CAAC,CAAC;YAE1D,GAAG,cAAc,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YACxE,GAAG,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YACrE,GAAG,cAAc,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC7D,GAAG,cAAc,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC7D,GAAG,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACxD,GAAG,cAAc,CAAC,eAAe,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC;SACtD;KACF;IACD,eAAe,EAAE,KAAK;CACvB,CAAC;AAEO,wBAAM"}