"use strict";var e=require("@react-pdf-viewer/core");function o(e){var o=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var l=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(o,t,l.get?l:{enumerable:!0,get:function(){return e[t]}})}})),o.default=e,Object.freeze(o)}var t=o(require("react")),l=function(){return t.createElement(e.Icon,{size:16},t.createElement("rect",{x:"0.5",y:"0.497",width:"22",height:"22",rx:"1",ry:"1"}),t.createElement("line",{x1:"0.5",y1:"6.497",x2:"22.5",y2:"6.497"}),t.createElement("line",{x1:"11.5",y1:"6.497",x2:"11.5",y2:"22.497"}))},c=function(){return t.createElement(e.Icon,{size:16},t.createElement("rect",{x:"0.5",y:"0.497",width:"22",height:"22",rx:"1",ry:"1"}),t.createElement("line",{x1:"11.5",y1:"0.497",x2:"11.5",y2:"22.497"}))},n=function(){return t.createElement(e.Icon,{size:16},t.createElement("path",{d:"M6.5,21.5c0,0.552-0.448,1-1,1h-4c-0.552,0-1-0.448-1-1v-20c0-0.552,0.448-1,1-1h4c0.552,0,1,0.448,1,1V21.5z\n            M14.5,21.5c0,0.552-0.448,1-1,1h-4c-0.552,0-1-0.448-1-1v-20c0-0.552,0.448-1,1-1h4c0.552,0,1,0.448,1,1V21.5z\n            M22.5,21.5 c0,0.552-0.448,1-1,1h-4c-0.552,0-1-0.448-1-1v-20c0-0.552,0.448-1,1-1h4c0.552,0,1,0.448,1,1V21.5z"}))},r=function(){return t.createElement(e.Icon,{size:16},t.createElement("rect",{x:"0.5",y:"0.497",width:"22",height:"22",rx:"1",ry:"1"}))},i=function(){return i=Object.assign||function(e){for(var o,t=1,l=arguments.length;t<l;t++)for(var c in o=arguments[t])Object.prototype.hasOwnProperty.call(o,c)&&(e[c]=o[c]);return e},i.apply(this,arguments)},a=function(o,t){o.get("switchScrollMode")(t);var l=o.get("viewMode");t!==e.ScrollMode.Horizontal&&t!==e.ScrollMode.Wrapped||l===e.ViewMode.SinglePage||o.get("switchViewMode")(e.ViewMode.SinglePage)},d=function(){return t.createElement(e.Icon,{size:16},t.createElement("path",{d:"M23.5,5.5c0,0.552-0.448,1-1,1h-21c-0.552,0-1-0.448-1-1v-3c0-0.552,0.448-1,1-1h21c0.552,0,1,0.448,1,1V5.5z\n            M23.5,13.5c0,0.552-0.448,1-1,1h-21c-0.552,0-1-0.448-1-1v-3c0-0.552,0.448-1,1-1h21c0.552,0,1,0.448,1,1V13.5z\n            M23.5,21.5 c0,0.552-0.448,1-1,1h-21c-0.552,0-1-0.448-1-1v-3c0-0.552,0.448-1,1-1h21c0.552,0,1,0.448,1,1V21.5z"}))},s=function(){return t.createElement(e.Icon,{size:16},t.createElement("path",{d:"M10.5,9.5c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V9.5z\n            M23.5,9.5c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V9.5z\n            M10.5,22.5 c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V22.5z\n            M23.5,22.5c0,0.552-0.448,1-1,1 h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V22.5z"}))},u=function(o){var l=o.children,c=o.mode,i=o.onClick,a=t.useContext(e.LocalizationContext).l10n,u="",m=t.createElement(d,null);switch(c){case e.ScrollMode.Horizontal:u=a&&a.scrollMode?a.scrollMode.horizontalScrolling:"Horizontal scrolling",m=t.createElement(n,null);break;case e.ScrollMode.Page:u=a&&a.scrollMode?a.scrollMode.pageScrolling:"Page scrolling",m=t.createElement(r,null);break;case e.ScrollMode.Wrapped:u=a&&a.scrollMode?a.scrollMode.wrappedScrolling:"Wrapped scrolling",m=t.createElement(s,null);break;case e.ScrollMode.Vertical:default:u=a&&a.scrollMode?a.scrollMode.verticalScrolling:"Vertical scrolling",m=t.createElement(d,null)}return l({icon:m,label:u,onClick:i})},m={left:0,top:8},M=function(o){var l=o.isDisabled,c=o.isSelected,n=o.mode,r=o.onClick,i="";switch(n){case e.ScrollMode.Horizontal:i="scroll-mode__horizontal-button";break;case e.ScrollMode.Page:i="scroll-mode__page-button";break;case e.ScrollMode.Wrapped:i="scroll-mode__wrapped-button";break;case e.ScrollMode.Vertical:default:i="scroll-mode__vertical-button"}return t.createElement(u,{mode:n,onClick:r},(function(o){return t.createElement(e.Tooltip,{ariaControlsSuffix:"scroll-mode-switch",position:e.Position.BottomCenter,target:t.createElement(e.MinimalButton,{ariaLabel:o.label,isDisabled:l,isSelected:c,testId:i,onClick:o.onClick},o.icon),content:function(){return o.label},offset:m})}))},f=function(o){var l=t.useState(o.get("scrollMode")||e.ScrollMode.Vertical),c=l[0],n=l[1],r=function(e){n(e)};return t.useEffect((function(){return o.subscribe("scrollMode",r),function(){o.unsubscribe("scrollMode",r)}}),[]),{scrollMode:c}},w=function(o){var l=t.useState(o.get("viewMode")||e.ViewMode.SinglePage),c=l[0],n=l[1],r=function(e){n(e)};return t.useEffect((function(){return o.subscribe("viewMode",r),function(){o.unsubscribe("viewMode",r)}}),[]),{viewMode:c}},S=function(o){var l=o.children,c=o.mode,n=o.store,r=w(n).viewMode,i=f(n).scrollMode===c,d=(c===e.ScrollMode.Horizontal||c===e.ScrollMode.Wrapped)&&r!==e.ViewMode.SinglePage;return(l||function(e){return t.createElement(M,{isDisabled:d,isSelected:i,mode:e.mode,onClick:e.onClick})})({isDisabled:d,isSelected:i,mode:c,onClick:function(){a(n,c)}})},g=function(o){var l=o.isDisabled,c=o.isSelected,n=o.mode,r=o.onClick,i="";switch(n){case e.ScrollMode.Horizontal:i="scroll-mode__horizontal-menu";break;case e.ScrollMode.Page:i="scroll-mode__page-menu";break;case e.ScrollMode.Wrapped:i="scroll-mode__wrapped-menu";break;case e.ScrollMode.Vertical:default:i="scroll-mode__vertical-menu"}return t.createElement(u,{mode:n,onClick:r},(function(o){return t.createElement(e.MenuItem,{checked:c,icon:o.icon,isDisabled:l,testId:i,onClick:o.onClick},o.label)}))},h=function(o,t){o.get("switchViewMode")(t);var l=o.get("scrollMode");l!==e.ScrollMode.Horizontal&&l!==e.ScrollMode.Wrapped||t===e.ViewMode.SinglePage||o.get("switchScrollMode")(e.ScrollMode.Vertical)},b=function(o){var n=o.children,i=o.mode,a=o.onClick,d=t.useContext(e.LocalizationContext).l10n,s="",u=t.createElement(r,null);switch(i){case e.ViewMode.DualPage:s=d&&d.scrollMode?d.scrollMode.dualPage:"Dual page",u=t.createElement(c,null);break;case e.ViewMode.DualPageWithCover:s=d&&d.scrollMode?d.scrollMode.dualPageCover:"Dual page with cover",u=t.createElement(l,null);break;case e.ViewMode.SinglePage:default:s=d&&d.scrollMode?d.scrollMode.singlePage:"Single page",u=t.createElement(r,null)}return n({icon:u,label:s,onClick:a})},p={left:0,top:8},v=function(o){var l=o.isDisabled,c=o.isSelected,n=o.mode,r=o.onClick,i="";switch(n){case e.ViewMode.DualPage:i="view-mode__dual-button";break;case e.ViewMode.DualPageWithCover:i="view-mode__dual-cover-button";break;case e.ViewMode.SinglePage:default:i="view-mode__single-button"}return t.createElement(b,{mode:n,onClick:r},(function(o){return t.createElement(e.Tooltip,{ariaControlsSuffix:"view-mode-switch",position:e.Position.BottomCenter,target:t.createElement(e.MinimalButton,{ariaLabel:o.label,isDisabled:l,isSelected:c,testId:i,onClick:o.onClick},o.icon),content:function(){return o.label},offset:p})}))},k=function(o){var l=o.children,c=o.mode,n=o.store,r=w(n).viewMode,i=f(n).scrollMode,a=r===c,d=(i===e.ScrollMode.Horizontal||i===e.ScrollMode.Wrapped)&&c!==e.ViewMode.SinglePage;return(l||function(e){return t.createElement(v,{isDisabled:d,isSelected:a,mode:e.mode,onClick:e.onClick})})({isDisabled:d,isSelected:a,mode:c,onClick:function(){h(n,c)}})},C=function(o){var l=o.isDisabled,c=o.isSelected,n=o.mode,r=o.onClick,i="";switch(n){case e.ViewMode.DualPage:i="view-mode__dual-menu";break;case e.ViewMode.DualPageWithCover:i="view-mode__dual-cover-menu";break;case e.ViewMode.SinglePage:default:i="view-mode__single-menu"}return t.createElement(b,{mode:n,onClick:r},(function(o){return t.createElement(e.MenuItem,{checked:c,icon:o.icon,isDisabled:l,testId:i,onClick:o.onClick},o.label)}))};exports.DualPageCoverViewModeIcon=l,exports.DualPageViewModeIcon=c,exports.HorizontalScrollingIcon=n,exports.PageScrollingIcon=r,exports.VerticalScrollingIcon=d,exports.WrappedScrollingIcon=s,exports.scrollModePlugin=function(){var o=t.useMemo((function(){return e.createStore({scrollMode:e.ScrollMode.Vertical,viewMode:e.ViewMode.SinglePage,switchScrollMode:function(){},switchViewMode:function(){}})}),[]),l=function(e){return t.createElement(S,i({},e,{store:o}))},c=function(e){return t.createElement(k,i({},e,{store:o}))};return{install:function(e){o.update("switchScrollMode",e.switchScrollMode),o.update("switchViewMode",e.switchViewMode)},onViewerStateChange:function(e){return o.update("scrollMode",e.scrollMode),o.update("viewMode",e.viewMode),e},switchScrollMode:function(e){a(o,e)},switchViewMode:function(e){h(o,e)},SwitchScrollMode:l,SwitchScrollModeButton:function(e){return t.createElement(l,{mode:e.mode},(function(e){return t.createElement(M,{isDisabled:e.isDisabled,isSelected:e.isSelected,mode:e.mode,onClick:function(){e.onClick()}})}))},SwitchScrollModeMenuItem:function(e){return t.createElement(l,{mode:e.mode},(function(o){return t.createElement(g,{isDisabled:o.isDisabled,isSelected:o.isSelected,mode:o.mode,onClick:function(){o.onClick(),e.onClick()}})}))},SwitchViewMode:c,SwitchViewModeButton:function(e){return t.createElement(c,{mode:e.mode},(function(e){return t.createElement(v,{isDisabled:e.isDisabled,isSelected:e.isSelected,mode:e.mode,onClick:function(){e.onClick()}})}))},SwitchViewModeMenuItem:function(e){return t.createElement(c,{mode:e.mode},(function(o){return t.createElement(C,{isDisabled:o.isDisabled,isSelected:o.isSelected,mode:o.mode,onClick:function(){o.onClick(),e.onClick()}})}))}}};
