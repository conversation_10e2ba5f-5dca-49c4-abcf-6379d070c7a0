[{"C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\JBS.js": "4", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\JIS.js": "5", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Home.js": "6", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\JNS.js": "7", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Contact.js": "8", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Submit.js": "9", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\VolumeDetail.js": "10", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\PublicationDetail.js": "11", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\VolumeIssues.js": "12", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\VolumesIssues.js": "13", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\IssueDetail.js": "14", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Login.js": "15", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Register.js": "16", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Profile.js": "17", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\EditorPanel.js": "18", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\AdminPanel.js": "19", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\EditorialBoardPage.js": "20", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ReviewerPanel.js": "21", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Community.js": "22", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Resources.js": "23", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Events.js": "24", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Careers.js": "25", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Help.js": "26", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\SubmissionGuidelines.js": "27", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\AuthorHandbook.js": "28", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ConflictOfInterest.js": "29", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\CopyrightTransfer.js": "30", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ManuscriptTemplate.js": "31", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ProfileSubmissions.js": "32", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ProfileBlogs.js": "33", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ProfileFavorites.js": "34", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\SubmitPdf.js": "35", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\NotFound.js": "36", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ReviewerApplication.js": "37", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\BannedPage.js": "38", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ProfileNotifications.js": "39", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\BlogAdminList.js": "40", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\BlogAdmin.js": "41", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\EventAdmin.js": "42", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\AdminAnnouncements.js": "43", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\AdminEditorialBoardSubmissions.js": "44", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\CitationManager.js": "45", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\blog\\BlogList.js": "46", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\blog\\BlogPost.js": "47", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\blog\\BlogSubmit.js": "48", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\countryList.js": "49", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ProfileAuthoredPublications.js": "50", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\EditorialBoardManager.js": "51", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\supabaseClient.js": "52", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\UsersAdmin.js": "53", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\CrawlingStats.js": "54", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\utils\\impactFactor.js": "55", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\components\\PublicationMetaTags.js": "56", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\utils\\citationSystem.js": "57", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\blog\\Comments.js": "58", "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\DatabaseTest.js": "59"}, {"size": 880, "mtime": 1752915232000, "results": "60", "hashOfConfig": "61"}, {"size": 13123, "mtime": 1753191666880, "results": "62", "hashOfConfig": "61"}, {"size": 362, "mtime": 1752521412000, "results": "63", "hashOfConfig": "61"}, {"size": 21313, "mtime": 1753191978551, "results": "64", "hashOfConfig": "61"}, {"size": 19294, "mtime": 1753191943345, "results": "65", "hashOfConfig": "61"}, {"size": 25536, "mtime": 1752914890000, "results": "66", "hashOfConfig": "61"}, {"size": 20743, "mtime": 1753191997283, "results": "67", "hashOfConfig": "61"}, {"size": 2252, "mtime": 1752914366000, "results": "68", "hashOfConfig": "61"}, {"size": 17135, "mtime": 1753275160584, "results": "69", "hashOfConfig": "61"}, {"size": 7152, "mtime": 1752526996000, "results": "70", "hashOfConfig": "61"}, {"size": 11531, "mtime": 1752913100000, "results": "71", "hashOfConfig": "61"}, {"size": 2153, "mtime": 1752527934000, "results": "72", "hashOfConfig": "61"}, {"size": 5330, "mtime": 1752914476000, "results": "73", "hashOfConfig": "61"}, {"size": 10541, "mtime": 1752529086000, "results": "74", "hashOfConfig": "61"}, {"size": 2251, "mtime": 1752914488000, "results": "75", "hashOfConfig": "61"}, {"size": 11009, "mtime": 1752914500000, "results": "76", "hashOfConfig": "61"}, {"size": 23884, "mtime": 1752914518000, "results": "77", "hashOfConfig": "61"}, {"size": 5139, "mtime": 1752546100000, "results": "78", "hashOfConfig": "61"}, {"size": 23364, "mtime": 1753172751508, "results": "79", "hashOfConfig": "61"}, {"size": 8936, "mtime": 1752935400000, "results": "80", "hashOfConfig": "61"}, {"size": 3846, "mtime": 1752546106000, "results": "81", "hashOfConfig": "61"}, {"size": 6733, "mtime": 1752914560000, "results": "82", "hashOfConfig": "61"}, {"size": 6663, "mtime": 1752914546000, "results": "83", "hashOfConfig": "61"}, {"size": 3602, "mtime": 1752914570000, "results": "84", "hashOfConfig": "61"}, {"size": 5988, "mtime": 1752914582000, "results": "85", "hashOfConfig": "61"}, {"size": 9728, "mtime": 1752914596000, "results": "86", "hashOfConfig": "61"}, {"size": 1402, "mtime": 1752749656000, "results": "87", "hashOfConfig": "61"}, {"size": 790, "mtime": 1752749446000, "results": "88", "hashOfConfig": "61"}, {"size": 775, "mtime": 1752749684000, "results": "89", "hashOfConfig": "61"}, {"size": 780, "mtime": 1752749674000, "results": "90", "hashOfConfig": "61"}, {"size": 804, "mtime": 1752749456000, "results": "91", "hashOfConfig": "61"}, {"size": 3168, "mtime": 1752751506000, "results": "92", "hashOfConfig": "61"}, {"size": 2446, "mtime": 1752751516000, "results": "93", "hashOfConfig": "61"}, {"size": 3677, "mtime": 1752754036000, "results": "94", "hashOfConfig": "61"}, {"size": 9280, "mtime": 1752825612000, "results": "95", "hashOfConfig": "61"}, {"size": 793, "mtime": 1752828462000, "results": "96", "hashOfConfig": "61"}, {"size": 11594, "mtime": 1752837932000, "results": "97", "hashOfConfig": "61"}, {"size": 5088, "mtime": 1752909700000, "results": "98", "hashOfConfig": "61"}, {"size": 2749, "mtime": 1752753116000, "results": "99", "hashOfConfig": "61"}, {"size": 3063, "mtime": 1752700680000, "results": "100", "hashOfConfig": "61"}, {"size": 14473, "mtime": 1752621674000, "results": "101", "hashOfConfig": "61"}, {"size": 8497, "mtime": 1752702124000, "results": "102", "hashOfConfig": "61"}, {"size": 5902, "mtime": 1752828766000, "results": "103", "hashOfConfig": "61"}, {"size": 6244, "mtime": 1752837132000, "results": "104", "hashOfConfig": "61"}, {"size": 45716, "mtime": 1753207568866, "results": "105", "hashOfConfig": "61"}, {"size": 2839, "mtime": 1752914534000, "results": "106", "hashOfConfig": "61"}, {"size": 13104, "mtime": 1752910524000, "results": "107", "hashOfConfig": "61"}, {"size": 14574, "mtime": 1752629678000, "results": "108", "hashOfConfig": "61"}, {"size": 3143, "mtime": 1752823314000, "results": "109", "hashOfConfig": "61"}, {"size": 3655, "mtime": 1752823716000, "results": "110", "hashOfConfig": "61"}, {"size": 11217, "mtime": 1752549680000, "results": "111", "hashOfConfig": "61"}, {"size": 421, "mtime": 1752529474000, "results": "112", "hashOfConfig": "61"}, {"size": 25250, "mtime": 1752909260000, "results": "113", "hashOfConfig": "61"}, {"size": 9972, "mtime": 1752921318000, "results": "114", "hashOfConfig": "61"}, {"size": 8113, "mtime": 1753191788521, "results": "115", "hashOfConfig": "61"}, {"size": 4005, "mtime": 1752913002000, "results": "116", "hashOfConfig": "61"}, {"size": 6193, "mtime": 1752920640000, "results": "117", "hashOfConfig": "61"}, {"size": 18386, "mtime": 1752911724000, "results": "118", "hashOfConfig": "61"}, {"size": 6664, "mtime": 1753195392791, "results": "119", "hashOfConfig": "61"}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lwe2lh", {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\JBS.js", ["297", "298"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\JIS.js", ["299", "300"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Home.js", ["301", "302"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\JNS.js", ["303", "304"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Contact.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Submit.js", ["305", "306", "307"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\VolumeDetail.js", ["308", "309", "310", "311"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\PublicationDetail.js", ["312", "313", "314"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\VolumeIssues.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\VolumesIssues.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\IssueDetail.js", ["315", "316", "317", "318", "319", "320"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Login.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Register.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Profile.js", ["321", "322", "323", "324"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\EditorPanel.js", ["325"], ["326"], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\AdminPanel.js", ["327", "328", "329", "330", "331"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\EditorialBoardPage.js", [], ["332"], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ReviewerPanel.js", [], ["333"], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Community.js", ["334"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Resources.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Events.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Careers.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\Help.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\SubmissionGuidelines.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\AuthorHandbook.js", ["335"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ConflictOfInterest.js", ["336"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\CopyrightTransfer.js", ["337"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ManuscriptTemplate.js", ["338"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ProfileSubmissions.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ProfileBlogs.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ProfileFavorites.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\SubmitPdf.js", ["339", "340"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\NotFound.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ReviewerApplication.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\BannedPage.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ProfileNotifications.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\BlogAdminList.js", ["341", "342", "343", "344", "345", "346", "347", "348", "349"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\BlogAdmin.js", ["350", "351", "352"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\EventAdmin.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\AdminAnnouncements.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\AdminEditorialBoardSubmissions.js", ["353"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\CitationManager.js", ["354", "355", "356"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\blog\\BlogList.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\blog\\BlogPost.js", ["357"], ["358", "359"], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\blog\\BlogSubmit.js", ["360", "361", "362", "363"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\countryList.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\ProfileAuthoredPublications.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\EditorialBoardManager.js", [], ["364"], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\supabaseClient.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\UsersAdmin.js", ["365", "366", "367"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\admin\\CrawlingStats.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\utils\\impactFactor.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\components\\PublicationMetaTags.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\utils\\citationSystem.js", ["368", "369"], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\blog\\Comments.js", [], [], "C:\\Users\\<USER>\\Downloads\\Darsgah-e-ahlebait\\darsgah-e-ahlebait\\src\\DatabaseTest.js", [], [], {"ruleId": "370", "severity": 1, "message": "371", "line": 5, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 5, "endColumn": 17}, {"ruleId": "370", "severity": 1, "message": "374", "line": 6, "column": 27, "nodeType": "372", "messageId": "373", "endLine": 6, "endColumn": 45}, {"ruleId": "370", "severity": 1, "message": "375", "line": 5, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 5, "endColumn": 17}, {"ruleId": "370", "severity": 1, "message": "374", "line": 6, "column": 27, "nodeType": "372", "messageId": "373", "endLine": 6, "endColumn": 45}, {"ruleId": "376", "severity": 1, "message": "377", "line": 397, "column": 17, "nodeType": "378", "endLine": 397, "endColumn": 85}, {"ruleId": "376", "severity": 1, "message": "377", "line": 405, "column": 17, "nodeType": "378", "endLine": 405, "endColumn": 85}, {"ruleId": "370", "severity": 1, "message": "371", "line": 5, "column": 55, "nodeType": "372", "messageId": "373", "endLine": 5, "endColumn": 62}, {"ruleId": "370", "severity": 1, "message": "374", "line": 6, "column": 27, "nodeType": "372", "messageId": "373", "endLine": 6, "endColumn": 45}, {"ruleId": "370", "severity": 1, "message": "379", "line": 6, "column": 8, "nodeType": "372", "messageId": "373", "endLine": 6, "endColumn": 19}, {"ruleId": "370", "severity": 1, "message": "380", "line": 30, "column": 9, "nodeType": "372", "messageId": "373", "endLine": 30, "endColumn": 15}, {"ruleId": "370", "severity": 1, "message": "381", "line": 209, "column": 15, "nodeType": "372", "messageId": "373", "endLine": 209, "endColumn": 19}, {"ruleId": "370", "severity": 1, "message": "382", "line": 13, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 13, "endColumn": 16}, {"ruleId": "370", "severity": 1, "message": "383", "line": 33, "column": 21, "nodeType": "372", "messageId": "373", "endLine": 33, "endColumn": 26}, {"ruleId": "370", "severity": 1, "message": "384", "line": 49, "column": 40, "nodeType": "372", "messageId": "373", "endLine": 49, "endColumn": 46}, {"ruleId": "370", "severity": 1, "message": "385", "line": 56, "column": 37, "nodeType": "372", "messageId": "373", "endLine": 56, "endColumn": 47}, {"ruleId": "370", "severity": 1, "message": "386", "line": 4, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 4, "endColumn": 20}, {"ruleId": "370", "severity": 1, "message": "387", "line": 20, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 20, "endColumn": 20}, {"ruleId": "370", "severity": 1, "message": "388", "line": 21, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 21, "endColumn": 24}, {"ruleId": "370", "severity": 1, "message": "389", "line": 22, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 22, "endColumn": 22}, {"ruleId": "370", "severity": 1, "message": "383", "line": 51, "column": 21, "nodeType": "372", "messageId": "373", "endLine": 51, "endColumn": 26}, {"ruleId": "370", "severity": 1, "message": "383", "line": 67, "column": 21, "nodeType": "372", "messageId": "373", "endLine": 67, "endColumn": 26}, {"ruleId": "370", "severity": 1, "message": "384", "line": 87, "column": 40, "nodeType": "372", "messageId": "373", "endLine": 87, "endColumn": 46}, {"ruleId": "370", "severity": 1, "message": "385", "line": 94, "column": 37, "nodeType": "372", "messageId": "373", "endLine": 94, "endColumn": 47}, {"ruleId": "370", "severity": 1, "message": "390", "line": 106, "column": 44, "nodeType": "372", "messageId": "373", "endLine": 106, "endColumn": 57}, {"ruleId": "370", "severity": 1, "message": "391", "line": 4, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 4, "endColumn": 14}, {"ruleId": "370", "severity": 1, "message": "392", "line": 199, "column": 9, "nodeType": "372", "messageId": "373", "endLine": 199, "endColumn": 25}, {"ruleId": "370", "severity": 1, "message": "383", "line": 255, "column": 13, "nodeType": "372", "messageId": "373", "endLine": 255, "endColumn": 18}, {"ruleId": "370", "severity": 1, "message": "381", "line": 277, "column": 11, "nodeType": "372", "messageId": "373", "endLine": 277, "endColumn": 15}, {"ruleId": "370", "severity": 1, "message": "393", "line": 4, "column": 7, "nodeType": "372", "messageId": "373", "endLine": 4, "endColumn": 21}, {"ruleId": "394", "severity": 1, "message": "395", "line": 25, "column": 6, "nodeType": "396", "endLine": 25, "endColumn": 12, "suggestions": "397", "suppressions": "398"}, {"ruleId": "370", "severity": 1, "message": "399", "line": 8, "column": 8, "nodeType": "372", "messageId": "373", "endLine": 8, "endColumn": 13}, {"ruleId": "370", "severity": 1, "message": "400", "line": 9, "column": 8, "nodeType": "372", "messageId": "373", "endLine": 9, "endColumn": 14}, {"ruleId": "370", "severity": 1, "message": "401", "line": 119, "column": 9, "nodeType": "372", "messageId": "373", "endLine": 119, "endColumn": 29}, {"ruleId": "370", "severity": 1, "message": "402", "line": 123, "column": 9, "nodeType": "372", "messageId": "373", "endLine": 123, "endColumn": 30}, {"ruleId": "370", "severity": 1, "message": "403", "line": 189, "column": 38, "nodeType": "372", "messageId": "373", "endLine": 189, "endColumn": 47}, {"ruleId": "394", "severity": 1, "message": "404", "line": 36, "column": 6, "nodeType": "396", "endLine": 36, "endColumn": 19, "suggestions": "405", "suppressions": "406"}, {"ruleId": "394", "severity": 1, "message": "395", "line": 22, "column": 6, "nodeType": "396", "endLine": 22, "endColumn": 12, "suggestions": "407", "suppressions": "408"}, {"ruleId": "370", "severity": 1, "message": "391", "line": 3, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 3, "endColumn": 14}, {"ruleId": "376", "severity": 1, "message": "377", "line": 15, "column": 72, "nodeType": "378", "endLine": 15, "endColumn": 84}, {"ruleId": "376", "severity": 1, "message": "377", "line": 8, "column": 60, "nodeType": "378", "endLine": 8, "endColumn": 72}, {"ruleId": "376", "severity": 1, "message": "377", "line": 8, "column": 60, "nodeType": "378", "endLine": 8, "endColumn": 72}, {"ruleId": "376", "severity": 1, "message": "377", "line": 8, "column": 64, "nodeType": "378", "endLine": 8, "endColumn": 76}, {"ruleId": "370", "severity": 1, "message": "409", "line": 12, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 12, "endColumn": 17}, {"ruleId": "370", "severity": 1, "message": "381", "line": 144, "column": 15, "nodeType": "372", "messageId": "373", "endLine": 144, "endColumn": 19}, {"ruleId": "370", "severity": 1, "message": "399", "line": 4, "column": 8, "nodeType": "372", "messageId": "373", "endLine": 4, "endColumn": 13}, {"ruleId": "370", "severity": 1, "message": "400", "line": 5, "column": 8, "nodeType": "372", "messageId": "373", "endLine": 5, "endColumn": 14}, {"ruleId": "370", "severity": 1, "message": "410", "line": 11, "column": 9, "nodeType": "372", "messageId": "373", "endLine": 11, "endColumn": 17}, {"ruleId": "370", "severity": 1, "message": "411", "line": 12, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 12, "endColumn": 19}, {"ruleId": "370", "severity": 1, "message": "412", "line": 13, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 13, "endColumn": 15}, {"ruleId": "370", "severity": 1, "message": "413", "line": 14, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 14, "endColumn": 16}, {"ruleId": "370", "severity": 1, "message": "414", "line": 15, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 15, "endColumn": 22}, {"ruleId": "370", "severity": 1, "message": "415", "line": 37, "column": 9, "nodeType": "372", "messageId": "373", "endLine": 37, "endColumn": 24}, {"ruleId": "370", "severity": 1, "message": "416", "line": 46, "column": 9, "nodeType": "372", "messageId": "373", "endLine": 46, "endColumn": 25}, {"ruleId": "370", "severity": 1, "message": "381", "line": 48, "column": 25, "nodeType": "372", "messageId": "373", "endLine": 48, "endColumn": 29}, {"ruleId": "370", "severity": 1, "message": "381", "line": 85, "column": 11, "nodeType": "372", "messageId": "373", "endLine": 85, "endColumn": 15}, {"ruleId": "370", "severity": 1, "message": "381", "line": 101, "column": 11, "nodeType": "372", "messageId": "373", "endLine": 101, "endColumn": 15}, {"ruleId": "370", "severity": 1, "message": "383", "line": 15, "column": 19, "nodeType": "372", "messageId": "373", "endLine": 15, "endColumn": 24}, {"ruleId": "370", "severity": 1, "message": "417", "line": 4, "column": 26, "nodeType": "372", "messageId": "373", "endLine": 4, "endColumn": 33}, {"ruleId": "370", "severity": 1, "message": "386", "line": 4, "column": 47, "nodeType": "372", "messageId": "373", "endLine": 4, "endColumn": 57}, {"ruleId": "370", "severity": 1, "message": "418", "line": 118, "column": 15, "nodeType": "372", "messageId": "373", "endLine": 118, "endColumn": 21}, {"ruleId": "370", "severity": 1, "message": "419", "line": 44, "column": 25, "nodeType": "372", "messageId": "373", "endLine": 44, "endColumn": 29}, {"ruleId": "394", "severity": 1, "message": "420", "line": 79, "column": 6, "nodeType": "396", "endLine": 79, "endColumn": 12, "suggestions": "421", "suppressions": "422"}, {"ruleId": "394", "severity": 1, "message": "423", "line": 90, "column": 6, "nodeType": "396", "endLine": 90, "endColumn": 18, "suggestions": "424", "suppressions": "425"}, {"ruleId": "370", "severity": 1, "message": "426", "line": 25, "column": 10, "nodeType": "372", "messageId": "373", "endLine": 25, "endColumn": 18}, {"ruleId": "370", "severity": 1, "message": "381", "line": 61, "column": 25, "nodeType": "372", "messageId": "373", "endLine": 61, "endColumn": 29}, {"ruleId": "370", "severity": 1, "message": "381", "line": 89, "column": 11, "nodeType": "372", "messageId": "373", "endLine": 89, "endColumn": 15}, {"ruleId": "370", "severity": 1, "message": "381", "line": 105, "column": 11, "nodeType": "372", "messageId": "373", "endLine": 105, "endColumn": 15}, {"ruleId": "394", "severity": 1, "message": "404", "line": 34, "column": 6, "nodeType": "396", "endLine": 34, "endColumn": 19, "suggestions": "427", "suppressions": "428"}, {"ruleId": "370", "severity": 1, "message": "429", "line": 3, "column": 43, "nodeType": "372", "messageId": "373", "endLine": 3, "endColumn": 48}, {"ruleId": "370", "severity": 1, "message": "430", "line": 3, "column": 76, "nodeType": "372", "messageId": "373", "endLine": 3, "endColumn": 87}, {"ruleId": "394", "severity": 1, "message": "431", "line": 29, "column": 6, "nodeType": "396", "endLine": 29, "endColumn": 8, "suggestions": "432"}, {"ruleId": "370", "severity": 1, "message": "381", "line": 66, "column": 13, "nodeType": "372", "messageId": "373", "endLine": 66, "endColumn": 17}, {"ruleId": "370", "severity": 1, "message": "433", "line": 87, "column": 11, "nodeType": "372", "messageId": "373", "endLine": 87, "endColumn": 19}, "no-unused-vars", "'FaFlask' is defined but never used.", "Identifier", "unusedVar", "'formatImpactFactor' is defined but never used.", "'FaGlobe' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'html2canvas' is defined but never used.", "'pdfRef' is assigned a value but never used.", "'data' is assigned a value but never used.", "'issues' is assigned a value but never used.", "'error' is assigned a value but never used.", "'volErr' is assigned a value but never used.", "'nextVolErr' is assigned a value but never used.", "'FaDownload' is defined but never used.", "'references' is assigned a value but never used.", "'extractingRefs' is assigned a value but never used.", "'nextVolumeId' is assigned a value but never used.", "'firstIssueErr' is assigned a value but never used.", "'Link' is defined but never used.", "'renderFieldGroup' is assigned a value but never used.", "'STATUS_OPTIONS' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchSubmissions'. Either include it or remove the dependency array.", "ArrayExpression", ["434"], ["435"], "'Modal' is defined but never used.", "'Button' is defined but never used.", "'handleOpenEmailModal' is assigned a value but never used.", "'handleCloseEmailModal' is assigned a value but never used.", "'userError' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMembers'. Either include it or remove the dependency array.", ["436"], ["437"], ["438"], ["439"], "'pdfText' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'submitter' is assigned a value but never used.", "'likes' is assigned a value but never used.", "'status' is assigned a value but never used.", "'emailMessage' is assigned a value but never used.", "'handleOpenModal' is assigned a value but never used.", "'handleCloseModal' is assigned a value but never used.", "'FaTrash' is defined but never used.", "'result' is assigned a value but never used.", "'meta' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPost'. Either include it or remove the dependency array.", ["440"], ["441"], "React Hook useEffect has a missing dependency: 'startViewTracking'. Either include it or remove the dependency array.", ["442"], ["443"], "'authorId' is assigned a value but never used.", ["444"], ["445"], "'FaEye' is defined but never used.", "'FaUserTimes' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["446"], "'response' is assigned a value but never used.", {"desc": "447", "fix": "448"}, {"kind": "449", "justification": "450"}, {"desc": "451", "fix": "452"}, {"kind": "449", "justification": "450"}, {"desc": "447", "fix": "453"}, {"kind": "449", "justification": "450"}, {"desc": "454", "fix": "455"}, {"kind": "449", "justification": "450"}, {"desc": "456", "fix": "457"}, {"kind": "449", "justification": "450"}, {"desc": "451", "fix": "458"}, {"kind": "449", "justification": "450"}, {"desc": "459", "fix": "460"}, "Update the dependencies array to be: [fetchSubmissions, user]", {"range": "461", "text": "462"}, "directive", "", "Update the dependencies array to be: [fetchMembers, journalType]", {"range": "463", "text": "464"}, {"range": "465", "text": "462"}, "Update the dependencies array to be: [fetchPost, slug]", {"range": "466", "text": "467"}, "Update the dependencies array to be: [post, startViewTracking, user]", {"range": "468", "text": "469"}, {"range": "470", "text": "464"}, "Update the dependencies array to be: [fetchUsers]", {"range": "471", "text": "472"}, [703, 709], "[fetchSubmissions, user]", [1207, 1220], "[fetchMembers, journalType]", [554, 560], [3400, 3406], "[fetchPost, slug]", [3719, 3731], "[post, startViewTracking, user]", [947, 960], [1074, 1076], "[fetchUsers]"]