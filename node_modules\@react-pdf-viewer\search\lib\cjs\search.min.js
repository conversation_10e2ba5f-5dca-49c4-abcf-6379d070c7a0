"use strict";var e=require("@react-pdf-viewer/core");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var r=t(require("react")),n=function(){return r.createElement(e.Icon,{size:16},r.createElement("path",{d:"M0.541,5.627L11.666,18.2c0.183,0.207,0.499,0.226,0.706,0.043c0.015-0.014,0.03-0.028,0.043-0.043\n            L23.541,5.627"}))},o=function(){return r.createElement(e.Icon,{size:16},r.createElement("path",{d:"M23.535,18.373L12.409,5.8c-0.183-0.207-0.499-0.226-0.706-0.043C11.688,5.77,11.674,5.785,11.66,5.8\n            L0.535,18.373"}))},a=function(){return r.createElement(e.Icon,{ignoreDirection:!0,size:16},r.createElement("path",{d:"M10.5,0.5c5.523,0,10,4.477,10,10s-4.477,10-10,10s-10-4.477-10-10S4.977,0.5,10.5,0.5z\n            M23.5,23.5\n            l-5.929-5.929"}))},c=function(){return c=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},c.apply(this,arguments)},u={keyword:"",regExp:new RegExp(" "),wholeWords:!1},i=function(e){return{left:"".concat(e.left,"%"),top:"".concat(e.top,"%"),height:"".concat(e.height,"%"),width:"".concat(e.width,"%")}},s=function(t){var n=t.index,o=t.area,a=t.onHighlightKeyword,c=r.useRef();return e.useIsomorphicLayoutEffect((function(){var e=c.current;a&&e&&a({highlightEle:e,keyword:o.keyword})}),[]),r.createElement("div",{className:"rpv-search__highlight","data-index":n,ref:c,style:i(o),title:o.keywordStr.trim()})},l=function(e){var t=e.parentNode;t&&t.removeChild(e)},h=function(e){var t=e.parentNode;if(t){var r=document.createRange();r.selectNodeContents(e),function(e,t){l(e);var r=t.parentNode;r&&r.insertBefore(e,t),l(t)}(r.extractContents(),e),t.normalize()}},d=function(e,t){return e.top<t.top?-1:e.top>t.top?1:e.left<t.left?-1:e.left>t.left?1:0},f=function(t){var n=t.numPages,o=t.pageIndex,a=t.renderHighlights,c=t.store,l=t.onHighlightKeyword,f=r.useRef(),p=r.useCallback((function(e){return r.createElement(r.Fragment,null,e.highlightAreas.map((function(e,t){return r.createElement(s,{index:t,key:t,area:e,onHighlightKeyword:l})})))}),[]),g=a||p,m=r.useState(c.get("matchPosition")),v=m[0],x=m[1],y=r.useState(c.get("keyword")||[u]),E=y[0],w=y[1],b=r.useState({pageIndex:o,scale:1,status:e.LayerRenderStatus.PreRender}),k=b[0],S=b[1],C=r.useRef(null),P=r.useRef([]),_=r.useState([]),I=_[0],T=_[1],M=function(){return!0},R=r.useCallback((function(){return c.get("targetPageFilter")||M}),[c.get("targetPageFilter")]),L=function(e){var t=P.current;if(0===t.length)return[];var r=[],a=[].slice.call(e.querySelectorAll(".rpv-core__text-layer-text")),c=t.map((function(e){return e.char})).join("");return E.forEach((function(u){var i=u.keyword;if(i.trim()){for(var s,l=-1===u.regExp.flags.indexOf("g")?new RegExp(u.regExp,"".concat(u.regExp.flags,"g")):u.regExp,d=[];null!==(s=l.exec(c));)d.push({keyword:l,startIndex:s.index,endIndex:l.lastIndex});d.map((function(e){return{keyword:e.keyword,indexes:t.slice(e.startIndex,e.endIndex)}})).forEach((function(t){var c=t.indexes.reduce((function(e,t){return e[t.spanIndex]=(e[t.spanIndex]||[]).concat([t]),e}),{});Object.values(c).forEach((function(c){if(1!==c.length||""!==c[0].char.trim()){var s=u.wholeWords?c.slice(1,-1):c,l=function(e,t,r,a,c){var u=document.createRange(),i=a.firstChild;if(!i||i.nodeType!==Node.TEXT_NODE)return null;var s=i.textContent.length,l=c[0].charIndexInSpan,d=1===c.length?l:c[c.length-1].charIndexInSpan;if(l>s||d+1>s)return null;u.setStart(i,l),u.setEnd(i,d+1);var f=document.createElement("span");u.surroundContents(f);var p=f.getBoundingClientRect(),g=r.getBoundingClientRect(),m=g.height,v=g.width,x=100*(p.left-g.left)/v,y=100*(p.top-g.top)/m,E=100*p.height/m,w=100*p.width/v;return h(f),{keyword:t,keywordStr:e,numPages:n,pageIndex:o,left:x,top:y,height:E,width:w,pageHeight:m,pageWidth:v}}(i,t.keyword,e,a[s[0].spanIndex],s);l&&r.push(l)}}))}))}})),r.sort(d)},j=function(e){e&&e.length>0&&w(e)},N=function(e){return x(e)},K=function(e){if(e.has(o)){var t=e.get(o);t&&S({ele:t.ele,pageIndex:o,scale:t.scale,status:t.status})}},O=function(){return 0===E.length||1===E.length&&""===E[0].keyword.trim()};return r.useEffect((function(){if(!O()&&k.status===e.LayerRenderStatus.DidRender&&!P.current.length){var t=k.ele,r=[].slice.call(t.querySelectorAll(".rpv-core__text-layer-text")).map((function(e){return e.textContent})).reduce((function(e,t,r){return e.concat(t.split("").map((function(e,t){return{char:e,charIndexInSpan:t,spanIndex:r}})))}),[{char:"",charIndexInSpan:0,spanIndex:0}]).slice(1);P.current=r}}),[E,k.status]),r.useEffect((function(){if(!O()&&k.ele&&k.status===e.LayerRenderStatus.DidRender&&R()({pageIndex:o,numPages:n})){var t=k.ele,r=L(t);T(r)}}),[E,v,k.status,P.current]),r.useEffect((function(){O()&&k.ele&&k.status===e.LayerRenderStatus.DidRender&&T([])}),[E,k.status]),r.useEffect((function(){if(0!==I.length){var t=f.current;if(v.pageIndex===o&&t&&k.status===e.LayerRenderStatus.DidRender){var r=t.querySelector('.rpv-search__highlight[data-index="'.concat(v.matchIndex,'"]'));if(r){var n=function(e,t){for(var r=e.offsetTop,n=e.offsetLeft,o=e.parentElement;o&&o!==t;)r+=o.offsetTop,n+=o.offsetLeft,o=o.parentElement;return{left:n,top:r}}(r,t),a=n.left,u=n.top,i=c.get("jumpToDestination");i&&(i({pageIndex:o,bottomOffset:(t.getBoundingClientRect().height-u)/k.scale,leftOffset:a/k.scale,scaleTo:k.scale}),C.current&&C.current.classList.remove("rpv-search__highlight--current"),C.current=r,r.classList.add("rpv-search__highlight--current"))}}}}),[I,v]),r.useEffect((function(){return c.subscribe("keyword",j),c.subscribe("matchPosition",N),c.subscribe("renderStatus",K),function(){c.unsubscribe("keyword",j),c.unsubscribe("matchPosition",N),c.unsubscribe("renderStatus",K)}}),[]),r.createElement("div",{className:"rpv-search__highlights","data-testid":"search__highlights-".concat(o),ref:f},g({getCssProperties:i,highlightAreas:I}))},p=function(e){var t,r=e.wholeWords?" ".concat(e.keyword," "):e.keyword,n=e.matchCase?"g":"gi";return{keyword:e.keyword,regExp:new RegExp((t=r,t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")),n),wholeWords:e.wholeWords||!1}},g=function(e,t,r){return e instanceof RegExp?{keyword:e.source,regExp:e,wholeWords:r||!1}:"string"==typeof e?""===e?u:p({keyword:e,matchCase:t||!1,wholeWords:r||!1}):(void 0!==t&&(e.matchCase=t),void 0!==r&&(e.wholeWords=r),p(e))},m=function(t){var n,o=t.get("initialKeyword"),a=r.useMemo((function(){if(o&&1===o.length){var e=g(o[0]);return{matchCase:-1===e.regExp.flags.indexOf("i"),wholeWords:e.wholeWords}}return{matchCase:!1,wholeWords:!1}}),[]),c=function(e){var t=r.useRef(e.get("doc")),n=function(e){t.current=e};return r.useEffect((function(){return e.subscribe("doc",n),function(){e.unsubscribe("doc",n)}}),[]),t}(t),i=r.useState(o),s=i[0],l=i[1],h=r.useState([]),d=h[0],f=h[1],p=r.useState(0),m=p[0],v=p[1],x=r.useState(a.matchCase),y=x[0],E=x[1],w=r.useRef([]),b=r.useState(a.wholeWords),k=b[0],S=b[1],C=function(){return!0},P=r.useCallback((function(){return t.get("targetPageFilter")||C}),[t.get("targetPageFilter")]),_=function(e){var t=d.length;if(0===s.length||0===t)return null;var r=e===t+1?1:Math.max(1,Math.min(t,e));return v(r),T(d[r-1])},I=function(e){return l(""===e?[]:[e])},T=function(e){var r=t.get("jumpToPage");return r&&r(e.pageIndex),t.update("matchPosition",{matchIndex:e.matchIndex,pageIndex:e.pageIndex}),e},M=function(r,n,o){var a=c.current;if(!a)return Promise.resolve([]);var u=a.numPages,i=r.map((function(e){return g(e,n,o)}));return t.update("keyword",i),v(0),f([]),new Promise((function(t,r){var n=0===w.current.length?function(){var t=c.current;if(!t)return Promise.resolve([]);var r=Array(t.numPages).fill(0).map((function(r,n){return e.getPage(t,n).then((function(e){return e.getTextContent()})).then((function(e){var t=e.items.map((function(e){return e.str||""})).join("");return Promise.resolve({pageContent:t,pageIndex:n})}))}));return Promise.all(r).then((function(e){return e.sort((function(e,t){return e.pageIndex-t.pageIndex})),Promise.resolve(e.map((function(e){return e.pageContent})))}))}().then((function(e){return w.current=e,Promise.resolve(e)})):Promise.resolve(w.current);n.then((function(e){var r=[];e.forEach((function(e,t){P()({pageIndex:t,numPages:u})&&i.forEach((function(n){for(var o,a=0;null!==(o=n.regExp.exec(e));)r.push({keyword:n.regExp,matchIndex:a,pageIndex:t,pageText:e,startIndex:o.index,endIndex:n.regExp.lastIndex}),a++}))})),f(r),r.length>0&&(v(1),T(r[0])),t(r)}))}))};return r.useEffect((function(){w.current=[]}),[c.current]),{clearKeyword:function(){t.update("keyword",[u]),I(""),v(0),f([]),E(!1),S(!1)},changeMatchCase:function(e){E(e),s.length>0&&M(s,e,k)},changeWholeWords:function(e){S(e),s.length>0&&M(s,y,e)},currentMatch:m,jumpToMatch:_,jumpToNextMatch:function(){return _(m+1)},jumpToPreviousMatch:function(){return _(m-1)},keywords:s,matchCase:y,numberOfMatches:d.length,wholeWords:k,search:function(){return M(s,y,k)},searchFor:M,setKeywords:l,keyword:0===s.length?"":(n=s[0],n instanceof RegExp?n.source:"string"==typeof n?n:n.keyword),setKeyword:I,setTargetPages:function(e){t.update("targetPageFilter",e)}}},v=function(e){var t=e.children,n=e.store,o=m(n),a=r.useState(!1),u=a[0],i=a[1],s=function(e){return i(!0)};return r.useEffect((function(){return n.subscribe("doc",s),function(){n.unsubscribe("doc",s)}}),[]),t(c(c({},o),{isDocumentLoaded:u}))},x=function(t){var n=t.containerRef,o=t.store,a=r.useRef(!1),c=function(){a.current=!0},u=function(){a.current=!1},i=function(t){var r=n.current;r&&(t.shiftKey||t.altKey||"f"!==t.key||(e.isMac()?t.metaKey&&!t.ctrlKey:t.ctrlKey)&&(a.current||document.activeElement&&r.contains(document.activeElement))&&(t.preventDefault(),o.update("areShortcutsPressed",!0)))};return r.useEffect((function(){var e=n.current;if(e)return document.addEventListener("keydown",i),e.addEventListener("mouseenter",c),e.addEventListener("mouseleave",u),function(){document.removeEventListener("keydown",i),e.removeEventListener("mouseenter",c),e.removeEventListener("mouseleave",u)}}),[n.current]),r.createElement(r.Fragment,null)},y={left:0,top:8},E=function(t){var a=t.store,c=t.onToggle,u=r.useContext(e.LocalizationContext).l10n,i=r.useContext(e.ThemeContext).direction,s=r.useState(!1),l=s[0],h=s[1],d=r.useState(!1),f=d[0],p=d[1],g=i===e.TextDirection.RightToLeft,v=m(a),x=v.clearKeyword,E=v.changeMatchCase,w=v.changeWholeWords,b=v.currentMatch,k=v.jumpToNextMatch,S=v.jumpToPreviousMatch,C=v.keyword,P=v.matchCase,_=v.numberOfMatches,I=v.wholeWords,T=v.search,M=v.setKeyword,R=function(e){h(!0),T().then((function(t){h(!1),p(!0),e&&e()}))};r.useEffect((function(){var e=a.get("initialKeyword");e&&1===e.length&&C&&R((function(){a.update("initialKeyword",[])}))}),[]);var L=u&&u.search?u.search.enterToSearch:"Enter to search",j=u&&u.search?u.search.previousMatch:"Previous match",N=u&&u.search?u.search.nextMatch:"Next match",K=u&&u.search?u.search.close:"Close";return r.createElement("div",{className:"rpv-search__popover"},r.createElement("div",{className:"rpv-search__popover-input-counter"},r.createElement(e.TextBox,{ariaLabel:L,autoFocus:!0,placeholder:L,type:"text",value:C,onChange:function(e){p(!1),M(e)},onKeyDown:function(e){"Enter"===e.key&&C&&(f?k():R())}}),r.createElement("div",{className:e.classNames({"rpv-search__popover-counter":!0,"rpv-search__popover-counter--ltr":!g,"rpv-search__popover-counter--rtl":g})},l&&r.createElement(e.Spinner,{testId:"search__popover-searching",size:"1rem"}),!l&&r.createElement("span",{"data-testid":"search__popover-num-matches"},b,"/",_))),r.createElement("label",{className:"rpv-search__popover-label"},r.createElement("input",{className:"rpv-search__popover-label-checkbox","data-testid":"search__popover-match-case",checked:P,type:"checkbox",onChange:function(e){p(!1),E(e.target.checked)}})," ",u&&u.search?u.search.matchCase:"Match case"),r.createElement("label",{className:"rpv-search__popover-label"},r.createElement("input",{className:"rpv-search__popover-label-checkbox",checked:I,"data-testid":"search__popover-whole-words",type:"checkbox",onChange:function(e){p(!1),w(e.target.checked)}})," ",u&&u.search?u.search.wholeWords:"Whole words"),r.createElement("div",{className:"rpv-search__popover-footer"},r.createElement("div",{className:"rpv-search__popover-footer-item"},r.createElement(e.Tooltip,{ariaControlsSuffix:"search-previous-match",position:g?e.Position.BottomRight:e.Position.BottomCenter,target:r.createElement(e.MinimalButton,{ariaLabel:j,isDisabled:b<=1,onClick:S},r.createElement(o,null)),content:function(){return j},offset:y})),r.createElement("div",{className:"rpv-search__popover-footer-item"},r.createElement(e.Tooltip,{ariaControlsSuffix:"search-next-match",position:e.Position.BottomCenter,target:r.createElement(e.MinimalButton,{ariaLabel:N,isDisabled:b>_-1,onClick:k},r.createElement(n,null)),content:function(){return N},offset:y})),r.createElement("div",{className:e.classNames({"rpv-search__popover-footer-button":!0,"rpv-search__popover-footer-button--ltr":!g,"rpv-search__popover-footer-button--rtl":g})},r.createElement(e.Button,{onClick:function(){c(),x()}},K))))},w=function(t){var n=t.children,o=t.onClick,c=r.useContext(e.LocalizationContext).l10n,u=c&&c.search?c.search.search:"Search";return n({icon:r.createElement(a,null),label:u,onClick:o})},b={left:0,top:8},k=function(t){var n=t.enableShortcuts,o=t.store,a=t.onClick,c=n?e.isMac()?"Meta+F":"Ctrl+F":"",u=function(e){e&&a()};return r.useEffect((function(){return o.subscribe("areShortcutsPressed",u),function(){o.unsubscribe("areShortcutsPressed",u)}}),[]),r.createElement(w,{onClick:a},(function(t){return r.createElement(e.Tooltip,{ariaControlsSuffix:"search-popover",position:e.Position.BottomCenter,target:r.createElement(e.MinimalButton,{ariaKeyShortcuts:c,ariaLabel:t.label,testId:"search__popover-button",onClick:a},t.icon),content:function(){return t.label},offset:b})}))},S={left:0,top:8},C=function(t){var n=t.children,o=t.enableShortcuts,a=t.store,u=r.useContext(e.ThemeContext).direction===e.TextDirection.RightToLeft?e.Position.BottomRight:e.Position.BottomLeft,i=n||function(e){return r.createElement(k,c({enableShortcuts:o,store:a},e))};return r.createElement(e.Popover,{ariaControlsSuffix:"search",lockScroll:!1,position:u,target:function(e){return i({onClick:e})},content:function(e){return r.createElement(E,{store:a,onToggle:e})},offset:S,closeOnClickOutside:!1,closeOnEscape:!0})},P=function(e){return Array.isArray(e)?e.map((function(e){return g(e)})):[g(e)]};exports.NextIcon=n,exports.PreviousIcon=o,exports.SearchIcon=a,exports.searchPlugin=function(t){var n=r.useMemo((function(){return Object.assign({},{enableShortcuts:!0,onHighlightKeyword:function(){}},t)}),[]),o=r.useMemo((function(){return e.createStore({initialKeyword:t&&t.keyword?Array.isArray(t.keyword)?t.keyword:[t.keyword]:[],keyword:t&&t.keyword?P(t.keyword):[u],matchPosition:{matchIndex:-1,pageIndex:-1},renderStatus:new Map})}),[]),a=m(o),i=a.clearKeyword,s=a.jumpToMatch,l=a.jumpToNextMatch,h=a.jumpToPreviousMatch,d=a.searchFor,p=a.setKeywords,g=a.setTargetPages,y=function(e){return r.createElement(C,c({enableShortcuts:n.enableShortcuts},e,{store:o}))};return{install:function(e){var r=t&&t.keyword?Array.isArray(t.keyword)?t.keyword:[t.keyword]:[],n=t&&t.keyword?P(t.keyword):[u];o.update("initialKeyword",r),o.update("jumpToDestination",e.jumpToDestination),o.update("jumpToPage",e.jumpToPage),o.update("keyword",n)},renderPageLayer:function(e){return r.createElement(f,{key:e.pageIndex,numPages:e.doc.numPages,pageIndex:e.pageIndex,renderHighlights:null==t?void 0:t.renderHighlights,store:o,onHighlightKeyword:n.onHighlightKeyword})},renderViewer:function(e){var t=e.slot;return t.subSlot&&(t.subSlot.children=r.createElement(r.Fragment,null,n.enableShortcuts&&r.createElement(x,{containerRef:e.containerRef,store:o}),t.subSlot.children)),t},uninstall:function(e){var t=o.get("renderStatus");t&&t.clear()},onDocumentLoad:function(e){o.update("doc",e.doc)},onTextLayerRender:function(e){var t=o.get("renderStatus");t&&(t=t.set(e.pageIndex,e),o.update("renderStatus",t))},Search:function(e){return r.createElement(v,c({},e,{store:o}))},ShowSearchPopover:y,ShowSearchPopoverButton:function(){return r.createElement(y,null,(function(e){return r.createElement(k,c({enableShortcuts:n.enableShortcuts,store:o},e))}))},clearHighlights:function(){i()},highlight:function(e){var t=Array.isArray(e)?e:[e];return p(t),d(t)},jumpToMatch:s,jumpToNextMatch:l,jumpToPreviousMatch:h,setTargetPages:g}};
