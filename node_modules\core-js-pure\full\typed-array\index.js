'use strict';
var parent = require('../../actual/typed-array');
require('../../modules/es.map');
require('../../modules/es.promise');
require('../../modules/esnext.typed-array.from-async');
// TODO: Remove from `core-js@4`
require('../../modules/esnext.typed-array.at');
// TODO: Remove from `core-js@4`
require('../../modules/esnext.typed-array.filter-out');
require('../../modules/esnext.typed-array.filter-reject');
require('../../modules/esnext.typed-array.group-by');
require('../../modules/esnext.typed-array.unique-by');

module.exports = parent;
