{"version": 3, "file": "naming-convention.js", "sourceRoot": "", "sources": ["../../src/rules/naming-convention.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oEAAkE;AAElE,oDAAoE;AAGpE,8CAAgC;AAMhC,uEAA4E;AAe5E,qDAAqD;AACrD,sHAAsH;AACtH,MAAM,kCAAkC,GAAY;IAClD;QACE,QAAQ,EAAE,SAAS;QACnB,MAAM,EAAE,CAAC,WAAW,CAAC;QACrB,iBAAiB,EAAE,OAAO;QAC1B,kBAAkB,EAAE,OAAO;KAC5B;IAED;QACE,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;QACnC,iBAAiB,EAAE,OAAO;QAC1B,kBAAkB,EAAE,OAAO;KAC5B;IAED;QACE,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,CAAC,YAAY,CAAC;KACvB;CACF,CAAC;AAEF,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,6DAA6D;YAC/D,WAAW,EAAE,KAAK;YAClB,4EAA4E;YAC5E,oBAAoB,EAAE,IAAI;SAC3B;QACD,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE;YACR,oBAAoB,EAClB,mEAAmE;YACrE,iBAAiB,EACf,0EAA0E;YAC5E,YAAY,EACV,qFAAqF;YACvF,aAAa,EACX,oEAAoE;YACtE,kBAAkB,EAChB,+EAA+E;YACjF,yBAAyB,EACvB,8GAA8G;SACjH;QACD,MAAM,EAAE,gCAAM;KACf;IACD,cAAc,EAAE,kCAAkC;IAClD,MAAM,CAAC,sBAAsB;QAC3B,MAAM,OAAO,GACX,sBAAsB,CAAC,OAAO;YAC9B,sBAAsB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;YACvC,CAAC,CAAC,sBAAsB;YACxB,CAAC,CAAC,2DAA2D;gBAC1D,MAAM,CAAC,cAAc,CACpB;oBACE,OAAO,EAAE,kCAAkC;iBAC5C,EACD,sBAAsB,CACX,CAAC;QAEpB,MAAM,UAAU,GAAG,IAAA,sCAAY,EAAC,OAAO,CAAC,CAAC;QAEzC,0FAA0F;QAC1F,MAAM,eAAe,GAAG,IAAI;aACzB,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC;aAChC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAChC,SAAS,YAAY,CACnB,SAA4B,EAC5B,IAO6C,EAC7C,SAAyB;YAEzB,IAAI,CAAC,SAAS,EAAE;gBACd,OAAO;aACR;YAED,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;YACrB,IAAI,eAAe,CAAC,GAAG,EAAE,eAAe,CAAC,MAAM,CAAC,EAAE;gBAChD,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,cAAc,CAAC,CAAC;aACzC;YAED,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAC5B,CAAC;QAED,SAAS,kBAAkB,CACzB,IAKgC;YAEhC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;YACvC,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;gBACvE,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,UAAU,CAAC,CAAC,CAAC;aACtC;iBAAM,IAAI,IAAI,CAAC,aAAa,EAAE;gBAC7B,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;aAC9C;iBAAM;gBACL,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;aACjC;YACD,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;aACjC;YACD,IAAI,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACvC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;aACnC;YACD,IAAI,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACvC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;aACnC;YACD,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,4BAA4B;gBACzD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,0BAA0B,EACvD;gBACA,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;aACnC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAC7D,SAAS,QAAQ,CACf,IAAY,EACZ,eAA4C,OAAO,CAAC,QAAQ,EAAE;;YAE9D,IAAI,QAAQ,GAAmC,IAAI,CAAC;YACpD,IAAI,KAAK,GAAgC,YAAY,CAAC;YACtD,OAAO,KAAK,EAAE;gBACZ,QAAQ,GAAG,MAAA,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,mCAAI,IAAI,CAAC;gBACvC,IAAI,QAAQ,EAAE;oBACZ,MAAM;iBACP;gBACD,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;aACrB;YACD,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO,KAAK,CAAC;aACd;YAED,OAAO,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;QAED,SAAS,cAAc,CAAC,EAAuB;;YAC7C,OAAO;YACL,gBAAgB;YAChB,kCAAkC;YAClC,CAAC,CAAA,MAAA,EAAE,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,QAAQ,IAAI,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC;gBACpE,oBAAoB;gBACpB,sCAAsC;gBACtC,CAAC,CAAA,MAAA,EAAE,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,iBAAiB;oBACnD,CAAA,MAAA,EAAE,CAAC,MAAM,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,QAAQ;oBAClD,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAC9B,CAAC;QACJ,CAAC;QAED,SAAS,uBAAuB,CAC9B,oBAMsD;YAEtD,OAAO,OAAO,CACZ,OAAO,IAAI,oBAAoB;gBAC7B,oBAAoB,CAAC,KAAK;gBAC1B,OAAO,IAAI,oBAAoB,CAAC,KAAK;gBACrC,oBAAoB,CAAC,KAAK,CAAC,KAAK,CACnC,CAAC;QACJ,CAAC;QAED,SAAS,yBAAyB,CAAC,EAAuB;YACxD,OAAO,OAAO,CACZ,EAAE,CAAC,MAAM;gBACP,CAAC,CAAC,OAAO,IAAI,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;oBACxC,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM;wBAClB,EAAE,CAAC,MAAM,CAAC,IAAI;wBACd,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,IAAI;wBACzB,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAC7B,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAQX;YACF,mBAAmB;YAEnB,kBAAkB,EAAE;gBAClB,SAAS,EAAE,UAAU,CAAC,QAAQ;gBAC9B,OAAO,EAAE,CAAC,IAAI,EAAE,SAAS,EAAQ,EAAE;oBACjC,MAAM,WAAW,GAAG,yBAAyB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAEvD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAa,CAAC;oBAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;oBAC3B,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,sBAAc,CAAC,mBAAmB,EAAE;wBACvD,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;4BAC3B,aAAa,CAAC,GAAG,CAAC,mCAAS,CAAC,KAAK,CAAC,CAAC;yBACpC;wBAED,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,EAAE;4BAChC,aAAa,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;yBACrC;qBACF;oBAED,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;wBACvB,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC;wBAEzC,IAAI,cAAc,CAAC,EAAE,CAAC,EAAE;4BACtB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,YAAY,CAAC,CAAC;yBACvC;wBAED,IAAI,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,EAAE;4BACnD,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;yBACnC;wBAED,IAAI,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;4BACrB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;yBACjC;wBAED,IAAI,yBAAyB,CAAC,EAAE,CAAC,EAAE;4BACjC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,KAAK,CAAC,CAAC;yBAChC;wBAED,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;aACF;YAED,aAAa;YAEb,mBAAmB;YAEnB,4DAA4D,EAAE;gBAC5D,SAAS,EAAE,UAAU,CAAC,QAAQ;gBAC9B,OAAO,EAAE,CACP,IAG+B,EAC/B,SAAS,EACH,EAAE;oBACR,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,EAAE;wBACnB,OAAO;qBACR;oBAED,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBACvC,0CAA0C;oBAC1C,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC;oBAEvC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;wBACnB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;qBACjC;oBAED,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBACzC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;qBACnC;oBAED,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBACjC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;qBACjC;oBAED,IAAI,IAAI,CAAC,KAAK,EAAE;wBACd,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,KAAK,CAAC,CAAC;qBAChC;oBAED,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;gBAChC,CAAC;aACF;YAED,sBAAsB;YAEtB,oBAAoB;YACpB,oHAAoH,EAClH;gBACE,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,OAAO,EAAE,CACP,IAKoC,EACpC,SAAS,EACH,EAAE;oBACR,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;wBAC1B,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE;4BACrD,OAAO;yBACR;wBAED,MAAM,WAAW,GAAG,yBAAyB,CAAC,KAAK,CAAC,CAAC;wBAErD,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;4BACtB,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;4BAEvC,IAAI,cAAc,CAAC,CAAC,CAAC,EAAE;gCACrB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,YAAY,CAAC,CAAC;6BACvC;4BAED,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gCACpB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;6BACjC;4BAED,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;wBAC1B,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;gBACL,CAAC;aACF;YAEH,uBAAuB;YAEvB,4BAA4B;YAE5B,mBAAmB,EAAE;gBACnB,SAAS,EAAE,UAAU,CAAC,iBAAiB;gBACvC,OAAO,EAAE,CAAC,IAAI,EAAE,SAAS,EAAQ,EAAE;oBACjC,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBAE3C,MAAM,WAAW,GAAG,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAE9D,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;wBACtB,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;oBAC1B,CAAC,CAAC,CAAC;gBACL,CAAC;aACF;YAED,+BAA+B;YAE/B,mBAAmB;YAEnB,6LAA6L,EAC3L;gBACE,SAAS,EAAE,UAAU,CAAC,qBAAqB;gBAC3C,OAAO,EAAE,CACP,IAAsC,EACtC,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,IAAI,GAAG,CAAY,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC,CAAC;oBACzD,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAEH,0MAA0M,EACxM;gBACE,SAAS,EAAE,UAAU,CAAC,aAAa;gBACnC,OAAO,EAAE,CACP,IAEwD,EACxD,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBAC3C,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAEH,+FAA+F,EAC7F;gBACE,SAAS,EAAE,UAAU,CAAC,YAAY;gBAClC,OAAO,EAAE,CACP,IAAiD,EACjD,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,IAAI,GAAG,CAAY,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC,CAAC;oBACzD,IAAI,IAAI,CAAC,QAAQ,EAAE;wBACjB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;qBACnC;oBAED,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAEH,sBAAsB;YAEtB,iBAAiB;YAEjB,CAAC;gBACC,mFAAmF;gBACnF,8EAA8E;gBAC9E,yFAAyF;aAC1F,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;gBACb,SAAS,EAAE,UAAU,CAAC,mBAAmB;gBACzC,OAAO,EAAE,CACP,IAE6C,EAC7C,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,IAAI,GAAG,CAAY,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC,CAAC;oBAEzD,IAAI,uBAAuB,CAAC,IAAI,CAAC,EAAE;wBACjC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,KAAK,CAAC,CAAC;qBAChC;oBAED,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAED,CAAC;gBACC,sHAAsH;gBACtH,iHAAiH;gBACjH,4HAA4H;gBAC5H,2FAA2F;aAC5F,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;gBACb,SAAS,EAAE,UAAU,CAAC,WAAW;gBACjC,OAAO,EAAE,CACP,IAIsD,EACtD,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBAE3C,IAAI,uBAAuB,CAAC,IAAI,CAAC,EAAE;wBACjC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,KAAK,CAAC,CAAC;qBAChC;oBAED,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAED,CAAC;gBACC,qCAAqC;gBACrC,8FAA8F;aAC/F,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;gBACb,SAAS,EAAE,UAAU,CAAC,UAAU;gBAChC,OAAO,EAAE,CACP,IAE+C,EAC/C,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,IAAI,GAAG,CAAY,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC,CAAC;oBACzD,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAED,oBAAoB;YAEpB,mBAAmB;YAEnB,oEAAoE,EAAE;gBACpE,SAAS,EAAE,UAAU,CAAC,QAAQ;gBAC9B,OAAO,EAAE,CAAC,IAAsC,EAAE,SAAS,EAAQ,EAAE;oBACnE,MAAM,SAAS,GAAG,IAAI,GAAG,CAAY,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC,CAAC;oBACzD,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAED,4EAA4E,EAC1E;gBACE,SAAS,EAAE,UAAU,CAAC,QAAQ;gBAC9B,OAAO,EAAE,CACP,IAA8C,EAC9C,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBAC3C,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAEH,sBAAsB;YAEtB,qBAAqB;YAErB,uDAAuD;YACvD,gCAAgC,EAAE;gBAChC,SAAS,EAAE,UAAU,CAAC,UAAU;gBAChC,OAAO,EAAE,CACP,IAA0C,EAC1C,SAAS,EACH,EAAE;oBACR,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;oBACnB,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBAEvC,IAAI,eAAe,CAAC,EAAE,EAAE,eAAe,CAAC,MAAM,CAAC,EAAE;wBAC/C,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,cAAc,CAAC,CAAC;qBACzC;oBAED,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;gBAC3B,CAAC;aACF;YAED,wBAAwB;YAExB,gBAAgB;YAEhB,mCAAmC,EAAE;gBACnC,SAAS,EAAE,UAAU,CAAC,KAAK;gBAC3B,OAAO,EAAE,CACP,IAA0D,EAC1D,SAAS,EACH,EAAE;oBACR,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;oBACnB,IAAI,EAAE,IAAI,IAAI,EAAE;wBACd,OAAO;qBACR;oBAED,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBACvC,wCAAwC;oBACxC,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC;oBAEvC,IAAI,IAAI,CAAC,QAAQ,EAAE;wBACjB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;qBACnC;oBAED,IAAI,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBACpC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;qBACnC;oBAED,IAAI,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBAC5B,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;qBACjC;oBAED,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;gBAC3B,CAAC;aACF;YAED,mBAAmB;YAEnB,oBAAoB;YAEpB,sBAAsB,EAAE;gBACtB,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,OAAO,EAAE,CAAC,IAAI,EAAE,SAAS,EAAQ,EAAE;oBACjC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBACvC,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;oBAEjC,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBACzC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;qBACnC;oBAED,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBACjC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;qBACjC;oBAED,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;gBAChC,CAAC;aACF;YAED,uBAAuB;YAEvB,oBAAoB;YAEpB,sBAAsB,EAAE;gBACtB,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,OAAO,EAAE,CAAC,IAAI,EAAE,SAAS,EAAQ,EAAE;oBACjC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBACvC,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;oBAEjC,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBACzC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;qBACnC;oBAED,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBACjC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;qBACjC;oBAED,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;gBAChC,CAAC;aACF;YAED,uBAAuB;YAEvB,eAAe;YAEf,iBAAiB,EAAE;gBACjB,SAAS,EAAE,UAAU,CAAC,IAAI;gBAC1B,OAAO,EAAE,CAAC,IAAI,EAAE,SAAS,EAAQ,EAAE;oBACjC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBACvC,sCAAsC;oBACtC,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC;oBAEvC,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBACzC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;qBACnC;oBAED,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBACjC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;qBACjC;oBAED,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;gBAChC,CAAC;aACF;YAED,kBAAkB;YAElB,wBAAwB;YAExB,8CAA8C,EAAE;gBAC9C,SAAS,EAAE,UAAU,CAAC,aAAa;gBACnC,OAAO,EAAE,CAAC,IAA8B,EAAE,SAAS,EAAQ,EAAE;oBAC3D,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBACvC,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;oBAEjC,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBACnC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;qBACjC;oBAED,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBAClC,CAAC;aACF;YAED,2BAA2B;SAC5B,CAAC;QAEF,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;aACtB,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE;YAC1C,OAAO;gBACL,QAAQ;gBACR,CAAC,IAAmC,EAAQ,EAAE;oBAC5C,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3B,CAAC;aACO,CAAC;QACb,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,CAAC,EAA8B,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CACxD,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,yBAAyB,CAChC,OAAsC;IAEtC,MAAM,WAAW,GAA0B,EAAE,CAAC;IAC9C,MAAM,OAAO,GAAG,IAAI,8BAAc,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5E,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACvB,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,UAAU,CACjB,IAA+B,EAC/B,IAAY,EACZ,KAAkC;;IAElC,IACE,CAAA,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,wBAAwB;QAC9D,CAAA,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,sBAAsB,EAC5D;QACA,OAAO,IAAI,CAAC;KACb;IAED,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,OAAO,KAAK,CAAC;KACd;IAED,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACrC,IAAI,QAAQ,EAAE;QACZ,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,UAAU,EAAE;YACrC,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC;YACxC,IACE,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,IAAI,MAAK,sBAAc,CAAC,wBAAwB;gBAC3D,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,IAAI,MAAK,sBAAc,CAAC,eAAe,EAClD;gBACA,OAAO,IAAI,CAAC;aACb;SACF;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,QAAQ,CAAC,KAAkC;IAClD,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CACL,KAAK,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM;QAC9C,KAAK,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAC/C,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CACtB,IAAyE,EACzE,MAAgC;IAEhC,MAAM,IAAI,GACR,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;QACvC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;QAC5C,CAAC,CAAC,IAAI,CAAC,IAAI;QACX,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IACtB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC5C,CAAC"}