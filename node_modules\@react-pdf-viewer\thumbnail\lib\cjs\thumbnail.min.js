"use strict";var e=require("@react-pdf-viewer/core");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var a=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,a.get?a:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var n,a=t(require("react"));exports.ThumbnailDirection=void 0,(n=exports.ThumbnailDirection||(exports.ThumbnailDirection={})).Horizontal="Horizontal",n.Vertical="Vertical";var i=function(){return i=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},i.apply(this,arguments)},r=function(t){var n=t.doc,i=t.getPageIndex,r=t.renderSpinner,o=t.store,u=t.width,c=n.numPages,l=i?i({numPages:c}):0,s=Math.max(0,Math.min(l,c-1)),d=o.get("pagesRotation")||new Map,g=d.has(s)?d.get(s):0,h=a.useState(""),m=h[0],b=h[1],p=e.useIsMounted(),f=a.useRef(),v=a.useState(o.get("rotation")||0),_=v[0],P=v[1],w=a.useState(g),E=w[0],x=w[1],R=a.useState(!1),C=R[0],S=R[1],M=function(e){var t=e.has(s)?e.get(s):0;x(t)},I=function(e){P(e)},V=e.useIntersectionObserver({onVisibilityChanged:function(e){S(e.isVisible)}});return a.useEffect((function(){if(C){var t=V.current;t&&(b(""),e.getPage(n,s).then((function(e){var n=e.getViewport({scale:1}),a=(n.rotation+_+E)%360,i=Math.abs(_+E)%180==0,r=i?n.width:n.height,o=i?n.height:n.width,c=document.createElement("canvas"),l=c.getContext("2d",{alpha:!1}),s=t.clientWidth,d=t.clientHeight,g=u?u/r:Math.min(s/r,d/o),h=g*r,m=g*o;c.height=m,c.width=h,c.style.opacity="0";var v=e.getViewport({rotation:a,scale:g});f.current=e.render({canvasContext:l,viewport:v}),f.current.promise.then((function(){p.current&&b(c.toDataURL()),c.width=0,c.height=0}),(function(){}))})))}}),[E,C]),a.useEffect((function(){return o.subscribe("pagesRotation",M),o.subscribe("rotation",I),function(){o.unsubscribe("pagesRotation",M),o.unsubscribe("rotation",I)}}),[]),a.useEffect((function(){return function(){var e;null===(e=f.current)||void 0===e||e.cancel()}}),[]),a.createElement("div",{ref:V,className:"rpv-thumbnail__cover-inner","data-testid":"thumbnail__cover-inner"},m?a.createElement("img",{className:"rpv-thumbnail__cover-image","data-testid":"thumbnail__cover-image",src:m}):a.createElement("div",{className:"rpv-thumbnail__cover-loader","data-testid":"thumbnail__cover-loader"},r?r():a.createElement(e.Spinner,null)))},o=function(t){var n=t.getPageIndex,i=t.renderSpinner,o=t.store,u=t.width,c=a.useState(o.get("doc")),l=c[0],s=c[1],d=function(e){s(e)};return a.useEffect((function(){return o.subscribe("doc",d),function(){o.unsubscribe("doc",d)}}),[]),a.createElement("div",{className:"rpv-thumbnail__cover"},l?a.createElement(r,{doc:l,getPageIndex:n,renderSpinner:i,store:o,width:u}):a.createElement("div",{className:"rpv-thumbnail__cover-loader"},i?i():a.createElement(e.Spinner,null)))},u=function(){return a.createElement(e.Spinner,null)},c=a.createContext({renderSpinner:u}),l=function(t){var n=t.children,i=t.doc,r=e.useIsMounted(),o=a.useState({loading:!0,labels:[]}),u=o[0],c=o[1];return a.useEffect((function(){i.getPageLabels().then((function(e){r.current&&c({loading:!1,labels:e||[]})}))}),[i.loadingTask.docId]),u.loading?a.createElement(a.Fragment,null):n(u.labels)},s=function(t){var n=t.page,i=t.pageHeight,r=t.pageIndex,o=t.pageWidth,u=t.rotation,l=t.thumbnailHeight,s=t.thumbnailWidth,d=t.onRenderCompleted,g=a.useContext(e.LocalizationContext).l10n,h=a.useRef(),m=a.useState(""),b=m[0],p=m[1],f=g&&g.thumbnail?g.thumbnail.thumbnailLabel:"Thumbnail of page {{pageIndex}}";return a.useEffect((function(){var e=h.current;e&&e.cancel();var t=document.createElement("canvas"),a=t.getContext("2d",{alpha:!1}),c=s,l=c/(o/i),g=c/o;t.height=l,t.width=c,t.style.height="".concat(l,"px"),t.style.width="".concat(c,"px");var m=n.getViewport({rotation:u,scale:g});return h.current=n.render({canvasContext:a,viewport:m}),h.current.promise.then((function(){p(t.toDataURL()),d(r)}),(function(){d(r)})),function(){var e;null===(e=h.current)||void 0===e||e.cancel()}}),[u]),b?a.createElement("img",{"aria-label":f.replace("{{pageIndex}}","".concat(r+1)),src:b,height:"".concat(l,"px"),width:"".concat(s,"px")}):a.useContext(c).renderSpinner()},d=function(t){var n=t.doc,i=t.pageHeight,r=t.pageIndex,o=t.pageRotation,u=t.pageWidth,l=t.rotation,d=t.shouldRender,g=t.thumbnailWidth,h=t.onRenderCompleted,m=t.onVisibilityChanged,b=e.useIsMounted(),p=a.useState({height:i,page:null,viewportRotation:0,width:u}),f=p[0],v=p[1],_=f.page,P=f.height,w=f.width,E=w/P,x=Math.abs(l+o)%180==0,R=x?g:g/E,C=x?g/E:g;a.useEffect((function(){d&&e.getPage(n,r).then((function(e){var t=e.getViewport({scale:1});b.current&&v({height:t.height,page:e,viewportRotation:t.rotation,width:t.width})}))}),[d]);var S=(f.viewportRotation+l+o)%360,M=e.useIntersectionObserver({onVisibilityChanged:function(e){m(r,e)}});return a.createElement("div",{className:"rpv-thumbnail__container","data-testid":"thumbnail__container-".concat(r),ref:M,style:{height:"".concat(C,"px"),width:"".concat(R,"px")}},_?a.createElement(s,{page:_,pageHeight:x?P:w,pageIndex:r,pageWidth:x?w:P,rotation:S,thumbnailHeight:C,thumbnailWidth:R,onRenderCompleted:h}):a.useContext(c).renderSpinner())},g=function(t){var n=t.currentPage,i=t.doc,r=t.labels,o=t.pagesRotation,u=t.pageHeight,c=t.pageWidth,l=t.renderCurrentPageLabel,s=t.renderThumbnailItem,g=t.rotatedPage,h=t.rotation,m=t.thumbnailDirection,b=t.thumbnailWidth,p=t.viewMode,f=t.onJumpToPage,v=t.onRotatePage,_=i.numPages,P=i.loadingTask.docId,w=a.useRef(null),E=a.useRef([]),x=a.useState(n),R=x[0],C=x[1],S=a.useContext(e.ThemeContext).direction===e.TextDirection.RightToLeft,M=a.useState(-1),I=M[0],V=M[1],y=e.useIsMounted(),D=e.usePrevious(p),T=a.useRef(!1),W=e.useRenderQueue({doc:i}),k=a.useMemo((function(){return Array(_).fill(0).map((function(e,t){return t}))}),[P]),L=a.useMemo((function(){switch(p){case e.ViewMode.DualPage:return e.chunk(k,2);case e.ViewMode.DualPageWithCover:return[[k[0]]].concat(e.chunk(k.slice(1),2));case e.ViewMode.SinglePage:default:return e.chunk(k,1)}}),[P,p]),H=function(){if(w.current){var e=E.current,t=R+1;t<e.length&&(R>=0&&e[R].setAttribute("tabindex","-1"),C(t))}},N=function(){if(w.current){var e=E.current,t=R-1;t>=0&&(R>=0&&e[R].setAttribute("tabindex","-1"),C(t))}},O=function(){R>=0&&R<_&&f(R)};e.useIsomorphicLayoutEffect((function(){var e=w.current;e&&(E.current=Array.from(e.querySelectorAll(".rpv-thumbnail__item")))}),[p]),a.useEffect((function(){var e=E.current;if(!(0===e.length||R<0||R>e.length)){var t=e[R];t.setAttribute("tabindex","0"),t.focus()}}),[R]),e.useIsomorphicLayoutEffect((function(){var e=w.current,t=E.current;if(!(!e||0===t.length||n<0||n>t.length)){var a=t[n].closest(".rpv-thumbnail__items");a&&(m===exports.ThumbnailDirection.Vertical?function(e,t){var n=e.getBoundingClientRect().top-t.getBoundingClientRect().top,a=e.clientHeight,i=t.clientHeight;n<0?t.scrollTop+=n:n+a<=i||(t.scrollTop+=n+a-i)}(a,e):function(e,t){var n=e.getBoundingClientRect().left-t.getBoundingClientRect().left,a=e.clientWidth,i=t.clientWidth;n<0?t.scrollLeft+=n:n+a<=i||(t.scrollLeft+=n+a-i)}(a,e))}}),[n,m]);var j=a.useCallback((function(e){y.current&&(W.markRendered(e),T.current=!1,z())}),[P]),A=a.useCallback((function(e,t){t.isVisible?W.setVisibility(e,t.ratio):W.setOutOfRange(e),z()}),[P]),z=a.useCallback((function(){if(!T.current){var e=W.getHighestPriorityPage();e>-1&&(W.markRendering(e),T.current=!0,V(e))}}),[P]);a.useEffect((function(){g>=0&&(W.markRendering(g),T.current=!0,V(g))}),[P,g]),e.useIsomorphicLayoutEffect((function(){D!==p&&(W.markNotRendered(),z())}),[p]);return a.createElement("div",{ref:w,"data-testid":"thumbnail__list",className:e.classNames({"rpv-thumbnail__list":!0,"rpv-thumbnail__list--horizontal":m===exports.ThumbnailDirection.Horizontal,"rpv-thumbnail__list--rtl":S,"rpv-thumbnail__list--vertical":m===exports.ThumbnailDirection.Vertical}),onKeyDown:function(e){switch(e.key){case"ArrowDown":H();break;case"ArrowUp":N();break;case"Enter":O()}}},L.map((function(t,g){var m=!1;switch(p){case e.ViewMode.DualPage:m=n===2*g||n===2*g+1;break;case e.ViewMode.DualPageWithCover:m=0===n&&0===g||g>0&&n===2*g-1||g>0&&n===2*g;break;case e.ViewMode.SinglePage:default:m=n===g}return a.createElement("div",{className:e.classNames({"rpv-thumbnail__items":!0,"rpv-thumbnail__items--dual":p===e.ViewMode.DualPage,"rpv-thumbnail__items--dual-cover":p===e.ViewMode.DualPageWithCover,"rpv-thumbnail__items--single":p===e.ViewMode.SinglePage,"rpv-thumbnail__items--selected":m}),key:"".concat(g,"___").concat(p)},t.map((function(t){return function(t){var g=p===e.ViewMode.DualPageWithCover&&(0===t||_%2==0&&t===_-1),m="".concat(i.loadingTask.docId,"___").concat(t),P=r.length===_?r[t]:"".concat(t+1),w=l?l({currentPage:n,pageIndex:t,numPages:_,pageLabel:P}):P,E=o.has(t)?o.get(t):0,x=a.createElement(d,{doc:i,pageHeight:u,pageIndex:t,pageRotation:E,pageWidth:c,rotation:h,shouldRender:I===t,thumbnailWidth:b,onRenderCompleted:j,onVisibilityChanged:A});return s?s({currentPage:n,key:m,numPages:_,pageIndex:t,renderPageLabel:a.createElement(a.Fragment,null,w),renderPageThumbnail:x,onJumpToPage:function(){return f(t)},onRotatePage:function(e){return v(t,e)}}):a.createElement("div",{key:m},a.createElement("div",{className:e.classNames({"rpv-thumbnail__item":!0,"rpv-thumbnail__item--dual-even":p===e.ViewMode.DualPage&&t%2==0,"rpv-thumbnail__item--dual-odd":p===e.ViewMode.DualPage&&t%2==1,"rpv-thumbnail__item--dual-cover":g,"rpv-thumbnail__item--dual-cover-even":p===e.ViewMode.DualPageWithCover&&!g&&t%2==0,"rpv-thumbnail__item--dual-cover-odd":p===e.ViewMode.DualPageWithCover&&!g&&t%2==1,"rpv-thumbnail__item--single":p===e.ViewMode.SinglePage,"rpv-thumbnail__item--selected":n===t}),role:"button",tabIndex:n===t?0:-1,onClick:function(){return f(t)}},x),a.createElement("div",{"data-testid":"thumbnail__label-".concat(t),className:"rpv-thumbnail__label"},w))}(t)})))})))},h=function(t){var n=t.renderCurrentPageLabel,i=t.renderThumbnailItem,r=t.store,o=t.thumbnailDirection,u=t.thumbnailWidth,s=a.useState(r.get("doc")),d=s[0],h=s[1],m=a.useState(r.get("currentPage")||0),b=m[0],p=m[1],f=a.useState(r.get("pageHeight")||0),v=f[0],_=f[1],P=a.useState(r.get("pageWidth")||0),w=P[0],E=P[1],x=a.useState(r.get("rotation")||0),R=x[0],C=x[1],S=a.useState(r.get("pagesRotation")||new Map),M=S[0],I=S[1],V=a.useState(r.get("rotatedPage")||-1),y=V[0],D=V[1],T=a.useState(r.get("viewMode")),W=T[0],k=T[1],L=function(e){p(e)},H=function(e){h(e)},N=function(e){_(e)},O=function(e){E(e)},j=function(e){C(e)},A=function(e){I(e)},z=function(e){D(e)},B=function(e){k(e)},q=function(e){var t=r.get("jumpToPage");t&&t(e)},J=function(e,t){r.get("rotatePage")(e,t)};return a.useEffect((function(){return r.subscribe("doc",H),r.subscribe("pageHeight",N),r.subscribe("pageWidth",O),r.subscribe("rotatedPage",z),r.subscribe("rotation",j),r.subscribe("pagesRotation",A),r.subscribe("viewMode",B),function(){r.unsubscribe("doc",H),r.unsubscribe("pageHeight",N),r.unsubscribe("pageWidth",O),r.unsubscribe("rotatedPage",z),r.unsubscribe("rotation",j),r.unsubscribe("pagesRotation",A),r.unsubscribe("viewMode",B)}}),[]),e.useIsomorphicLayoutEffect((function(){return r.subscribe("currentPage",L),function(){r.unsubscribe("currentPage",L)}}),[]),d?a.createElement(e.LazyRender,{testId:"thumbnail__list-container",attrs:{className:"rpv-thumbnail__list-container"}},a.createElement(l,{doc:d},(function(e){return a.createElement(g,{currentPage:b,doc:d,labels:e,pagesRotation:M,pageHeight:v,pageWidth:w,renderCurrentPageLabel:n,renderThumbnailItem:i,rotatedPage:y,rotation:R,thumbnailDirection:o,thumbnailWidth:u,viewMode:W,onJumpToPage:q,onRotatePage:J})}))):a.createElement("div",{"data-testid":"thumbnail-list__loader",className:"rpv-thumbnail__loader"},a.useContext(c).renderSpinner())};exports.thumbnailPlugin=function(t){var n=a.useMemo((function(){return e.createStore({rotatePage:function(){},viewMode:e.ViewMode.SinglePage})}),[]),r=a.useState(""),l=r[0],s=r[1];return{install:function(e){n.update("jumpToPage",e.jumpToPage),n.update("rotatePage",e.rotatePage)},onDocumentLoad:function(e){s(e.doc.loadingTask.docId),n.update("doc",e.doc)},onViewerStateChange:function(e){return n.update("currentPage",e.pageIndex),n.update("pagesRotation",e.pagesRotation),n.update("pageHeight",e.pageHeight),n.update("pageWidth",e.pageWidth),n.update("rotation",e.rotation),n.update("rotatedPage",e.rotatedPage),n.update("viewMode",e.viewMode),e},Cover:function(e){return a.createElement(o,i({},e,{renderSpinner:null==t?void 0:t.renderSpinner,store:n}))},Thumbnails:a.useCallback((function(e){return a.createElement(c.Provider,{value:{renderSpinner:(null==t?void 0:t.renderSpinner)||u}},a.createElement(h,{renderCurrentPageLabel:null==t?void 0:t.renderCurrentPageLabel,renderThumbnailItem:null==e?void 0:e.renderThumbnailItem,store:n,thumbnailDirection:(null==e?void 0:e.thumbnailDirection)||exports.ThumbnailDirection.Vertical,thumbnailWidth:(null==t?void 0:t.thumbnailWidth)||100}))}),[l])}};
