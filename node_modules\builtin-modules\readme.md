# builtin-modules

> List of the Node.js builtin modules

The list is just a [JSON file](builtin-modules.json) and can be used anywhere.

## Install

```
$ npm install builtin-modules
```

## Usage

```js
const builtinModules = require('builtin-modules');

console.log(builtinModules);
//=> ['assert', 'buffer', ...]
```

## API

Returns an array of builtin modules fetched from the running Node.js version.

### Static list

This module also comes bundled with a static array of builtin modules generated from the latest Node.js version. You can get it with `require('builtin-modules/static');`

## Related

- [is-builtin-module](https://github.com/sindresorhus/is-builtin-module) - Check if a string matches the name of a Node.js builtin module

---

<div align="center">
	<b>
		<a href="https://tidelift.com/subscription/pkg/npm-builtin-modules?utm_source=npm-builtin-modules&utm_medium=referral&utm_campaign=readme">Get professional support for this package with a Tidelift subscription</a>
	</b>
	<br>
	<sub>
		Tidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.
	</sub>
</div>
