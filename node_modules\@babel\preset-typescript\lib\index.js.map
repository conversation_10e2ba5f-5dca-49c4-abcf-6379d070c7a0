{"version": 3, "file": "index.js", "sources": ["../src/normalize-options.ts", "../src/plugin-rewrite-ts-imports.ts", "../src/index.ts"], "sourcesContent": ["import { OptionValidator } from \"@babel/helper-validator-option\";\nconst v = new OptionValidator(\"@babel/preset-typescript\");\n\nexport interface Options {\n  ignoreExtensions?: boolean;\n  allowDeclareFields?: boolean;\n  allowNamespaces?: boolean;\n  disallowAmbiguousJSXLike?: boolean;\n  jsxPragma?: string;\n  jsxPragmaFrag?: string;\n  onlyRemoveTypeImports?: boolean;\n  optimizeConstEnums?: boolean;\n  rewriteImportExtensions?: boolean;\n\n  // TODO: Remove in Babel 8\n  allExtensions?: boolean;\n  isTSX?: boolean;\n}\n\nexport default function normalizeOptions(options: Options = {}) {\n  let { allowNamespaces = true, jsxPragma, onlyRemoveTypeImports } = options;\n\n  const TopLevelOptions: {\n    [Key in keyof Omit<Options, \"allowDeclareFields\">]-?: Key;\n  } = {\n    ignoreExtensions: \"ignoreExtensions\",\n    allowNamespaces: \"allowNamespaces\",\n    disallowAmbiguousJSXLike: \"disallowAmbiguousJSXLike\",\n    jsxPragma: \"jsxPragma\",\n    jsxPragmaFrag: \"jsxPragmaFrag\",\n    onlyRemoveTypeImports: \"onlyRemoveTypeImports\",\n    optimizeConstEnums: \"optimizeConstEnums\",\n    rewriteImportExtensions: \"rewriteImportExtensions\",\n\n    // TODO: Remove in Babel 8\n    allExtensions: \"allExtensions\",\n    isTSX: \"isTSX\",\n  };\n\n  if (process.env.BABEL_8_BREAKING) {\n    v.invariant(\n      !(\"allowDeclareFields\" in options),\n      \"The .allowDeclareFields option has been removed and it's now always enabled. Please remove it from your config.\",\n    );\n    v.invariant(\n      !(\"allExtensions\" in options) && !(\"isTSX\" in options),\n      \"The .allExtensions and .isTSX options have been removed.\\n\" +\n        \"If you want to disable JSX detection based on file extensions, \" +\n        \"you can set the .ignoreExtensions option to true.\\n\" +\n        \"If you want to force JSX parsing, you can enable the \" +\n        \"@babel/plugin-syntax-jsx plugin.\",\n    );\n\n    v.validateTopLevelOptions(options, TopLevelOptions);\n    allowNamespaces = v.validateBooleanOption(\n      TopLevelOptions.allowNamespaces,\n      options.allowNamespaces,\n      true,\n    );\n    jsxPragma = v.validateStringOption(\n      TopLevelOptions.jsxPragma,\n      options.jsxPragma,\n      \"React\",\n    );\n    onlyRemoveTypeImports = v.validateBooleanOption(\n      TopLevelOptions.onlyRemoveTypeImports,\n      options.onlyRemoveTypeImports,\n      true,\n    );\n  }\n\n  const jsxPragmaFrag = v.validateStringOption(\n    TopLevelOptions.jsxPragmaFrag,\n    options.jsxPragmaFrag,\n    \"React.Fragment\",\n  );\n\n  if (!process.env.BABEL_8_BREAKING) {\n    // eslint-disable-next-line no-var\n    var allExtensions = v.validateBooleanOption(\n      TopLevelOptions.allExtensions,\n      options.allExtensions,\n      false,\n    );\n\n    // eslint-disable-next-line no-var\n    var isTSX = v.validateBooleanOption(\n      TopLevelOptions.isTSX,\n      options.isTSX,\n      false,\n    );\n    if (isTSX) {\n      v.invariant(allExtensions, \"isTSX:true requires allExtensions:true\");\n    }\n  }\n\n  const ignoreExtensions = v.validateBooleanOption(\n    TopLevelOptions.ignoreExtensions,\n    options.ignoreExtensions,\n    false,\n  );\n\n  const disallowAmbiguousJSXLike = v.validateBooleanOption(\n    TopLevelOptions.disallowAmbiguousJSXLike,\n    options.disallowAmbiguousJSXLike,\n    false,\n  );\n  if (disallowAmbiguousJSXLike) {\n    if (process.env.BABEL_8_BREAKING) {\n      v.invariant(\n        ignoreExtensions,\n        \"disallowAmbiguousJSXLike:true requires ignoreExtensions:true\",\n      );\n    } else {\n      v.invariant(\n        allExtensions,\n        \"disallowAmbiguousJSXLike:true requires allExtensions:true\",\n      );\n    }\n  }\n\n  const optimizeConstEnums = v.validateBooleanOption(\n    TopLevelOptions.optimizeConstEnums,\n    options.optimizeConstEnums,\n    false,\n  );\n\n  const rewriteImportExtensions = v.validateBooleanOption(\n    TopLevelOptions.rewriteImportExtensions,\n    options.rewriteImportExtensions,\n    false,\n  );\n\n  const normalized: Options = {\n    ignoreExtensions,\n    allowNamespaces,\n    disallowAmbiguousJSXLike,\n    jsxPragma,\n    jsxPragmaFrag,\n    onlyRemoveTypeImports,\n    optimizeConstEnums,\n    rewriteImportExtensions,\n  };\n  if (!process.env.BABEL_8_BREAKING) {\n    normalized.allExtensions = allExtensions;\n    normalized.isTSX = isTSX;\n  }\n  return normalized;\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport type { types as t, <PERSON>de<PERSON><PERSON>, PluginPass } from \"@babel/core\";\n\nexport default declare(function ({ types: t, template }) {\n  function maybeReplace(\n    source: t.ArgumentPlaceholder | t.Expression,\n    path: NodePath,\n    state: PluginPass,\n  ) {\n    if (!source) return;\n    // todo: if we want to support `preserveJsx`, we can register a global flag via file.set from transform-react-jsx, and read it here.\n    const preserveJsx = false;\n    if (t.isStringLiteral(source)) {\n      if (/^\\.\\.?\\//.test(source.value)) {\n        // @see packages/babel-helpers/src/helpers/tsRewriteRelativeImportExtensions.ts\n        source.value = source.value.replace(\n          /\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+)?)\\.([cm]?)ts$/i,\n          function (m, tsx, d, ext, cm) {\n            return tsx\n              ? preserveJsx\n                ? \".jsx\"\n                : \".js\"\n              : d && (!ext || !cm)\n                ? m\n                : d + ext + \".\" + cm.toLowerCase() + \"js\";\n          },\n        );\n      }\n      return;\n    }\n\n    if (\n      process.env.BABEL_8_BREAKING ||\n      state.availableHelper(\"tsRewriteRelativeImportExtensions\")\n    ) {\n      path.replaceWith(\n        t.callExpression(\n          state.addHelper(\"tsRewriteRelativeImportExtensions\"),\n          preserveJsx ? [source, t.booleanLiteral(true)] : [source],\n        ),\n      );\n    } else {\n      path.replaceWith(\n        template.expression\n          .ast`(${source} + \"\").replace(/([\\\\/].*\\.[mc]?)tsx?$/, \"$1js\")`,\n      );\n    }\n  }\n\n  return {\n    name: \"preset-typescript/plugin-rewrite-ts-imports\",\n    visitor: {\n      \"ImportDeclaration|ExportAllDeclaration|ExportNamedDeclaration\"(\n        path: NodePath<\n          | t.ImportDeclaration\n          | t.ExportAllDeclaration\n          | t.ExportNamedDeclaration\n        >,\n        state,\n      ) {\n        const node = path.node;\n        const kind = t.isImportDeclaration(node)\n          ? node.importKind\n          : node.exportKind;\n        if (kind === \"value\") {\n          maybeReplace(node.source, path.get(\"source\"), state);\n        }\n      },\n      CallExpression(path, state) {\n        if (!process.env.BABEL_8_BREAKING && t.isImport(path.node.callee)) {\n          maybeReplace(\n            // The argument of import must not be a spread element\n            path.node.arguments[0] as t.ArgumentPlaceholder | t.Expression,\n            path.get(\"arguments.0\"),\n            state,\n          );\n        }\n      },\n      ImportExpression(path, state) {\n        maybeReplace(path.node.source, path.get(\"source\"), state);\n      },\n    },\n  };\n});\n", "import { declarePreset } from \"@babel/helper-plugin-utils\";\nimport transformTypeScript from \"@babel/plugin-transform-typescript\";\nimport syntaxJSX from \"@babel/plugin-syntax-jsx\";\nimport transformModulesCommonJS from \"@babel/plugin-transform-modules-commonjs\";\nimport normalizeOptions from \"./normalize-options.ts\";\nimport type { Options } from \"./normalize-options.ts\";\nimport pluginRewriteTSImports from \"./plugin-rewrite-ts-imports.ts\";\n\nexport default declarePreset((api, opts: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const {\n    allExtensions,\n    ignoreExtensions,\n    allowNamespaces,\n    disallowAmbiguousJSXLike,\n    isTSX,\n    jsxPragma,\n    jsxPragmaFrag,\n    onlyRemoveTypeImports,\n    optimizeConstEnums,\n    rewriteImportExtensions,\n  } = normalizeOptions(opts);\n\n  const pluginOptions = process.env.BABEL_8_BREAKING\n    ? (disallowAmbiguousJSXLike: boolean) => ({\n        allowNamespaces,\n        disallowAmbiguousJSXLike,\n        jsxPragma,\n        jsxPragmaFrag,\n        onlyRemoveTypeImports,\n        optimizeConstEnums,\n      })\n    : (disallowAmbiguousJSXLike: boolean) => ({\n        allowDeclareFields: opts.allowDeclareFields,\n        allowNamespaces,\n        disallowAmbiguousJSXLike,\n        jsxPragma,\n        jsxPragmaFrag,\n        onlyRemoveTypeImports,\n        optimizeConstEnums,\n      });\n\n  const getPlugins = (isTSX: boolean, disallowAmbiguousJSXLike: boolean) => {\n    if (process.env.BABEL_8_BREAKING) {\n      const tsPlugin = [\n        transformTypeScript,\n        pluginOptions(disallowAmbiguousJSXLike),\n      ];\n      return isTSX ? [tsPlugin, syntaxJSX] : [tsPlugin];\n    } else {\n      return [\n        [\n          transformTypeScript,\n          { isTSX, ...pluginOptions(disallowAmbiguousJSXLike) },\n        ],\n      ];\n    }\n  };\n\n  const disableExtensionDetect = allExtensions || ignoreExtensions;\n\n  return {\n    plugins: rewriteImportExtensions ? [pluginRewriteTSImports] : [],\n    overrides: disableExtensionDetect\n      ? [{ plugins: getPlugins(isTSX, disallowAmbiguousJSXLike) }]\n      : // Only set 'test' if explicitly requested, since it requires that\n        // Babel is being called with a filename.\n        [\n          {\n            test: !process.env.BABEL_8_BREAKING\n              ? /\\.ts$/\n              : filename => filename == null || filename.endsWith(\".ts\"),\n            plugins: getPlugins(false, false),\n          },\n          {\n            test: !process.env.BABEL_8_BREAKING\n              ? /\\.mts$/\n              : filename => filename?.endsWith(\".mts\"),\n            sourceType: \"module\",\n            plugins: getPlugins(false, true),\n          },\n          {\n            test: !process.env.BABEL_8_BREAKING\n              ? /\\.cts$/\n              : filename => filename?.endsWith(\".cts\"),\n            sourceType: \"unambiguous\",\n            plugins: [\n              [transformModulesCommonJS, { allowTopLevelThis: true }],\n              [transformTypeScript, pluginOptions(true)],\n            ],\n          },\n          {\n            test: !process.env.BABEL_8_BREAKING\n              ? /\\.tsx$/\n              : filename => filename?.endsWith(\".tsx\"),\n            // disallowAmbiguousJSXLike is a no-op when parsing TSX, since it's\n            // always disallowed.\n            plugins: getPlugins(true, false),\n          },\n        ],\n  };\n});\n"], "names": ["v", "OptionValidator", "normalizeOptions", "options", "allowNamespaces", "jsxPragma", "onlyRemoveTypeImports", "TopLevelOptions", "ignoreExtensions", "disallowAmbiguousJSXLike", "jsxPragmaFrag", "optimizeConstEnums", "rewriteImportExtensions", "allExtensions", "isTSX", "validateStringOption", "validateBooleanOption", "invariant", "normalized", "declare", "types", "t", "template", "<PERSON><PERSON><PERSON><PERSON>", "source", "path", "state", "isStringLiteral", "test", "value", "replace", "m", "tsx", "d", "ext", "cm", "toLowerCase", "availableHelper", "replaceWith", "callExpression", "addHelper", "expression", "ast", "name", "visitor", "ImportDeclaration|ExportAllDeclaration|ExportNamedDeclaration", "node", "kind", "isImportDeclaration", "importKind", "exportKind", "get", "CallExpression", "isImport", "callee", "arguments", "ImportExpression", "declarePreset", "api", "opts", "assertVersion", "pluginOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getPlugins", "transformTypeScript", "Object", "assign", "disableExtensionDetect", "plugins", "pluginRewriteTSImports", "overrides", "sourceType", "transformModulesCommonJS", "allowTopLevelThis"], "mappings": ";;;;;;;;;;;;;;;AACA,MAAMA,CAAC,GAAG,IAAIC,qCAAe,CAAC,0BAA0B,CAAC,CAAA;AAkB1C,SAASC,gBAAgBA,CAACC,OAAgB,GAAG,EAAE,EAAE;EAC9D,IAAI;AAAEC,IAAAA,eAAe,GAAG,IAAI;IAAEC,SAAS;AAAEC,IAAAA,qBAAAA;AAAsB,GAAC,GAAGH,OAAO,CAAA;AAE1E,EAAA,MAAMI,eAEL,GAAG;AACFC,IAAAA,gBAAgB,EAAE,kBAAkB;AACpCJ,IAAAA,eAAe,EAAE,iBAAiB;AAClCK,IAAAA,wBAAwB,EAAE,0BAA0B;AACpDJ,IAAAA,SAAS,EAAE,WAAW;AACtBK,IAAAA,aAAa,EAAE,eAAe;AAC9BJ,IAAAA,qBAAqB,EAAE,uBAAuB;AAC9CK,IAAAA,kBAAkB,EAAE,oBAAoB;AACxCC,IAAAA,uBAAuB,EAAE,yBAAyB;AAGlDC,IAAAA,aAAa,EAAE,eAAe;AAC9BC,IAAAA,KAAK,EAAE,OAAA;GACR,CAAA;AAkCD,EAAA,MAAMJ,aAAa,GAAGV,CAAC,CAACe,oBAAoB,CAC1CR,eAAe,CAACG,aAAa,EAC7BP,OAAO,CAACO,aAAa,EACrB,gBACF,CAAC,CAAA;AAEkC,EAAA;AAEjC,IAAA,IAAIG,aAAa,GAAGb,CAAC,CAACgB,qBAAqB,CACzCT,eAAe,CAACM,aAAa,EAC7BV,OAAO,CAACU,aAAa,EACrB,KACF,CAAC,CAAA;AAGD,IAAA,IAAIC,KAAK,GAAGd,CAAC,CAACgB,qBAAqB,CACjCT,eAAe,CAACO,KAAK,EACrBX,OAAO,CAACW,KAAK,EACb,KACF,CAAC,CAAA;AACD,IAAA,IAAIA,KAAK,EAAE;AACTd,MAAAA,CAAC,CAACiB,SAAS,CAACJ,aAAa,EAAE,wCAAwC,CAAC,CAAA;AACtE,KAAA;AACF,GAAA;AAEA,EAAA,MAAML,gBAAgB,GAAGR,CAAC,CAACgB,qBAAqB,CAC9CT,eAAe,CAACC,gBAAgB,EAChCL,OAAO,CAACK,gBAAgB,EACxB,KACF,CAAC,CAAA;AAED,EAAA,MAAMC,wBAAwB,GAAGT,CAAC,CAACgB,qBAAqB,CACtDT,eAAe,CAACE,wBAAwB,EACxCN,OAAO,CAACM,wBAAwB,EAChC,KACF,CAAC,CAAA;AACD,EAAA,IAAIA,wBAAwB,EAAE;AAMrB,IAAA;AACLT,MAAAA,CAAC,CAACiB,SAAS,CACTJ,aAAa,EACb,2DACF,CAAC,CAAA;AACH,KAAA;AACF,GAAA;AAEA,EAAA,MAAMF,kBAAkB,GAAGX,CAAC,CAACgB,qBAAqB,CAChDT,eAAe,CAACI,kBAAkB,EAClCR,OAAO,CAACQ,kBAAkB,EAC1B,KACF,CAAC,CAAA;AAED,EAAA,MAAMC,uBAAuB,GAAGZ,CAAC,CAACgB,qBAAqB,CACrDT,eAAe,CAACK,uBAAuB,EACvCT,OAAO,CAACS,uBAAuB,EAC/B,KACF,CAAC,CAAA;AAED,EAAA,MAAMM,UAAmB,GAAG;IAC1BV,gBAAgB;IAChBJ,eAAe;IACfK,wBAAwB;IACxBJ,SAAS;IACTK,aAAa;IACbJ,qBAAqB;IACrBK,kBAAkB;AAClBC,IAAAA,uBAAAA;GACD,CAAA;AACkC,EAAA;IACjCM,UAAU,CAACL,aAAa,GAAGA,aAAa,CAAA;IACxCK,UAAU,CAACJ,KAAK,GAAGA,KAAK,CAAA;AAC1B,GAAA;AACA,EAAA,OAAOI,UAAU,CAAA;AACnB;;ACjJA,6BAAeC,yBAAO,CAAC,UAAU;AAAEC,EAAAA,KAAK,EAAEC,CAAC;AAAEC,EAAAA,QAAAA;AAAS,CAAC,EAAE;AACvD,EAAA,SAASC,YAAYA,CACnBC,MAA4C,EAC5CC,IAAc,EACdC,KAAiB,EACjB;IACA,IAAI,CAACF,MAAM,EAAE,OAAA;AAGb,IAAA,IAAIH,CAAC,CAACM,eAAe,CAACH,MAAM,CAAC,EAAE;MAC7B,IAAI,UAAU,CAACI,IAAI,CAACJ,MAAM,CAACK,KAAK,CAAC,EAAE;QAEjCL,MAAM,CAACK,KAAK,GAAGL,MAAM,CAACK,KAAK,CAACC,OAAO,CACjC,iDAAiD,EACjD,UAAUC,CAAC,EAAEC,GAAG,EAAEC,CAAC,EAAEC,GAAG,EAAEC,EAAE,EAAE;AAC5B,UAAA,OAAOH,GAAG,GAGJ,KAAK,GACPC,CAAC,KAAK,CAACC,GAAG,IAAI,CAACC,EAAE,CAAC,GAChBJ,CAAC,GACDE,CAAC,GAAGC,GAAG,GAAG,GAAG,GAAGC,EAAE,CAACC,WAAW,EAAE,GAAG,IAAI,CAAA;AAC/C,SACF,CAAC,CAAA;AACH,OAAA;AACA,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,IAEEV,KAAK,CAACW,eAAe,CAAC,mCAAmC,CAAC,EAC1D;AACAZ,MAAAA,IAAI,CAACa,WAAW,CACdjB,CAAC,CAACkB,cAAc,CACdb,KAAK,CAACc,SAAS,CAAC,mCAAmC,CAAC,EACH,CAAChB,MAAM,CAC1D,CACF,CAAC,CAAA;AACH,KAAC,MAAM;MACLC,IAAI,CAACa,WAAW,CACdhB,QAAQ,CAACmB,UAAU,CAChBC,GAAG,CAAA,CAAA,EAAIlB,MAAM,CAAA,+CAAA,CAClB,CAAC,CAAA;AACH,KAAA;AACF,GAAA;EAEA,OAAO;AACLmB,IAAAA,IAAI,EAAE,6CAA6C;AACnDC,IAAAA,OAAO,EAAE;AACP,MAAA,+DAA+DC,CAC7DpB,IAIC,EACDC,KAAK,EACL;AACA,QAAA,MAAMoB,IAAI,GAAGrB,IAAI,CAACqB,IAAI,CAAA;AACtB,QAAA,MAAMC,IAAI,GAAG1B,CAAC,CAAC2B,mBAAmB,CAACF,IAAI,CAAC,GACpCA,IAAI,CAACG,UAAU,GACfH,IAAI,CAACI,UAAU,CAAA;QACnB,IAAIH,IAAI,KAAK,OAAO,EAAE;AACpBxB,UAAAA,YAAY,CAACuB,IAAI,CAACtB,MAAM,EAAEC,IAAI,CAAC0B,GAAG,CAAC,QAAQ,CAAC,EAAEzB,KAAK,CAAC,CAAA;AACtD,SAAA;OACD;AACD0B,MAAAA,cAAcA,CAAC3B,IAAI,EAAEC,KAAK,EAAE;QAC1B,IAAqCL,CAAC,CAACgC,QAAQ,CAAC5B,IAAI,CAACqB,IAAI,CAACQ,MAAM,CAAC,EAAE;AACjE/B,UAAAA,YAAY,CAEVE,IAAI,CAACqB,IAAI,CAACS,SAAS,CAAC,CAAC,CAAC,EACtB9B,IAAI,CAAC0B,GAAG,CAAC,aAAa,CAAC,EACvBzB,KACF,CAAC,CAAA;AACH,SAAA;OACD;AACD8B,MAAAA,gBAAgBA,CAAC/B,IAAI,EAAEC,KAAK,EAAE;AAC5BH,QAAAA,YAAY,CAACE,IAAI,CAACqB,IAAI,CAACtB,MAAM,EAAEC,IAAI,CAAC0B,GAAG,CAAC,QAAQ,CAAC,EAAEzB,KAAK,CAAC,CAAA;AAC3D,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;AC3EF,YAAe+B,+BAAa,CAAC,CAACC,GAAG,EAAEC,IAAa,KAAK;AACnDD,EAAAA,GAAG,CAACE,aAAa,CAAkB,CAAE,CAAC,CAAA;EAEtC,MAAM;IACJ/C,aAAa;IACbL,gBAAgB;IAChBJ,eAAe;IACfK,wBAAwB;IACxBK,KAAK;IACLT,SAAS;IACTK,aAAa;IACbJ,qBAAqB;IACrBK,kBAAkB;AAClBC,IAAAA,uBAAAA;AACF,GAAC,GAAGV,gBAAgB,CAACyD,IAAI,CAAC,CAAA;EAE1B,MAAME,aAAa,GASdpD,wBAAiC,KAAM;IACtCqD,kBAAkB,EAAEH,IAAI,CAACG,kBAAkB;IAC3C1D,eAAe;IACfK,wBAAwB;IACxBJ,SAAS;IACTK,aAAa;IACbJ,qBAAqB;AACrBK,IAAAA,kBAAAA;AACF,GAAC,CAAC,CAAA;AAEN,EAAA,MAAMoD,UAAU,GAAGA,CAACjD,KAAc,EAAEL,wBAAiC,KAAK;AAOjE,IAAA;AACL,MAAA,OAAO,CACL,CACEuD,oCAAmB,EAAAC,MAAA,CAAAC,MAAA,CAAA;AACjBpD,QAAAA,KAAAA;AAAK,OAAA,EAAK+C,aAAa,CAACpD,wBAAwB,CAAC,EACpD,CACF,CAAA;AACH,KAAA;GACD,CAAA;AAED,EAAA,MAAM0D,sBAAsB,GAAGtD,aAAa,IAAIL,gBAAgB,CAAA;EAEhE,OAAO;AACL4D,IAAAA,OAAO,EAAExD,uBAAuB,GAAG,CAACyD,sBAAsB,CAAC,GAAG,EAAE;IAChEC,SAAS,EAAEH,sBAAsB,GAC7B,CAAC;AAAEC,MAAAA,OAAO,EAAEL,UAAU,CAACjD,KAAK,EAAEL,wBAAwB,CAAA;KAAG,CAAC,GAG1D,CACE;AACEmB,MAAAA,IAAI,EACA,OACwD;AAC5DwC,MAAAA,OAAO,EAAEL,UAAU,CAAC,KAAK,EAAE,KAAK,CAAA;AAClC,KAAC,EACD;AACEnC,MAAAA,IAAI,EACA,QACsC;AAC1C2C,MAAAA,UAAU,EAAE,QAAQ;AACpBH,MAAAA,OAAO,EAAEL,UAAU,CAAC,KAAK,EAAE,IAAI,CAAA;AACjC,KAAC,EACD;AACEnC,MAAAA,IAAI,EACA,QACsC;AAC1C2C,MAAAA,UAAU,EAAE,aAAa;AACzBH,MAAAA,OAAO,EAAE,CACP,CAACI,yCAAwB,EAAE;AAAEC,QAAAA,iBAAiB,EAAE,IAAA;OAAM,CAAC,EACvD,CAACT,oCAAmB,EAAEH,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;AAE9C,KAAC,EACD;AACEjC,MAAAA,IAAI,EACA,QACsC;AAG1CwC,MAAAA,OAAO,EAAEL,UAAU,CAAC,IAAI,EAAE,KAAK,CAAA;KAChC,CAAA;GAER,CAAA;AACH,CAAC,CAAC;;;;"}