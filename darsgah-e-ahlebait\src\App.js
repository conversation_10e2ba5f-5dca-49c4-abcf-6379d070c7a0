import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import './App.css';
import Home from './Home';
import JBS from './JBS';
import JNS from './JNS';
import JIS from './JIS';
import Contact from './Contact';
import Submit from './Submit';
import PublicationDetail from './PublicationDetail';
import VolumesIssues from './VolumesIssues';
import VolumeDetail from './VolumeDetail';
import IssueDetail from './IssueDetail';
import VolumeIssues from './VolumeIssues';
import Login from './Login';
import Register from './Register';
import Profile from './Profile';
import AdminPanel from './AdminPanel';
import EditorPanel from './EditorPanel';
import ReviewerPanel from './ReviewerPanel';
import EditorialBoardPage from './EditorialBoardPage';
import BlogAdminList from './admin/BlogAdminList';
import BlogAdmin from './admin/BlogAdmin';
import BlogList from './blog/BlogList';
import BlogPost from './blog/BlogPost';
import BlogSubmit from './blog/BlogSubmit';
import EventAdmin from './admin/EventAdmin';
import Events from './Events';
import Resources from './Resources';
import Community from './Community';
import Careers from './Careers';
import Help from './Help';
import SubmissionGuidelines from './SubmissionGuidelines';
import AuthorHandbook from './AuthorHandbook';
import ManuscriptTemplate from './ManuscriptTemplate';
import CopyrightTransfer from './CopyrightTransfer';
import ConflictOfInterest from './ConflictOfInterest';
import ProfileSubmissions from './ProfileSubmissions';
import ProfileBlogs from './ProfileBlogs';
import ProfileFavorites from './ProfileFavorites';
import ProfileNotifications from './ProfileNotifications';
import SubmitPdf from './SubmitPdf';
import NotFound from './NotFound';
import AdminAnnouncements from './admin/AdminAnnouncements';
import ReviewerApplication from './ReviewerApplication';
import AdminEditorialBoardSubmissions from './admin/AdminEditorialBoardSubmissions';
import BannedPage from './BannedPage';
import CitationManager from './admin/CitationManager';
import DatabaseTest from './DatabaseTest';

function App() {
  const [user, setUser] = useState(null);
  const [navbarOpen, setNavbarOpen] = useState(false);
  const [bannedUser, setBannedUser] = useState(null);

  useEffect(() => {
    // Load user from localStorage on mount
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      const userData = JSON.parse(storedUser);
      // Check if user is banned
      if (userData.is_banned) {
        // Clear the banned user from localStorage and don't set the user state
        localStorage.removeItem('user');
        setBannedUser(userData);
      } else {
        setUser(userData);
      }
    }
  }, []);

  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem('user');
  };

  // If user is banned, show banned page
  if (bannedUser) {
    return (
      <Router>
        <BannedPage banReason={bannedUser.ban_reason} />
      </Router>
    );
  }

  return (
    <Router>
      <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" />
      <div className="bg-primary bg-opacity-10 py-2 border-bottom" style={{fontSize: '1.05rem', letterSpacing: '0.5px'}}>
        <div className="container-fluid d-flex flex-wrap justify-content-between align-items-center gap-4">
          <div className="d-flex flex-wrap align-items-center gap-4">
            <span className="text-primary fw-bold"><i className="bi bi-envelope me-2"></i><EMAIL></span>
            <span className="text-secondary"><i className="bi bi-telephone me-2"></i>03444244544</span>
          </div>
          <div className="d-flex align-items-center gap-2">
            {user ? (
              <>
                <span className="fw-bold text-primary"><i className="bi bi-person-circle me-1"></i> {user.full_name || user.email}</span>
                <Link to="/profile" className="btn btn-outline-info btn-sm ms-2">Profile</Link>
                {user.role === 'admin' && (
                  <Link to="/admin" className="btn btn-warning btn-sm ms-2">Admin</Link>
                )}
                {user.role === 'editor' && (
                  <Link to="/editor" className="btn btn-secondary btn-sm ms-2">Editor</Link>
                )}
                {user.role === 'reviewer' && (
                  <Link to="/reviewer" className="btn btn-success btn-sm ms-2">Reviewer</Link>
                )}
                <button className="btn btn-outline-danger btn-sm ms-2" onClick={handleLogout}>Logout</button>
              </>
            ) : (
              <>
                <Link to="/login" className="btn btn-outline-primary btn-sm d-flex align-items-center">
                  <i className="bi bi-box-arrow-in-right me-1"></i> Login
                </Link>
                <Link to="/register" className="btn btn-primary btn-sm d-flex align-items-center">
                  <i className="bi bi-person-plus me-1"></i> Register
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
      <nav className="navbar navbar-expand-lg navbar-dark bg-dark mb-4 py-3" style={{minHeight: '90px', fontSize: '1.22rem', boxShadow: '0 4px 24px rgba(0,0,0,0.08)'}}>
        <div className="container-fluid align-items-center">
          <Link className="navbar-brand text-lowercase fw-bold" to="/" style={{fontSize: '2.2rem', letterSpacing: '1px'}}>darsgah-e-ahlebait</Link>
          <button
            className="navbar-toggler"
            type="button"
            onClick={() => setNavbarOpen(!navbarOpen)}
            aria-controls="navbarNav"
            aria-expanded={navbarOpen}
            aria-label="Toggle navigation"
          >
            <span className="navbar-toggler-icon"></span>
          </button>
          <div className={`collapse navbar-collapse${navbarOpen ? ' show' : ''}`} id="navbarNav">
            <ul className="navbar-nav ms-auto me-4 gap-2">
              <li className="nav-item"><Link className="nav-link fw-bold px-4 py-2 rounded nav-link-custom" to="/jbs">JBS</Link></li>
              <li className="nav-item"><Link className="nav-link fw-bold px-4 py-2 rounded nav-link-custom" to="/jns">JNS</Link></li>
              <li className="nav-item"><Link className="nav-link fw-bold px-4 py-2 rounded nav-link-custom" to="/jis">JIS</Link></li>
              <li className="nav-item"><Link className="nav-link fw-bold px-4 py-2 rounded nav-link-custom" to="/volumes-issues">Volumes & Issues</Link></li>
              <li className="nav-item"><Link className="nav-link fw-bold px-4 py-2 rounded nav-link-custom" to="/contact">Contact</Link></li>
              <li className="nav-item"><Link className="nav-link fw-bold px-4 py-2 rounded nav-link-custom" to="/submit">Submit</Link></li>
            </ul>
          </div>
        </div>
      </nav>
      <style>{`
        .nav-link-custom {
          font-size: 1.18rem;
          letter-spacing: 0.5px;
          color: #fff !important;
          background: rgba(0,123,255,0.08);
          transition: background 0.2s, color 0.2s, box-shadow 0.2s;
        }
        .nav-link-custom:hover, .nav-link-custom.active {
          background: #0d6efd !important;
          color: #fff !important;
          box-shadow: 0 2px 12px rgba(13,110,253,0.12);
        }
      `}</style>
      <div className="container">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/jbs" element={<JBS />} />
          <Route path="/jns" element={<JNS />} />
          <Route path="/jis" element={<JIS />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/submit" element={<Submit />} />
          <Route path="/submit/pdf" element={<SubmitPdf />} />
          <Route path="/publication/:id" element={<PublicationDetail />} />
          <Route path="/volumes-issues" element={<VolumesIssues />} />
          <Route path="/volume/:id" element={<VolumeDetail />} />
          <Route path="/issue/:issue_number" element={<IssueDetail />} />
          <Route path="/volume-issues/:volume_number/:year/:journal_type" element={<VolumeIssues />} />
          <Route path="/login" element={<Login setUser={setUser} />} />
          <Route path="/register" element={<Register />} />
          <Route path="/profile" element={<Profile user={user} />} />
          <Route path="/profile/submissions" element={<ProfileSubmissions user={user} />} />
          <Route path="/profile/blogs" element={<ProfileBlogs user={user} />} />
          <Route path="/profile/favorites" element={<ProfileFavorites />} />
          <Route path="/profile/notifications" element={<ProfileNotifications />} />
          <Route path="/admin" element={<AdminPanel user={user} />} />
          <Route path="/editor" element={<EditorPanel user={user} />} />
          <Route path="/reviewer" element={<ReviewerPanel user={user} />} />
          <Route path="/editorial-board/jbs" element={<EditorialBoardPage journalType="jbs" />} />
          <Route path="/editorial-board/jns" element={<EditorialBoardPage journalType="jns" />} />
          <Route path="/editorial-board/jis" element={<EditorialBoardPage journalType="jis" />} />
          <Route path="/editorial-board/apply" element={<ReviewerApplication />} />
          <Route path="/admin/blog" element={<BlogAdminList />} />
          <Route path="/admin/blog/new" element={<BlogAdmin />} />
          <Route path="/admin/blog/edit/:id" element={<BlogAdmin />} />
          <Route path="/admin/events" element={<EventAdmin />} />
          <Route path="/admin/announcements" element={<AdminAnnouncements />} />
          <Route path="/admin/editorial-board-applications" element={<AdminEditorialBoardSubmissions />} />
          <Route path="/admin/citations" element={<CitationManager />} />
          <Route path="/database-test" element={<DatabaseTest />} />
          <Route path="/blog" element={<BlogList />} />
          <Route path="/blog/:slug" element={<BlogPost />} />
          <Route path="/blog/submit" element={<BlogSubmit />} />
          <Route path="/events" element={<Events />} />
          <Route path="/resources" element={<Resources />} />
          <Route path="/community" element={<Community />} />
          <Route path="/careers" element={<Careers />} />
          <Route path="/help" element={<Help />} />
          <Route path="/submission-guidelines" element={<SubmissionGuidelines />} />
          <Route path="/author-handbook" element={<AuthorHandbook />} />
          <Route path="/manuscript-template" element={<ManuscriptTemplate />} />
          <Route path="/copyright-transfer" element={<CopyrightTransfer />} />
          <Route path="/conflict-of-interest" element={<ConflictOfInterest />} />
          <Route path="/banned" element={<BannedPage />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </div>
      <footer className="bg-dark text-light mt-5 pt-5 pb-4">
        <div className="container">
          <div className="row mb-4">
            <div className="col-md-4 mb-3">
              <h5 className="text-uppercase mb-3">About darsgah-e-ahlebait</h5>
              <p style={{fontSize: '15px'}}>A platform for open access, peer-reviewed research in biotechnology, natural sciences, and the intersection of Islam and science. We connect scholars, foster innovation, and promote global knowledge sharing.</p>
            </div>
            <div className="col-md-4 mb-3">
              <h5 className="text-uppercase mb-3">Quick Links</h5>
              <ul className="list-unstyled">
                <li><Link className="text-light text-decoration-none" to="/">Home</Link></li>
                <li><Link className="text-light text-decoration-none" to="/jbs">JBS</Link></li>
                <li><Link className="text-light text-decoration-none" to="/jns">JNS</Link></li>
                <li><Link className="text-light text-decoration-none" to="/jis">JIS</Link></li>
                <li><Link className="text-light text-decoration-none" to="/volumes-issues">Volumes & Issues</Link></li>
                <li><Link className="text-light text-decoration-none" to="/contact">Contact</Link></li>
                <li><Link className="text-light text-decoration-none" to="/submit">Submit</Link></li>
              </ul>
            </div>
            <div className="col-md-4 mb-3">
              <h5 className="text-uppercase mb-3">Contact</h5>
              <ul className="list-unstyled" style={{fontSize: '15px'}}>
                <li>Email: <a href="mailto:<EMAIL>" className="text-light"><EMAIL></a></li>
                <li>Phone: 03444244544</li>
              </ul>
            </div>
          </div>
          <div className="text-center border-top pt-3" style={{fontSize: '14px'}}>
            &copy; {new Date().getFullYear()} darsgah-e-ahlebait. All rights reserved.
          </div>
        </div>
      </footer>
    </Router>
  );
}

export default App;
