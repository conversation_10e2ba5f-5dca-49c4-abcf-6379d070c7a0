"use strict";var e=require("@react-pdf-viewer/core");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var n=t(require("react")),r=function(){return n.createElement(e.Icon,{size:16},n.createElement("path",{d:"M18.5,7.5c.275,0,.341-.159.146-.354L12.354.854a.5.5,0,0,0-.708,0L5.354,7.147c-.2.195-.129.354.146.354h3v10a1,1,0,0,0,1,1h5a1,1,0,0,0,1-1V7.5Z"}),n.createElement("path",{d:"M23.5,18.5v4a1,1,0,0,1-1,1H1.5a1,1,0,0,1-1-1v-4"}))},o=function(){return o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},o.apply(this,arguments)},i=function(e){var t=n.useRef(),r=function(){var n=t.current;n&&(n.click(),e.get("triggerOpenFile")&&e.update("triggerOpenFile",!1))},o=function(e){e&&r()};return n.useEffect((function(){return e.subscribe("triggerOpenFile",o),function(){e.unsubscribe("triggerOpenFile",o)}}),[]),{inputRef:t,openFile:r}},c={left:0,top:8},u=function(t){var o=t.enableShortcuts,u=t.store,a=t.onClick,l=n.useContext(e.LocalizationContext).l10n,p=l&&l.open?l.open.openFile:"Open file",f=i(u),s=f.inputRef,m=f.openFile,v=o?e.isMac()?"Meta+O":"Ctrl+O":"";return n.createElement(e.Tooltip,{ariaControlsSuffix:"open",position:e.Position.BottomCenter,target:n.createElement("div",{className:"rpv-open__input-wrapper"},n.createElement("input",{accept:".pdf",ref:s,className:"rpv-open__input",multiple:!1,tabIndex:-1,title:"",type:"file",onChange:a}),n.createElement(e.MinimalButton,{ariaKeyShortcuts:v,ariaLabel:p,testId:"open__button",onClick:m},n.createElement(r,null))),content:function(){return p},offset:c})},a=function(e){var t=e.children,r=e.enableShortcuts,o=e.store;return(t||function(e){return n.createElement(u,{enableShortcuts:r,store:o,onClick:e.onClick})})({onClick:function(e){var t=e.target.files;if(t&&t.length){var n=o.get("openFile");n&&n(t[0])}}})},l=function(t){var o=t.store,c=t.onClick,u=n.useContext(e.LocalizationContext).l10n,a=u&&u.open?u.open.openFile:"Open file",l=i(o),p=l.inputRef,f=l.openFile;return n.createElement(e.MenuItem,{icon:n.createElement(r,null),testId:"open__menu",onClick:f},n.createElement("div",{className:"rpv-open__input-wrapper"},n.createElement("input",{accept:".pdf",ref:p,className:"rpv-open__input",multiple:!1,tabIndex:-1,title:"",type:"file",onChange:c}),a))},p=function(t){var r=t.containerRef,o=t.store,i=function(t){if(!t.shiftKey&&!t.altKey&&"o"===t.key&&(e.isMac()?t.metaKey:t.ctrlKey)){var n=r.current;n&&document.activeElement&&n.contains(document.activeElement)&&(t.preventDefault(),o.update("triggerOpenFile",!0))}};return n.useEffect((function(){if(r.current)return document.addEventListener("keydown",i),function(){document.removeEventListener("keydown",i)}}),[r.current]),n.createElement(n.Fragment,null)};exports.OpenFileIcon=r,exports.openPlugin=function(t){var r=n.useMemo((function(){return Object.assign({},{enableShortcuts:!0},t)}),[]),i=n.useMemo((function(){return e.createStore({})}),[]),c=function(e){return n.createElement(a,o({enableShortcuts:r.enableShortcuts},e,{store:i}))};return{install:function(e){i.update("openFile",e.openFile)},renderViewer:function(e){var t=e.slot,c={children:n.createElement(n.Fragment,null,r.enableShortcuts&&n.createElement(p,{containerRef:e.containerRef,store:i}),t.children)};return o(o({},t),c)},Open:c,OpenButton:function(){return n.createElement(c,null)},OpenMenuItem:function(){return n.createElement(c,null,(function(e){return n.createElement(l,{store:i,onClick:e.onClick})}))}}};
