{"name": "@types/warning", "version": "3.0.3", "description": "TypeScript definitions for warning", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/warning", "license": "MIT", "contributors": [{"name": "<PERSON> Le", "githubUsername": "cvle", "url": "https://github.com/cvle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/warning"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "31a2af9a6e16ffc50a5005c5fe2ed2791e3cd2529e9090eae49a34c0446b7a70", "typeScriptVersion": "4.5"}