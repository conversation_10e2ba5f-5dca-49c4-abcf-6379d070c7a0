{"name": "@svgr/babel-plugin-svg-em-dimensions", "description": "Transform SVG to use em-based dimensions", "version": "5.4.0", "main": "lib/index.js", "repository": "https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-svg-em-dimensions", "author": "<PERSON> <<EMAIL>>", "publishConfig": {"access": "public"}, "keywords": ["babel-plugin"], "engines": {"node": ">=10"}, "homepage": "https://react-svgr.com", "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}, "license": "MIT", "scripts": {"prebuild": "rm -rf lib/", "build": "babel --config-file ../../babel.config.js -d lib --ignore \"**/*.test.js\" src", "prepublishOnly": "yarn run build"}, "gitHead": "e9c9d2fbfbce7a6879c90cd8522101caf2406d42"}