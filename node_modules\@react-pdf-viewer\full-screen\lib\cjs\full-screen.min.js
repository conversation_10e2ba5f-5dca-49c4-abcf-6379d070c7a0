"use strict";var e=require("@react-pdf-viewer/core");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var n=t(require("react")),r=function(){return n.createElement(e.Icon,{size:16},n.createElement("path",{d:"M11.5 23.499L11.5 14.499"}),n.createElement("path",{d:"M7.5 18.499L11.5 14.499 15.5 18.499"}),n.createElement("path",{d:"M11.5 1.499L11.5 10.499"}),n.createElement("path",{d:"M7.5 6.499L11.5 10.499 15.5 6.499"}),n.createElement("path",{d:"M20.5 12.499L1.5 12.499"}))},l=function(){return n.createElement(e.Icon,{size:16},n.createElement("path",{d:"M0.5 12L23.5 12"}),n.createElement("path",{d:"M11.5 1L11.5 23"}),n.createElement("path",{d:"M8.5 4L11.5 1 14.5 4"}),n.createElement("path",{d:"M20.5 9L23.5 12 20.5 15"}),n.createElement("path",{d:"M3.5 15L0.5 12 3.5 9"}),n.createElement("path",{d:"M14.5 20L11.5 23 8.5 20"}))},c=function(){return c=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var l in t=arguments[n])Object.prototype.hasOwnProperty.call(t,l)&&(e[l]=t[l]);return e},c.apply(this,arguments)},u={left:0,top:8},o=function(t){var r=t.enableShortcuts,c=t.onClick,o=n.useContext(e.LocalizationContext).l10n,i=o&&o.fullScreen?o.fullScreen.enterFullScreen:"Full screen",a=r?e.isMac()?"Meta+Ctrl+F":"F11":"";return n.createElement(e.Tooltip,{ariaControlsSuffix:"full-screen-enter",position:e.Position.BottomCenter,target:n.createElement(e.MinimalButton,{ariaKeyShortcuts:a,ariaLabel:i,isDisabled:!e.isFullScreenEnabled(),testId:"full-screen__enter-button",onClick:c},n.createElement(l,null)),content:function(){return i},offset:u})},i={left:0,top:8},a=function(t){var l=t.onClick,c=n.useContext(e.LocalizationContext).l10n,u=c&&c.fullScreen?c.fullScreen.exitFullScreen:"Exit full screen";return n.createElement(e.Tooltip,{ariaControlsSuffix:"full-screen-exit",position:e.Position.BottomCenter,target:n.createElement(e.MinimalButton,{ariaKeyShortcuts:"Esc",ariaLabel:u,testId:"full-screen__exit-button-with-tooltip",onClick:l},n.createElement(r,null)),content:function(){return u},offset:i})},f=function(t,r){var l=n.useState(r.get("fullScreenMode")),c=l[0],u=l[1],o=n.useCallback((function(e){u(e)}),[]);return n.useEffect((function(){return r.subscribe("fullScreenMode",o),function(){r.unsubscribe("fullScreenMode",o)}}),[]),{enterFullScreen:function(){var e=r.get("getPagesContainer");if(e){var n=t(e());r.get("enterFullScreenMode")(n)}},exitFullScreen:function(){r.get("exitFullScreenMode")()},isFullScreen:c===e.FullScreenMode.Entering||c===e.FullScreenMode.EnteredCompletely}},s=function(e){var t=e.children,r=e.enableShortcuts,l=e.getFullScreenTarget,c=e.store,u=f(l,c),i=u.enterFullScreen,s=u.exitFullScreen,S=u.isFullScreen;return(t||function(e){return S?n.createElement(a,{onClick:e.onClick}):n.createElement(o,{enableShortcuts:r,onClick:e.onClick})})({onClick:S?s:i})},S=function(t){var r=t.onClick,c=n.useContext(e.LocalizationContext).l10n,u=c&&c.fullScreen?c.fullScreen.enterFullScreen:"Full screen";return n.createElement(e.MenuItem,{icon:n.createElement(l,null),isDisabled:!e.isFullScreenEnabled(),testId:"full-screen__enter-menu",onClick:r},u)},d=function(t){var l=t.onClick,c=n.useContext(e.LocalizationContext).l10n,u=n.useContext(e.ThemeContext).direction===e.TextDirection.RightToLeft,o=c&&c.fullScreen?c.fullScreen.exitFullScreen:"Exit full screen";return n.createElement("div",{className:e.classNames({"rpv-full-screen__exit-button":!0,"rpv-full-screen__exit-button--ltr":!u,"rpv-full-screen__exit-button--rtl":u})},n.createElement(e.MinimalButton,{ariaLabel:o,testId:"full-screen__exit-button",onClick:l},n.createElement(r,null)))},m=function(e){var t=e.children,r=e.getFullScreenTarget,l=e.store,c=f(r,l),u=c.enterFullScreen,o=c.exitFullScreen,i=c.isFullScreen;return i&&(t||function(e){return n.createElement(d,{onClick:e.onClick})})({onClick:i?o:u})},E=function(t){var r=t.store,l=t.onEnterFullScreen,c=t.onExitFullScreen,u=n.useState(r.get("fullScreenMode")),o=u[0],i=u[1],a=n.useCallback((function(e){i(e)}),[]);return n.useEffect((function(){switch(o){case e.FullScreenMode.EnteredCompletely:l(r.get("zoom"));break;case e.FullScreenMode.Exited:c(r.get("zoom"))}}),[o]),n.useEffect((function(){return r.subscribe("fullScreenMode",a),function(){r.unsubscribe("fullScreenMode",a)}}),[]),(o===e.FullScreenMode.Entering||o===e.FullScreenMode.Entered)&&n.createElement("div",{className:"rpv-full-screen__overlay"},n.createElement(e.Spinner,null))},F=function(t){var r=t.containerRef,l=t.getFullScreenTarget,c=t.store,u=f(l,c).enterFullScreen,o=function(t){if(!t.shiftKey&&!t.altKey&&(e.isMac()?t.metaKey&&t.ctrlKey&&"f"===t.key:"F11"===t.key)){var n=r.current;n&&document.activeElement&&n.contains(document.activeElement)&&(t.preventDefault(),u())}};return n.useEffect((function(){if(r.current)return document.addEventListener("keydown",o),function(){document.removeEventListener("keydown",o)}}),[r.current]),n.createElement(n.Fragment,null)};exports.ExitFullScreenIcon=r,exports.FullScreenIcon=l,exports.fullScreenPlugin=function(t){var r=(null==t?void 0:t.getFullScreenTarget)||function(e){return e},l=n.useMemo((function(){return Object.assign({},{enableShortcuts:!0,onEnterFullScreen:function(){},onExitFullScreen:function(){}},t)}),[]),u=n.useMemo((function(){return e.createStore({enterFullScreenMode:function(){},exitFullScreenMode:function(){},fullScreenMode:e.FullScreenMode.Normal,zoom:function(){}})}),[]),i=function(e){return n.createElement(s,c({},e,{enableShortcuts:l.enableShortcuts,getFullScreenTarget:r,store:u}))},a=function(){return n.createElement(m,{getFullScreenTarget:r,store:u},null==t?void 0:t.renderExitFullScreenButton)};return{install:function(e){u.update("enterFullScreenMode",e.enterFullScreenMode),u.update("exitFullScreenMode",e.exitFullScreenMode),u.update("getPagesContainer",e.getPagesContainer),u.update("zoom",e.zoom)},onViewerStateChange:function(e){return u.update("fullScreenMode",e.fullScreenMode),e},renderViewer:function(e){var t=e.slot;return t.subSlot&&(t.subSlot.children=n.createElement(n.Fragment,null,l.enableShortcuts&&n.createElement(F,{containerRef:e.containerRef,getFullScreenTarget:r,store:u}),n.createElement(E,{store:u,onEnterFullScreen:l.onEnterFullScreen,onExitFullScreen:l.onExitFullScreen}),n.createElement(a,null),t.subSlot.children)),t},EnterFullScreen:i,EnterFullScreenButton:function(){return n.createElement(i,null,(function(e){return n.createElement(o,c({enableShortcuts:l.enableShortcuts},e))}))},EnterFullScreenMenuItem:function(e){return n.createElement(i,null,(function(t){return n.createElement(S,{onClick:function(){t.onClick(),e.onClick()}})}))}}};
