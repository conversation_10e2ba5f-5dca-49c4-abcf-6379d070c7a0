import React, { useState, useRef } from 'react';
import { supabase } from './supabaseClient';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

function Submit() {
  // Get user from localStorage
  const user = JSON.parse(localStorage.getItem('user') || 'null');
  const [form, setForm] = useState({
    // username will be set from user
    title: '',
    authors: '',
    abstract: '',
    keywords: '',
    mainContent: '',
    conclusion: '',
    references: '',
    organization: '',
    country: '',
    journal: '',
  });
  const [step, setStep] = useState(1); // 1: form, 2: preview, 3: confirmation
  const [generatedPdf, setGeneratedPdf] = useState(null);
  const [pdfBlob, setPdfBlob] = useState(null);
  const [submitted, setSubmitted] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const pdfRef = useRef();

  const navigate = useNavigate();

  const handleChange = e => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  // Generate PDF from form data
  const generatePDF = async () => {
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 25.4; // 1 inch margin in mm
    const contentWidth = pageWidth - (2 * margin);
    let yPosition = margin;

    // Add journal logo/header (placeholder for now)
    pdf.setFontSize(10);
    pdf.text('Darsgah-e-Ahlebait Journal', pageWidth / 2, 15, { align: 'center' });
    yPosition += 20;

    // Title - Bold, 18pt, Centered
    pdf.setFont('times', 'bold');
    pdf.setFontSize(18);
    const titleLines = pdf.splitTextToSize(form.title, contentWidth);
    titleLines.forEach(line => {
      pdf.text(line, pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 7;
    });
    yPosition += 10;

    // Authors - Italic, 12pt, Centered
    pdf.setFont('times', 'italic');
    pdf.setFontSize(12);
    const authorLines = pdf.splitTextToSize(form.authors, contentWidth);
    authorLines.forEach(line => {
      pdf.text(line, pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 5;
    });
    yPosition += 15;

    // Abstract section
    pdf.setFont('times', 'bold');
    pdf.setFontSize(12);
    pdf.text('Abstract', margin, yPosition);
    yPosition += 8;

    pdf.setFont('times', 'normal');
    const abstractLines = pdf.splitTextToSize(form.abstract, contentWidth);
    abstractLines.forEach(line => {
      if (yPosition > pageHeight - margin) {
        pdf.addPage();
        yPosition = margin;
      }
      pdf.text(line, margin, yPosition, { align: 'justify' });
      yPosition += 5;
    });
    yPosition += 10;

    // Keywords section
    pdf.setFont('times', 'bold');
    pdf.text('Keywords', margin, yPosition);
    yPosition += 8;

    pdf.setFont('times', 'normal');
    const keywordLines = pdf.splitTextToSize(form.keywords, contentWidth);
    keywordLines.forEach(line => {
      if (yPosition > pageHeight - margin) {
        pdf.addPage();
        yPosition = margin;
      }
      pdf.text(line, margin, yPosition, { align: 'justify' });
      yPosition += 5;
    });
    yPosition += 15;

    // Main Content section with 1.5 line spacing
    pdf.setFont('times', 'bold');
    pdf.text('Main Content', margin, yPosition);
    yPosition += 8;

    pdf.setFont('times', 'normal');
    const contentLines = pdf.splitTextToSize(form.mainContent, contentWidth);
    contentLines.forEach(line => {
      if (yPosition > pageHeight - margin) {
        pdf.addPage();
        yPosition = margin;
      }
      pdf.text(line, margin, yPosition, { align: 'justify' });
      yPosition += 7.5; // 1.5 line spacing
    });
    yPosition += 15;

    // Conclusion section
    pdf.setFont('times', 'bold');
    pdf.text('Conclusion', margin, yPosition);
    yPosition += 8;

    pdf.setFont('times', 'normal');
    const conclusionLines = pdf.splitTextToSize(form.conclusion, contentWidth);
    conclusionLines.forEach(line => {
      if (yPosition > pageHeight - margin) {
        pdf.addPage();
        yPosition = margin;
      }
      pdf.text(line, margin, yPosition, { align: 'justify' });
      yPosition += 5;
    });
    yPosition += 15;

    // References section
    pdf.setFont('times', 'bold');
    pdf.text('References', margin, yPosition);
    yPosition += 8;

    pdf.setFont('times', 'normal');
    const refLines = form.references.split('\n');
    refLines.forEach((ref, index) => {
      if (ref.trim()) {
        if (yPosition > pageHeight - margin) {
          pdf.addPage();
          yPosition = margin;
        }
        pdf.text(`${index + 1}. ${ref}`, margin, yPosition);
        yPosition += 5;
      }
    });

    // Add page numbers
    const pageCount = pdf.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      pdf.setPage(i);
      pdf.setFontSize(10);
      pdf.text(`${i}`, pageWidth / 2, pageHeight - 10, { align: 'center' });
    }

    return pdf;
  };

  // Handle form submission to generate PDF preview
  const handleFormSubmit = async (e) => {
    e.preventDefault();
    if (!user) {
      setError('You must be logged in to submit a publication.');
      return;
    }

    // Validate required fields
    if (!form.title || !form.authors || !form.abstract || !form.mainContent) {
      setError('Please fill in all required fields.');
      return;
    }

    setError('');
    try {
      const pdf = await generatePDF();
      const pdfBlob = pdf.output('blob');
      setPdfBlob(pdfBlob);
      setGeneratedPdf(pdf.output('datauristring'));
      setStep(2); // Move to preview step
    } catch (err) {
      setError('Failed to generate PDF preview.');
      console.error('PDF generation error:', err);
    }
  };

  // Handle final submission
  const handleFinalSubmit = async () => {
    setSubmitting(true);
    setError('');

    try {
      // Generate filename
      const authorName = form.authors.split(',')[0].trim().replace(/\s+/g, '_');
      const titleShort = form.title.substring(0, 30).replace(/\s+/g, '_');
      const fileName = `${authorName}_${titleShort}_${Date.now()}.pdf`;

      // Upload PDF to Supabase Storage
      const { data, error: uploadError } = await supabase.storage
        .from('pdf')
        .upload(fileName, pdfBlob);

      if (uploadError) {
        setError('Failed to upload PDF.');
        setSubmitting(false);
        return;
      }

      // Get public URL
      const { data: urlData } = supabase.storage.from('pdf').getPublicUrl(fileName);
      const pdf_url = urlData.publicUrl;

      // Insert into request table
      const { error: insertError } = await supabase.from('request').insert([
        {
          ...form,
          username: user.username || user.full_name || user.email,
          email: user.email,
          pdf_url
        }
      ]);

      if (insertError) {
        setError('Failed to submit publication. ' + (insertError.message || ''));
        setSubmitting(false);
        return;
      }

      setSubmitted(true);
      setSubmitting(false);
      setStep(3); // Move to confirmation step
    } catch (err) {
      setError('Failed to submit publication.');
      setSubmitting(false);
      console.error('Submission error:', err);
    }
  };

  // Handle edit button
  const handleEdit = () => {
    setStep(1);
    setGeneratedPdf(null);
    setPdfBlob(null);
  };

  return (
    <div className="submit-page">
      <Helmet>
        <title>Submit - darsgah-e-ahlebait</title>
        <meta name="description" content="Submit your research paper to darsgah-e-ahlebait journals." />
      </Helmet>

      {/* Step 1: Form */}
      {step === 1 && (
        <div className="row justify-content-center">
          <div className="col-md-10">
            <div className="card shadow-sm border-0">
              <div className="card-body">
                <h2 className="mb-4 text-center">Submit a Publication</h2>
                {!user ? (
                  <div className="alert alert-danger text-center my-5">You must be logged in to submit a publication.</div>
                ) : (
                  <form onSubmit={handleFormSubmit}>
                    <div className="row">
                      <div className="col-md-6 mb-3">
                        <label className="form-label">Title</label>
                        <input type="text" className="form-control" name="title" value={form.title} onChange={handleChange} required />
                      </div>
                      <div className="col-md-6 mb-3">
                        <label className="form-label">Organization</label>
                        <input type="text" className="form-control" name="organization" value={form.organization} onChange={handleChange} />
                      </div>
                    </div>
                    <div className="row">
                      <div className="col-md-6 mb-3">
                        <label className="form-label">Country</label>
                        <input type="text" className="form-control" name="country" value={form.country} onChange={handleChange} />
                      </div>
                      <div className="col-md-6 mb-3">
                        <label className="form-label">Journal</label>
                        <select className="form-control" name="journal" value={form.journal} onChange={handleChange} required>
                          <option value="">Select Journal</option>
                          <option value="JBS">JBS</option>
                          <option value="JNS">JNS</option>
                        </select>
                      </div>
                    </div>
                    <div className="mb-3">
                      <label className="form-label">Abstract</label>
                      <textarea className="form-control" name="abstract" rows="3" value={form.abstract} onChange={handleChange} required></textarea>
                    </div>
                    <div className="row">
                      <div className="col-md-6 mb-3">
                        <label className="form-label">Authors</label>
                        <input type="text" className="form-control" name="authors" value={form.authors} onChange={handleChange} required />
                      </div>
                      <div className="col-md-6 mb-3">
                        <label className="form-label">Keywords *</label>
                        <input
                          type="text"
                          className="form-control"
                          name="keywords"
                          value={form.keywords}
                          onChange={handleChange}
                          placeholder="Separate with commas"
                          required
                        />
                      </div>
                    </div>
                    <div className="mb-3">
                      <label className="form-label">Main Content (support paragraphs and headers) *</label>
                      <textarea
                        className="form-control"
                        name="mainContent"
                        value={form.mainContent}
                        onChange={handleChange}
                        rows="10"
                        placeholder="Enter your main content here. Use line breaks for paragraphs."
                        required
                      ></textarea>
                    </div>

                    <div className="mb-3">
                      <label className="form-label">Conclusion *</label>
                      <textarea
                        className="form-control"
                        name="conclusion"
                        value={form.conclusion}
                        onChange={handleChange}
                        rows="4"
                        required
                      ></textarea>
                    </div>

                    <div className="mb-3">
                      <label className="form-label">References</label>
                      <textarea
                        className="form-control"
                        name="references"
                        value={form.references}
                        onChange={handleChange}
                        rows="6"
                        placeholder="Enter each reference on a new line"
                      ></textarea>
                    </div>
                    {submitting && <div className="alert alert-info text-center">Submitting...</div>}
                    {error && <div className="alert alert-danger text-center">{error}</div>}
                    <div className="d-grid">
                      <button type="submit" className="btn btn-primary btn-lg">
                        Generate PDF Preview
                      </button>
                    </div>
                  </form>
                )
              )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Step 2: PDF Preview */}
      {step === 2 && (
        <div className="row">
          <div className="col-md-6">
            <div className="card shadow-sm border-0">
              <div className="card-body">
                <h3 className="mb-4">Form Data</h3>
                <div className="mb-3">
                  <strong>Title:</strong> {form.title}
                </div>
                <div className="mb-3">
                  <strong>Authors: <AUTHORS>
                </div>
                <div className="mb-3">
                  <strong>Abstract:</strong> {form.abstract}
                </div>
                <div className="mb-3">
                  <strong>Keywords:</strong> {form.keywords}
                </div>
                <div className="mb-3">
                  <strong>Main Content:</strong>
                  <div style={{maxHeight: '200px', overflowY: 'auto', border: '1px solid #ddd', padding: '10px', marginTop: '5px'}}>
                    {form.mainContent}
                  </div>
                </div>
                <div className="mb-3">
                  <strong>Conclusion:</strong> {form.conclusion}
                </div>
                <div className="mb-3">
                  <strong>References:</strong>
                  <div style={{maxHeight: '150px', overflowY: 'auto', border: '1px solid #ddd', padding: '10px', marginTop: '5px'}}>
                    {form.references}
                  </div>
                </div>

                <div className="d-flex gap-2">
                  <button className="btn btn-secondary" onClick={handleEdit}>
                    Edit
                  </button>
                  <button
                    className="btn btn-success"
                    onClick={handleFinalSubmit}
                    disabled={submitting}
                  >
                    {submitting ? 'Submitting...' : 'Confirm & Submit'}
                  </button>
                </div>
                {error && <div className="alert alert-danger mt-3">{error}</div>}
              </div>
            </div>
          </div>

          <div className="col-md-6">
            <div className="card shadow-sm border-0">
              <div className="card-body">
                <h3 className="mb-4">PDF Preview</h3>
                {generatedPdf && (
                  <iframe
                    src={generatedPdf}
                    width="100%"
                    height="600px"
                    style={{border: '1px solid #ddd'}}
                    title="PDF Preview"
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Step 3: Confirmation */}
      {step === 3 && submitted && (
        <div className="row justify-content-center">
          <div className="col-md-8">
            <div className="card shadow-sm border-0">
              <div className="card-body text-center">
                <h2 className="text-success mb-4">Submission Successful!</h2>
                <p className="lead">Your publication has been submitted successfully.</p>
                <p>You will receive a confirmation email shortly.</p>
                <button
                  className="btn btn-primary"
                  onClick={() => navigate('/profile/submissions')}
                >
                  View My Submissions
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Submit; 