import React, { useState } from 'react';
import { supabase } from './supabaseClient';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet';

function Submit() {
  // Get user from localStorage
  const user = JSON.parse(localStorage.getItem('user') || 'null');
  const [form, setForm] = useState({
    // username will be set from user
    title: '',
    organization: '',
    country: '',
    abstract: '',
    authors: '',
    journal: '',
    keywords: '',
  });
  const [pdfFile, setPdfFile] = useState(null);
  const [submitted, setSubmitted] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');

  const navigate = useNavigate();

  const handleChange = e => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleFileChange = e => {
    setPdfFile(e.target.files[0]);
  };

  const handleSubmit = async e => {
    e.preventDefault();
    setSubmitting(true);
    setError('');
    let pdf_url = '';
    if (!pdfFile) {
      setError('Please upload a PDF file.');
      setSubmitting(false);
      return;
    }
    if (!user) {
      setError('You must be logged in to submit a publication.');
      setSubmitting(false);
      return;
    }
    // Upload PDF to Supabase Storage
    const fileExt = pdfFile.name.split('.').pop();
    const fileName = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExt}`;
    const { data, error: uploadError } = await supabase.storage.from('pdf').upload(fileName, pdfFile);
    if (uploadError) {
      setError('Failed to upload PDF.');
      setSubmitting(false);
      return;
    }
    // Get public URL
    const { data: urlData } = supabase.storage.from('pdf').getPublicUrl(fileName);
    pdf_url = urlData.publicUrl;
    // Insert into request table
    const { error: insertError } = await supabase.from('request').insert([
      { ...form, username: user.username || user.full_name || user.email, email: user.email, pdf_url }
    ]);
    if (insertError) {
      setError('Failed to submit publication. ' + (insertError.message || ''));
      setSubmitting(false);
      return;
    }
    setSubmitted(true);
    setSubmitting(false);
  };

  return (
    <div className="submit-page">
      <Helmet>
        <title>Submit - darsgah-e-ahlebait</title>
        <meta name="description" content="Submit your research paper to darsgah-e-ahlebait journals." />
      </Helmet>
      <div className="row justify-content-center">
        <div className="col-md-9">
          <div className="card shadow-sm border-0">
            <div className="card-body">
              <h2 className="mb-4 text-center">Submit a Publication</h2>
              {submitted ? (
                <div className="alert alert-success text-center">Your submission has been received!</div>
              ) : (
                !user ? (
                  <div className="alert alert-danger text-center my-5">You must be logged in to submit a publication.</div>
                ) : (
                  <form onSubmit={handleSubmit}>
                    <div className="row">
                      <div className="col-md-6 mb-3">
                        <label className="form-label">Title</label>
                        <input type="text" className="form-control" name="title" value={form.title} onChange={handleChange} required />
                      </div>
                      <div className="col-md-6 mb-3">
                        <label className="form-label">Organization</label>
                        <input type="text" className="form-control" name="organization" value={form.organization} onChange={handleChange} />
                      </div>
                    </div>
                    <div className="row">
                      <div className="col-md-6 mb-3">
                        <label className="form-label">Country</label>
                        <input type="text" className="form-control" name="country" value={form.country} onChange={handleChange} />
                      </div>
                      <div className="col-md-6 mb-3">
                        <label className="form-label">Journal</label>
                        <select className="form-control" name="journal" value={form.journal} onChange={handleChange} required>
                          <option value="">Select Journal</option>
                          <option value="JBS">JBS</option>
                          <option value="JNS">JNS</option>
                        </select>
                      </div>
                    </div>
                    <div className="mb-3">
                      <label className="form-label">Abstract</label>
                      <textarea className="form-control" name="abstract" rows="3" value={form.abstract} onChange={handleChange} required></textarea>
                    </div>
                    <div className="row">
                      <div className="col-md-6 mb-3">
                        <label className="form-label">Authors</label>
                        <input type="text" className="form-control" name="authors" value={form.authors} onChange={handleChange} required />
                      </div>
                      <div className="col-md-6 mb-3">
                        <label className="form-label">Keywords</label>
                        <input type="text" className="form-control" name="keywords" value={form.keywords} onChange={handleChange} />
                      </div>
                    </div>
                    <div className="mb-3">
                      <label className="form-label">PDF File</label>
                      <input type="file" className="form-control" accept="application/pdf" onChange={handleFileChange} required />
                    </div>
                    {submitting && <div className="alert alert-info text-center">Submitting...</div>}
                    {error && <div className="alert alert-danger text-center">{error}</div>}
                    <div className="d-grid">
                      <button type="submit" className="btn btn-success">Submit Publication</button>
                    </div>
                  </form>
                )
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Submit; 