{"version": 3, "file": "createProjectProgram.js", "sourceRoot": "", "sources": ["../../src/create-program/createProjectProgram.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAC1B,gDAAwB;AACxB,+CAAiC;AAEjC,8CAA6C;AAE7C,yDAAsD;AACtD,+EAA4E;AAE5E,qCAA6C;AAE7C,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,0DAA0D,CAAC,CAAC;AAE9E,MAAM,6BAA6B,GAAG;IACpC,EAAE,CAAC,SAAS,CAAC,EAAE;IACf,EAAE,CAAC,SAAS,CAAC,GAAG;IAChB,EAAE,CAAC,SAAS,CAAC,EAAE;IACf,EAAE,CAAC,SAAS,CAAC,GAAG;IAChB,EAAE,CAAC,SAAS,CAAC,GAAG;IAChB,EAAE,CAAC,SAAS,CAAC,GAAG;IAChB,EAAE,CAAC,SAAS,CAAC,GAAG;IAChB,EAAE,CAAC,SAAS,CAAC,GAAG;CACI,CAAC;AAEvB;;;GAGG;AACH,SAAS,oBAAoB,CAC3B,aAA4B;IAE5B,GAAG,CAAC,kCAAkC,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;IAEhE,MAAM,mBAAmB,GAAG,IAAA,yDAA2B,EAAC,aAAa,CAAC,CAAC;IACvE,MAAM,aAAa,GAAG,IAAA,yBAAY,EAAC,mBAAmB,EAAE,cAAc,CAAC,EAAE,CACvE,IAAA,0BAAiB,EAAC,cAAc,EAAE,aAAa,CAAC,CACjD,CAAC;IAEF,0FAA0F;IAC1F,IAAI,aAAa,IAAI,aAAa,CAAC,oBAAoB,EAAE;QACvD,OAAO,aAAa,CAAC;KACtB;IAED,MAAM,uBAAuB,GAAG,CAAC,WAAmB,EAAU,EAAE,CAC9D,IAAA,mCAAgB,EAAC,WAAW,EAAE,aAAa,CAAC,eAAe,CAAC,CAAC;IAE/D,MAAM,iBAAiB,GAAG,IAAA,mCAAgB,EACxC,aAAa,CAAC,QAAQ,EACtB,aAAa,CAAC,eAAe,CAC9B,CAAC;IACF,MAAM,gBAAgB,GAAG,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IAC7E,MAAM,iBAAiB,GACrB,gBAAgB,CAAC,MAAM,KAAK,CAAC;QAC3B,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,KAAK,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACxE,MAAM,UAAU,GAAG;QACjB,qCAAqC,iBAAiB,uCAAuC,iBAAiB,EAAE;KACjH,CAAC;IACF,IAAI,iBAAiB,GAAG,KAAK,CAAC;IAE9B,MAAM,mBAAmB,GAAG,aAAa,CAAC,mBAAmB,IAAI,EAAE,CAAC;IAEpE,mBAAmB,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;QAC3C,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACnC,UAAU,CAAC,IAAI,CACb,gCAAgC,cAAc,uFAAuF,cAAc,KAAK,CACzJ,CAAC;SACH;QACD,IAAI,6BAA6B,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;YAC1D,UAAU,CAAC,IAAI,CACb,8CAA8C,cAAc,uHAAuH,CACpL,CAAC;SACH;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,cAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAC3D,IAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;QAC1D,MAAM,cAAc,GAAG,iCAAiC,aAAa,qBAAqB,CAAC;QAC3F,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;YAClC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;gBAChD,UAAU,CAAC,IAAI,CACb,GAAG,cAAc,8EAA8E,CAChG,CAAC;gBACF,iBAAiB,GAAG,IAAI,CAAC;aAC1B;SACF;aAAM;YACL,UAAU,CAAC,IAAI,CACb,GAAG,cAAc,wEAAwE,CAC1F,CAAC;YACF,iBAAiB,GAAG,IAAI,CAAC;SAC1B;KACF;IAED,IAAI,CAAC,iBAAiB,EAAE;QACtB,MAAM,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,GAC9C,aAAa,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC;YACjC,CAAC,CAAC,CAAC,wBAAwB,EAAE,eAAe,CAAC;YAC7C,CAAC,CAAC,CAAC,yBAAyB,EAAE,wBAAwB,CAAC,CAAC;QAC5D,UAAU,CAAC,IAAI,CACb,YAAY,mBAAmB,6BAA6B,EAC5D,mEAAmE,EACnE,YAAY,mBAAmB,uBAAuB,EACtD,8FAA8F,EAC9F,oOAAoO,CACrO,CAAC;KACH;IAED,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACzC,CAAC;AAEQ,oDAAoB"}