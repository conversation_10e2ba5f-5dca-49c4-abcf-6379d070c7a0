"use strict";var e=require("@react-pdf-viewer/core");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var r=t(require("react")),n=function(){return r.createElement(e.Icon,{size:16},r.createElement("path",{d:"M12,1.001c6.075,0,11,4.925,11,11s-4.925,11-11,11s-11-4.925-11-11S5.925,1.001,12,1.001z\n            M14.5,17.005H13\n            c-0.552,0-1-0.448-1-1v-6.5c0-0.276-0.224-0.5-0.5-0.5H10\n            M11.745,6.504L11.745,6.504\n            M11.745,6.5c-0.138,0-0.25,0.112-0.25,0.25\n            S11.607,7,11.745,7s0.25-0.112,0.25-0.25S11.883,6.5,11.745,6.5"}))},o=function(){return o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},o.apply(this,arguments)},a=function(t){var n=t.doc,o=t.render,a=r.useState(),i=a[0],l=a[1];return r.useEffect((function(){n.getMetadata().then((function(e){return Promise.resolve(e)})).then((function(e){return n.getDownloadInfo().then((function(t){return Promise.resolve({fileName:e.contentDispositionFilename||"",info:e.info,length:t.length})}))})).then((function(e){l(e)}))}),[]),i?o(i):r.createElement("div",{className:"rpv-properties__loader"},r.createElement(e.Spinner,null))},i=function(t){var n=t.label,o=t.value,a=r.useContext(e.ThemeContext).direction===e.TextDirection.RightToLeft;return r.createElement("dl",{className:e.classNames({"rpv-properties__item":!0,"rpv-properties__item--rtl":a})},r.createElement("dt",{className:"rpv-properties__item-label"},n,":"),r.createElement("dd",{className:"rpv-properties__item-value"},o||"-"))},l=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"),c=function(e,t,r,n){var o=parseInt(e,10);return o>=t&&o<=r?o:n},p=function(t){var n=t.doc,o=t.fileName,p=t.onToggle,s=r.useContext(e.LocalizationContext).l10n,u=function(e){var t=function(e){var t=l.exec(e);if(!t)return null;var r=parseInt(t[1],10),n=c(t[2],1,12,1)-1,o=c(t[3],1,31,1),a=c(t[4],0,23,0),i=c(t[5],0,59,0),p=c(t[6],0,59,0),s=t[7]||"Z",u=c(t[8],0,23,0),m=c(t[9],0,59,0);switch(s){case"-":a+=u,i+=m;break;case"+":a-=u,i-=m}return new Date(Date.UTC(r,n,o,a,i,p))}(e);return t?"".concat(t.toLocaleDateString(),", ").concat(t.toLocaleTimeString()):""};return r.createElement("div",{className:"rpv-properties__modal"},r.createElement(a,{doc:n,render:function(t){return r.createElement(r.Fragment,null,r.createElement("div",{className:"rpv-properties__modal-section"},r.createElement(i,{label:s&&s.properties?s.properties.fileName:"File name",value:t.fileName||(c=o,p=c.split("/").pop(),p?p.split("#")[0].split("?")[0]:c)}),r.createElement(i,{label:s&&s.properties?s.properties.fileSize:"File size",value:(a=t.length,l=Math.floor(Math.log(a)/Math.log(1024)),"".concat((a/Math.pow(1024,l)).toFixed(2)," ").concat(["B","kB","MB","GB","TB"][l]))})),r.createElement(e.Separator,null),r.createElement("div",{className:"rpv-properties__modal-section"},r.createElement(i,{label:s&&s.properties?s.properties.title:"Title",value:t.info.Title}),r.createElement(i,{label:s&&s.properties?s.properties.author:"Author",value:t.info.Author}),r.createElement(i,{label:s&&s.properties?s.properties.subject:"Subject",value:t.info.Subject}),r.createElement(i,{label:s&&s.properties?s.properties.keywords:"Keywords",value:t.info.Keywords}),r.createElement(i,{label:s&&s.properties?s.properties.creator:"Creator",value:t.info.Creator}),r.createElement(i,{label:s&&s.properties?s.properties.creationDate:"Creation date",value:u(t.info.CreationDate)}),r.createElement(i,{label:s&&s.properties?s.properties.modificationDate:"Modification date",value:u(t.info.ModDate)})),r.createElement(e.Separator,null),r.createElement("div",{className:"rpv-properties__modal-section"},r.createElement(i,{label:s&&s.properties?s.properties.pdfProducer:"PDF producer",value:t.info.Producer}),r.createElement(i,{label:s&&s.properties?s.properties.pdfVersion:"PDF version",value:t.info.PDFFormatVersion}),r.createElement(i,{label:s&&s.properties?s.properties.pageCount:"Page count",value:"".concat(n.numPages)})));var a,l,c,p}}),r.createElement("div",{className:"rpv-properties__modal-footer"},r.createElement(e.Button,{onClick:p},s&&s.properties?s.properties.close:"Close")))},s={left:0,top:8},u=function(t){var o=t.onClick,a=r.useContext(e.LocalizationContext).l10n,i=a&&a.properties?a.properties.showProperties:"Show properties";return r.createElement(e.Tooltip,{ariaControlsSuffix:"properties",position:e.Position.BottomCenter,target:r.createElement(e.MinimalButton,{ariaLabel:i,testId:"properties__button",onClick:o},r.createElement(n,null)),content:function(){return i},offset:s})},m=function(t){var n=t.children,a=t.store,i=function(e){var t=r.useState(e.get("doc")),n=t[0],o=t[1],a=function(e){o(e)};return r.useEffect((function(){return e.subscribe("doc",a),function(){e.unsubscribe("doc",a)}}),[]),{currentDoc:n}}(a).currentDoc,l=a.get("fileName")||"",c=n||function(e){return r.createElement(u,o({},e))};return i?r.createElement(e.Modal,{ariaControlsSuffix:"properties",target:function(e){return c({onClick:e})},content:function(e){return r.createElement(p,{doc:i,fileName:l,onToggle:e})},closeOnClickOutside:!0,closeOnEscape:!0}):r.createElement(r.Fragment,null)},f=function(t){var o=t.onClick,a=r.useContext(e.LocalizationContext).l10n,i=a&&a.properties?a.properties.showProperties:"Show properties";return r.createElement(e.MenuItem,{icon:r.createElement(n,null),testId:"properties__menu",onClick:o},i)};exports.InfoIcon=n,exports.propertiesPlugin=function(){var t=r.useMemo((function(){return e.createStore({fileName:""})}),[]),n=function(e){return r.createElement(m,o({},e,{store:t}))};return{onDocumentLoad:function(e){t.update("doc",e.doc)},onViewerStateChange:function(e){return t.update("fileName",e.file.name),e},ShowProperties:n,ShowPropertiesButton:function(){return r.createElement(m,{store:t})},ShowPropertiesMenuItem:function(e){return r.createElement(n,null,(function(e){return r.createElement(f,o({},e))}))}}};
