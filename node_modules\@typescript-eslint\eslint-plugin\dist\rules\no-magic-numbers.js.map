{"version": 3, "file": "no-magic-numbers.js", "sourceRoot": "", "sources": ["../../src/rules/no-magic-numbers.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAChC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,kBAAkB,CAAC,CAAC;AAKvD,iFAAiF;AACjF,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS;AAC3B,yHAAyH;AACzH,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;IACjC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EACxB;IACE,UAAU,EAAE;QACV,yBAAyB,EAAE;YACzB,IAAI,EAAE,SAAS;SAChB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,SAAS;SAChB;QACD,6BAA6B,EAAE;YAC7B,IAAI,EAAE,SAAS;SAChB;QACD,iBAAiB,EAAE;YACjB,IAAI,EAAE,SAAS;SAChB;KACF;CACF,CACF,CAAC;AAEF,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,wBAAwB;YACrC,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,MAAM,EAAE,CAAC,MAAM,CAAC;QAChB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE;QACd;YACE,MAAM,EAAE,EAAE;YACV,kBAAkB,EAAE,KAAK;YACzB,YAAY,EAAE,KAAK;YACnB,aAAa,EAAE,KAAK;YACpB,yBAAyB,EAAE,KAAK;YAChC,WAAW,EAAE,KAAK;YAClB,6BAA6B,EAAE,KAAK;SACrC;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC,OAAO;YACL,OAAO,CAAC,IAAI;;gBACV,qDAAqD;gBACrD,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;oBACpE,OAAO;iBACR;gBAED,wEAAwE;gBACxE,wEAAwE;gBACxE,wEAAwE;gBACxE,wEAAwE;gBACxE,IAAI,SAA8B,CAAC;gBAEnC,qDAAqD;gBACrD,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE;oBACnC,SAAS,GAAG,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC;iBAC1C;gBACD,sDAAsD;qBACjD,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;oBACrC,SAAS,GAAG,OAAO,CAAC,yBAAyB,KAAK,IAAI,CAAC;iBACxD;gBACD,oCAAoC;qBAC/B,IAAI,6BAA6B,CAAC,IAAI,CAAC,EAAE;oBAC5C,SAAS,GAAG,OAAO,CAAC,iBAAiB,KAAK,IAAI,CAAC;iBAChD;gBACD,iDAAiD;qBAC5C,IAAI,oCAAoC,CAAC,IAAI,CAAC,EAAE;oBACnD,SAAS,GAAG,OAAO,CAAC,6BAA6B,KAAK,IAAI,CAAC;iBAC5D;gBAED,wEAAwE;gBACxE,IAAI,SAAS,KAAK,IAAI,EAAE;oBACtB,OAAO;iBACR;gBACD,yDAAyD;qBACpD,IAAI,SAAS,KAAK,KAAK,EAAE;oBAC5B,IAAI,cAAc,GAChB,IAAI,CAAC;oBACP,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;oBAEnB,IACE,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,eAAe;wBACpD,6DAA6D;wBAC7D,oHAAoH;wBACpH,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,GAAG,EAC5B;wBACA,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC;wBAC7B,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;qBAC5C;oBAED,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,SAAS;wBACpB,IAAI,EAAE,cAAc;wBACpB,IAAI,EAAE,EAAE,GAAG,EAAE;qBACd,CAAC,CAAC;oBAEH,OAAO;iBACR;gBAED,uCAAuC;gBACvC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH;;GAEG;AACH,SAAS,gBAAgB,CAAC,IAAsB;;IAC9C,IACE,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,eAAe;QACpD,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EACzC;QACA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;KAC3B;IAED,OAAO,IAAI,CAAC,MAAM,CAAC;AACrB,CAAC;AAED;;;;;GAKG;AACH,SAAS,mCAAmC,CAAC,IAAmB;;IAC9D,OAAO,CAAA,MAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,sBAAsB,CAAC;AAC7E,CAAC;AAED;;;;;GAKG;AACH,SAAS,wBAAwB,CAAC,IAAmB;;IACnD,IAAI,CAAA,MAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,WAAW,EAAE;QAC5D,OAAO,mCAAmC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACzD;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;GAKG;AACH,SAAS,yBAAyB,CAAC,IAAsB;IACvD,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACtC,OAAO,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,sBAAc,CAAC,YAAY,CAAC;AACtD,CAAC;AAED;;;;;GAKG;AACH,SAAS,qBAAqB,CAAC,IAAmB;;IAChD,OAAO,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,aAAa,CAAC;AAC5D,CAAC;AAED;;;;;GAKG;AACH,SAAS,sBAAsB,CAAC,IAAmB;;IACjD,4CAA4C;IAC5C,IACE,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,eAAe;QACpD,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,GAAG,EAC5B;QACA,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;KACpB;IAED,0DAA0D;IAC1D,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE;QAChC,OAAO,KAAK,CAAC;KACd;IAED,yDAAyD;IACzD,IAAI,mCAAmC,CAAC,IAAI,CAAC,EAAE;QAC7C,OAAO,IAAI,CAAC;KACb;IAED,0FAA0F;IAC1F,IAAI,wBAAwB,CAAC,IAAI,CAAC,EAAE;QAClC,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;GAKG;AACH,SAAS,oCAAoC,CAAC,IAAsB;IAClE,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAEtC,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,sBAAc,CAAC,kBAAkB,IAAI,MAAM,CAAC,QAAQ,EAAE;QACzE,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;GAKG;AACH,SAAS,6BAA6B,CAAC,IAAsB;;IAC3D,oCAAoC;IACpC,IAAI,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAEtC,sEAAsE;IACtE,2BAA2B;IAC3B,OACE,CAAA,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,WAAW;QACrD,CAAA,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,kBAAkB,EAC5D;QACA,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;KAC5B;IAED,OAAO,CAAA,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,mBAAmB,CAAC;AACvE,CAAC"}