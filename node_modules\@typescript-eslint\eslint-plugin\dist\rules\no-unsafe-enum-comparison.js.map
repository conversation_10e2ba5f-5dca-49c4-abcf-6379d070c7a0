{"version": 3, "file": "no-unsafe-enum-comparison.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-enum-comparison.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AAChC,gDAAmD;AAEnD;;GAEG;AACH,SAAS,YAAY,CAAC,aAAwB,EAAE,KAAc;IAC5D,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAEpE,OAAO,CACL,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;QACtC,OAAO,CAAC,aAAa,CACnB,KAAK,EACL,EAAE,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU,CAC9C,CAAC;QACJ,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;YACtC,OAAO,CAAC,aAAa,CACnB,KAAK,EACL,EAAE,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU,CAC9C,CAAC,CACL,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,IAAa;IACrC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC;QACpD,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC;YACpD,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM;YACrB,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM;QACvB,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC;AAED,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,2BAA2B;IACjC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,wDAAwD;YACrE,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,UAAU,EACR,mEAAmE;SACtE;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAE5D,SAAS,eAAe,CAAC,IAAmB;YAC1C,OAAO,WAAW,CAAC,iBAAiB,CAClC,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAC/C,CAAC;QACJ,CAAC;QAED,OAAO;YACL,oCAAoC,CAClC,IAA+B;gBAE/B,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxC,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE1C,+DAA+D;gBAC/D,EAAE;gBACF,QAAQ;gBACR,WAAW;gBACX,MAAM;gBACN,MAAM,aAAa,GAAG,IAAA,qBAAY,EAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBACtD,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,IAAA,qBAAY,EAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;gBACjE,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,IAAI,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE;oBAC3D,OAAO;iBACR;gBAED,6CAA6C;gBAC7C,EAAE;gBACF,QAAQ;gBACR,gCAAgC;gBAChC,MAAM;gBACN,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;oBACxC,IAAI,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;wBACpC,OAAO;qBACR;iBACF;gBAED,MAAM,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBACnD,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAErD,oEAAoE;gBACpE,EAAE;gBACF,QAAQ;gBACR,wCAAwC;gBACxC,eAAe;gBACf,MAAM;gBACN,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;oBACxC,IAAI,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;wBACzC,OAAO;qBACR;iBACF;gBAED,IACE,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC;oBAClC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,EAClC;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,YAAY;wBACvB,IAAI;qBACL,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}