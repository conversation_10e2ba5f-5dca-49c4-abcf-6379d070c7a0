{"version": 3, "file": "promise-function-async.js", "sourceRoot": "", "sources": ["../../src/rules/promise-function-async.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA2E;AAC3E,+CAAiC;AAEjC,8CAAgC;AAchC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,MAAM;QACf,IAAI,EAAE;YACJ,WAAW,EACT,0EAA0E;YAC5E,WAAW,EAAE,KAAK;YAClB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,YAAY,EAAE,+CAA+C;SAC9D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,QAAQ,EAAE;wBACR,WAAW,EACT,yDAAyD;wBAC3D,IAAI,EAAE,SAAS;qBAChB;oBACD,mBAAmB,EAAE;wBACnB,WAAW,EACT,qEAAqE;wBACvE,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;oBACD,mBAAmB,EAAE;wBACnB,IAAI,EAAE,SAAS;qBAChB;oBACD,yBAAyB,EAAE;wBACzB,IAAI,EAAE,SAAS;qBAChB;oBACD,wBAAwB,EAAE;wBACxB,IAAI,EAAE,SAAS;qBAChB;oBACD,uBAAuB,EAAE;wBACvB,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,QAAQ,EAAE,IAAI;YACd,mBAAmB,EAAE,EAAE;YACvB,mBAAmB,EAAE,IAAI;YACzB,yBAAyB,EAAE,IAAI;YAC/B,wBAAwB,EAAE,IAAI;YAC9B,uBAAuB,EAAE,IAAI;SAC9B;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,QAAQ,EACR,mBAAmB,EACnB,mBAAmB,EACnB,yBAAyB,EACzB,wBAAwB,EACxB,uBAAuB,GACxB,EACF;QAED,MAAM,sBAAsB,GAAG,IAAI,GAAG,CAAC;YACrC,SAAS;YACT,GAAG,mBAAoB;SACxB,CAAC,CAAC;QACH,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,SAAS,YAAY,CACnB,IAG+B;;YAE/B,MAAM,YAAY,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACpE,MAAM,UAAU,GAAG,OAAO;iBACvB,iBAAiB,CAAC,YAAY,CAAC;iBAC/B,iBAAiB,EAAE,CAAC;YACvB,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;gBACtB,OAAO;aACR;YACD,MAAM,UAAU,GAAG,OAAO,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YAEnE,IACE,CAAC,IAAI,CAAC,sBAAsB,CAC1B,UAAU,EACV,QAAS,EACT,sBAAsB;YACtB,qIAAqI;YACrI,IAAI,CAAC,UAAU,IAAI,IAAI,CACxB,EACD;gBACA,+BAA+B;gBAC/B,OAAO;aACR;YAED,IAAI,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,0BAA0B,EAAE;gBACnE,iCAAiC;gBACjC,OAAO;aACR;YAED,IACE,IAAI,CAAC,MAAM;gBACX,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;oBAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,CAAC;gBACvD,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,EAC1D;gBACA,qCAAqC;gBACrC,OAAO;aACR;YAED,IACE,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,EACvE;gBACA,+DAA+D;gBAC/D,OAAO,OAAO,CAAC,MAAM,CAAC;oBACpB,SAAS,EAAE,cAAc;oBACzB,IAAI;oBACJ,GAAG,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,UAAU,CAAC;iBAC/C,CAAC,CAAC;aACJ;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,SAAS,EAAE,cAAc;gBACzB,IAAI;gBACJ,GAAG,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,UAAU,CAAC;gBAC9C,GAAG,EAAE,KAAK,CAAC,EAAE;oBACX,IACE,IAAI,CAAC,MAAM;wBACX,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;4BACnD,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;gCAC3C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EACxB;wBACA,wEAAwE;wBACxE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;wBAE3B,kCAAkC;wBAClC,IAAI,QAAQ,GAAG,UAAU,CAAC,aAAa,CAAC,MAAM,CAAE,CAAC;wBAEjD,8CAA8C;wBAC9C,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;4BAC/C,MAAM,CAAC,UAAU,EACjB;4BACA,MAAM,aAAa,GACjB,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;4BAClD,QAAQ,GAAG,UAAU,CAAC,aAAa,CAAC,aAAa,CAAE,CAAC;yBACrD;wBAED,uEAAuE;wBACvE,OACE,QAAQ,CAAC,IAAI,KAAK,uBAAe,CAAC,OAAO;4BACzC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EACvC;4BACA,QAAQ,GAAG,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAE,CAAC;yBAChD;wBAED,2DAA2D;wBAC3D,MAAM,WAAW,GAAG,CAAC,UAAU,CAAC,cAAe,CAC7C,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAE,EACpC,QAAQ,CACT,CAAC;wBAEF,IAAI,IAAI,GAAG,QAAQ,CAAC;wBACpB,IAAI,WAAW,EAAE;4BACf,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;yBACnB;wBACD,OAAO,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;qBAC/C;oBAED,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAChD,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,qDACK,CAAC,mBAAmB,IAAI;YACzB,wCAAwC,CACtC,IAAsC;gBAEtC,YAAY,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC;SACF,CAAC,GACC,CAAC,yBAAyB,IAAI;YAC/B,oCAAoC,CAClC,IAAkC;gBAElC,YAAY,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC;SACF,CAAC,KACF,mCAAmC,CACjC,IAAiC;gBAEjC,IACE,IAAI,CAAC,MAAM;oBACX,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACpD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,EAC7B;oBACA,IAAI,uBAAuB,EAAE;wBAC3B,YAAY,CAAC,IAAI,CAAC,CAAC;qBACpB;oBACD,OAAO;iBACR;gBACD,IAAI,wBAAwB,EAAE;oBAC5B,YAAY,CAAC,IAAI,CAAC,CAAC;iBACpB;YACH,CAAC,IACD;IACJ,CAAC;CACF,CAAC,CAAC"}