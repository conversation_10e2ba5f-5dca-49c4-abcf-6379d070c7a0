"use strict";var e=require("@react-pdf-viewer/core");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var o=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,o.get?o:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var n=t(require("react")),o=function(){return n.createElement(e.Icon,{ignoreDirection:!0,size:16},n.createElement("path",{d:"M10.5,0.499c5.523,0,10,4.477,10,10s-4.477,10-10,10s-10-4.477-10-10S4.977,0.499,10.5,0.499z\n            M23.5,23.499\n            l-5.929-5.929\n            M5.5,10.499h10\n            M10.5,5.499v10"}))},r=function(){return n.createElement(e.Icon,{ignoreDirection:!0,size:16},n.createElement("path",{d:"M10.5,0.499c5.523,0,10,4.477,10,10s-4.477,10-10,10s-10-4.477-10-10S4.977,0.499,10.5,0.499z\n            M23.5,23.499\n            l-5.929-5.929\n            M5.5,10.499h10"}))},c=function(){return c=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},c.apply(this,arguments)},a=function(e){var t=n.useState(e.get("scale")||0),o=t[0],r=t[1],c=function(e){r(e)};return n.useEffect((function(){return e.subscribe("scale",c),function(){e.unsubscribe("scale",c)}}),[]),{scale:o}},u=function(e){var t=e.children,o=e.store;return(t||function(e){return n.createElement(n.Fragment,null,"".concat(Math.round(100*e.scale),"%"))})({scale:a(o).scale})},l={passive:!1},i=null,s=function(t){var o=t.pagesContainerRef,r=t.store,c=e.useDebounceCallback((function(e){var t=r.get("zoom");t&&t(e)}),40),a=function(e){if(e.ctrlKey){e.preventDefault();var t=e.target.getBoundingClientRect(),n=1-e.deltaY/100,o=e.clientX-t.left,a=e.clientY-t.top,u=r.get("scale"),l=(i||(i=document.createElementNS("http://www.w3.org/2000/svg","svg"))).createSVGMatrix().translate(o,a).scale(n).translate(-o,-a).scale(u);c(l.a)}};return e.useIsomorphicLayoutEffect((function(){var e=o.current;if(e)return e.addEventListener("wheel",a,l),function(){e.removeEventListener("wheel",a)}}),[]),n.createElement(n.Fragment,null)},m=[.1,.2,.3,.4,.5,.6,.7,.8,.9,1,1.1,1.3,1.5,1.7,1.9,2.1,2.4,2.7,3,3.3,3.7,4.1,4.6,5.1,5.7,6.3,7,7.7,8.5,9.4,10],f=function(e){return m.find((function(t){return t>e}))||e},v=function(e){var t=m.findIndex((function(t){return t>=e}));return-1===t||0===t?e:m[t-1]},p=function(t){var o=t.containerRef,r=t.store,c=function(t){if(!t.shiftKey&&!t.altKey&&(e.isMac()?t.metaKey:t.ctrlKey)){var n=o.current;if(n&&document.activeElement&&n.contains(document.activeElement)){var c=r.get("zoom");if(c){var a=r.get("scale")||1,u=1;switch(t.key){case"-":u=v(a);break;case"=":u=f(a);break;case"0":u=1;break;default:u=a}u!==a&&(t.preventDefault(),c(u))}}}};return n.useEffect((function(){if(o.current)return document.addEventListener("keydown",c),function(){document.removeEventListener("keydown",c)}}),[o.current]),n.createElement(n.Fragment,null)},z=[.5,.75,1,1.25,1.5,2,3,4],E={left:0,top:8},d=function(t){var o=t.levels,r=void 0===o?z:o,c=t.scale,a=t.onZoom,u=n.useContext(e.LocalizationContext).l10n,l=n.useContext(e.ThemeContext).direction===e.TextDirection.RightToLeft,i=u&&u.zoom?u.zoom.zoomDocument:"Zoom document";return n.createElement(e.Popover,{ariaControlsSuffix:"zoom",ariaHasPopup:"menu",position:e.Position.BottomCenter,target:function(t){return n.createElement(e.MinimalButton,{ariaLabel:i,testId:"zoom__popover-target",onClick:function(){t()}},n.createElement("span",{className:"rpv-zoom__popover-target"},n.createElement("span",{"data-testid":"zoom__popover-target-scale",className:e.classNames({"rpv-zoom__popover-target-scale":!0,"rpv-zoom__popover-target-scale--ltr":!l,"rpv-zoom__popover-target-scale--rtl":l})},Math.round(100*c),"%"),n.createElement("span",{className:"rpv-zoom__popover-target-arrow"})))},content:function(t){return n.createElement(e.Menu,null,Object.keys(e.SpecialZoomLevel).map((function(o){var r=o;return n.createElement(e.MenuItem,{key:r,onClick:function(){t(),a(r)}},function(t){switch(t){case e.SpecialZoomLevel.ActualSize:return u&&u.zoom?u.zoom.actualSize:"Actual size";case e.SpecialZoomLevel.PageFit:return u&&u.zoom?u.zoom.pageFit:"Page fit";case e.SpecialZoomLevel.PageWidth:return u&&u.zoom?u.zoom.pageWidth:"Page width"}}(r))})),n.createElement(e.MenuDivider,null),r.map((function(o){return n.createElement(e.MenuItem,{key:o,onClick:function(){t(),a(o)}},"".concat(Math.round(100*o),"%"))})))},offset:E,closeOnClickOutside:!0,closeOnEscape:!0})},g=function(e){var t=e.children,o=e.levels,r=e.store;return(t||function(e){return n.createElement(d,{levels:o,scale:e.scale,onZoom:e.onZoom})})({scale:a(r).scale,onZoom:function(e){var t=r.get("zoom");t&&t(e)}})},C={left:0,top:8},h=function(t){var r=t.enableShortcuts,c=t.onClick,a=n.useContext(e.LocalizationContext).l10n,u=a&&a.zoom?a.zoom.zoomIn:"Zoom in",l=r?e.isMac()?"Meta+=":"Ctrl+=":"";return n.createElement(e.Tooltip,{ariaControlsSuffix:"zoom-in",position:e.Position.BottomCenter,target:n.createElement(e.MinimalButton,{ariaKeyShortcuts:l,ariaLabel:u,testId:"zoom__in-button",onClick:c},n.createElement(o,null)),content:function(){return u},offset:C})},b=function(e){var t=e.children,n=e.enableShortcuts,o=e.store,r=a(o).scale;return(t||h)({enableShortcuts:n,onClick:function(){var e=o.get("zoom");e&&e(f(r))}})},k=function(t){var r=t.onClick,c=n.useContext(e.LocalizationContext).l10n,a=c&&c.zoom?c.zoom.zoomIn:"Zoom in";return n.createElement(e.MenuItem,{icon:n.createElement(o,null),testId:"zoom__in-menu",onClick:r},a)},S={left:0,top:8},M=function(t){var o=t.enableShortcuts,c=t.onClick,a=n.useContext(e.LocalizationContext).l10n,u=a&&a.zoom?a.zoom.zoomOut:"Zoom out",l=o?e.isMac()?"Meta+-":"Ctrl+-":"";return n.createElement(e.Tooltip,{ariaControlsSuffix:"zoom-out",position:e.Position.BottomCenter,target:n.createElement(e.MinimalButton,{ariaKeyShortcuts:l,ariaLabel:u,testId:"zoom__out-button",onClick:c},n.createElement(r,null)),content:function(){return u},offset:S})},I=function(e){var t=e.children,n=e.enableShortcuts,o=e.store,r=a(o).scale;return(t||M)({enableShortcuts:n,onClick:function(){var e=o.get("zoom");e&&e(v(r))}})},Z=function(t){var o=t.onClick,c=n.useContext(e.LocalizationContext).l10n,a=c&&c.zoom?c.zoom.zoomOut:"Zoom out";return n.createElement(e.MenuItem,{icon:n.createElement(r,null),testId:"zoom__out-menu",onClick:o},a)};exports.ZoomInIcon=o,exports.ZoomOutIcon=r,exports.zoomPlugin=function(t){var o=n.useMemo((function(){return Object.assign({},{enableShortcuts:!0},t)}),[]),r=n.useMemo((function(){return e.createStore({})}),[]),a=function(e){return n.createElement(b,c({enableShortcuts:o.enableShortcuts},e,{store:r}))},l=function(e){return n.createElement(I,c({enableShortcuts:o.enableShortcuts},e,{store:r}))},i=function(e){return n.createElement(g,c({},e,{store:r}))};return{renderViewer:function(e){var t=e.slot;if(!o.enableShortcuts)return t;var a={children:n.createElement(n.Fragment,null,n.createElement(p,{containerRef:e.containerRef,store:r}),n.createElement(s,{pagesContainerRef:e.pagesContainerRef,store:r}),t.children)};return c(c({},t),a)},install:function(e){r.update("zoom",e.zoom)},onViewerStateChange:function(e){return r.update("scale",e.scale),e},zoomTo:function(e){var t=r.get("zoom");t&&t(e)},CurrentScale:function(e){return n.createElement(u,c({},e,{store:r}))},ZoomIn:a,ZoomInButton:function(){return n.createElement(a,null,(function(e){return n.createElement(h,c({},e))}))},ZoomInMenuItem:function(e){return n.createElement(a,null,(function(t){return n.createElement(k,{onClick:function(){t.onClick(),e.onClick()}})}))},ZoomOut:l,ZoomOutButton:function(){return n.createElement(l,null,(function(e){return n.createElement(M,c({},e))}))},ZoomOutMenuItem:function(e){return n.createElement(l,null,(function(t){return n.createElement(Z,{onClick:function(){t.onClick(),e.onClick()}})}))},Zoom:i,ZoomPopover:function(e){return n.createElement(i,null,(function(t){return n.createElement(d,c({levels:null==e?void 0:e.levels},t))}))}}};
