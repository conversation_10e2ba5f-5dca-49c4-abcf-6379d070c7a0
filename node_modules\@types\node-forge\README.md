# Installation
> `npm install --save @types/node-forge`

# Summary
This package contains type definitions for node-forge (https://github.com/digitalbazaar/forge).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-forge.

### Additional Details
 * Last updated: Fri, 11 Jul 2025 22:02:30 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [<PERSON>      ](https://github.com/westy92), [<PERSON>       ](https://github.com/flynetworks), [<PERSON><PERSON><PERSON>      ](https://github.com/a-k-g), [<PERSON><PERSON>l2228          ](https://github.com/rafal2228), [<PERSON><PERSON>         ](https://github.com/beenotung), [<PERSON>        ](https://github.com/joeflateau), [timhwang21         ](https://github.com/timhwang21), [<PERSON>     ](https://github.com/andersk), [<PERSON><PERSON><PERSON>    ](https://github.com/saschazar21), [<PERSON><PERSON><PERSON>    ](https://github.com/rogierschouten), [Ivan Aseev         ](https://github.com/aseevia), [Wiktor Kwapisiewicz](https://github.com/wiktor-k), [Ligia Frangello    ](https://github.com/frangello), [Dmitry Avezov      ](https://github.com/avezov), [Jose Fuentes       ](https://github.com/j-fuentes), [Anya Reyes         ](https://github.com/darkade), and [BendingBender      ](https://github.com/BendingBender).
