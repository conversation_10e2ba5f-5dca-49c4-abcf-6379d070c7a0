"use strict";var e=require("@react-pdf-viewer/core"),t=require("react"),n=require("react-dom");function r(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var o,a=r(t),i=function(){return a.createElement(e.Icon,{size:16},a.createElement("path",{d:"M7.5,19.499h9 M7.5,16.499h9 M5.5,16.5h-3c-1.103-0.003-1.997-0.897-2-2v-6c0.003-1.103,0.897-1.997,2-2h19\n            c1.103,0.003,1.997,0.897,2,2v6c-0.003,1.103-0.897,1.997-2,2h-3\n            M5.5,4.5v-4h9.586c0.265,0,0.52,0.105,0.707,0.293l2.414,2.414\n            C18.395,3.394,18.5,3.649,18.5,3.914V4.5\n            M18.5,22.5c0,0.552-0.448,1-1,1h-11c-0.552,0-1-0.448-1-1v-9h13V22.5z\n            M3.5,8.499\n            c0.552,0,1,0.448,1,1s-0.448,1-1,1s-1-0.448-1-1S2.948,8.499,3.5,8.499z\n            M14.5,0.499v4h4"}))},c=function(){return c=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},c.apply(this,arguments)},u={left:0,top:8},s=function(t){var n=t.enableShortcuts,r=t.onClick,o=a.useContext(e.LocalizationContext).l10n,c=o&&o.print?o.print.print:"Print",s=n?e.isMac()?"Meta+P":"Ctrl+P":"";return a.createElement(e.Tooltip,{ariaControlsSuffix:"print",position:e.Position.BottomCenter,target:a.createElement(e.MinimalButton,{ariaKeyShortcuts:s,ariaLabel:c,testId:"print__button",onClick:r},a.createElement(i,null)),content:function(){return c},offset:u})};!function(e){e.CheckingPermission="CheckingPermission",e.Inactive="Inactive",e.Preparing="Preparing",e.Cancelled="Cancelled",e.Ready="Ready"}(o||(o={}));var l=function(e){var t=e.children,n=e.enableShortcuts,r=e.store;return(t||s)({enableShortcuts:n,onClick:function(){r.update("printStatus",o.CheckingPermission)}})},p=function(t){var n=t.doc,r=t.store,i=a.useContext(e.LocalizationContext).l10n,c=a.useState(!0),u=c[0],s=c[1];return a.useEffect((function(){n.getPermissions().then((function(e){null===e||e.includes(4)||e.includes(2048)?r.update("printStatus",o.Preparing):s(!1)}))}),[]),u?a.createElement(a.Fragment,null):a.createElement(e.Modal,{ariaControlsSuffix:"print-permission",closeOnClickOutside:!1,closeOnEscape:!1,content:function(t){return a.createElement(a.Fragment,null,a.createElement("div",{className:"rpv-print__permission-body"},i&&i.print?i.print.disallowPrint:"The document does not allow to print"),a.createElement("div",{className:"rpv-print__permission-footer"},a.createElement(e.Button,{onClick:function(){t(),r.update("printStatus",o.Cancelled)}},i&&i.print?i.print.close:"Close")))},isOpened:!0})},m=function(t){var n=t.numLoadedPages,r=t.numPages,o=t.onCancel,i=a.useContext(e.LocalizationContext).l10n,c=a.useContext(e.ThemeContext).direction===e.TextDirection.RightToLeft,u=Math.floor(100*n/r);return a.createElement("div",{className:"rpv-print__progress"},a.createElement("div",{className:e.classNames({"rpv-print__progress-body":!0,"rpv-print__progress-body--rtl":c})},a.createElement("div",{className:"rpv-print__progress-message"},i&&i.print?i.print.preparingDocument:"Preparing document ..."),a.createElement("div",{className:"rpv-print__progress-bar"},a.createElement(e.ProgressBar,{progress:u})),a.createElement(e.Button,{onClick:o},i&&i.print?i.print.cancel:"Cancel")))},d=function(t){var n=t.canvas,r=t.page,o=t.pageHeight,i=t.pageIndex,c=t.pageWidth,u=t.rotation,s=t.onLoad,l=e.useIsMounted(),p=a.useRef(),m=a.useState(""),d=m[0],f=m[1],g=a.useMemo((function(){return"undefined"!=typeof process&&void 0!==process.env.JEST_WORKER_ID}),[]);return a.useEffect((function(){var e=p.current;e&&e.cancel();var t=150/72;n.height=Math.floor(o*t),n.width=Math.floor(c*t);var a=n.getContext("2d");a.save(),a.fillStyle="rgb(255, 255, 255)",a.fillRect(0,0,n.width,n.height),a.restore();var i=r.getViewport({rotation:u,scale:1});p.current=r.render({canvasContext:a,intent:"print",transform:[t,0,0,t,0,0],viewport:i}),p.current.promise.then((function(){"toBlob"in n&&"createObjectURL"in URL?n.toBlob((function(e){l.current&&f(URL.createObjectURL(e)),g&&s()})):(l.current&&f(n.toDataURL()),g&&s())}),(function(){}))}),[]),d&&a.createElement("div",{className:"rpv-print__page"},a.createElement("img",{"data-testid":"print__thumbnail-".concat(i),src:d,onLoad:function(){g||s()}}))},f=function(t){var n=t.canvas,r=t.doc,o=t.pageIndex,i=t.pageRotation,c=t.pageSize,u=t.rotation,s=t.shouldRender,l=t.onLoad,p=e.useIsMounted(),m=a.useState(null),f=m[0],g=m[1],v=Math.abs(u+i)%180==0;a.useEffect((function(){s&&e.getPage(r,o).then((function(e){p.current&&g(e)}))}),[s]);var h=(c.rotation+u+i)%360;return f&&a.createElement(d,{canvas:n,page:f,pageHeight:v?c.pageHeight:c.pageWidth,pageIndex:o,pageWidth:v?c.pageWidth:c.pageHeight,rotation:h,onLoad:l})},g=function(e){var t=e.doc,r=e.numLoadedPages,i=e.pagesRotation,c=e.pageSizes,u=e.printPages,s=e.printStatus,l=e.rotation,p=e.onCancel,m=e.onLoad,d=a.useMemo((function(){return document.createElement("canvas")}),[]),g=a.useMemo((function(){var e=document.querySelector(".rpv-print__zone");if(e)return e;var t=document.createElement("div");return t.classList.add("rpv-print__zone"),t.setAttribute("data-testid","print__zone"),document.body.appendChild(t),t}),[]);a.useEffect((function(){s===o.Ready&&(document.documentElement.classList.add("rpv-print__html-printing"),document.body.classList.add("rpv-print__body-printing"),window.print());var e=function(){if(s===o.Ready){document.documentElement.classList.remove("rpv-print__html-printing"),document.body.classList.remove("rpv-print__body-printing");var t=document.querySelectorAll(".rpv-print__zone");t&&t.forEach((function(e){e.parentElement.removeChild(e)})),d.height=0,d.width=0,document.removeEventListener("mousemove",e),p()}};return document.addEventListener("mousemove",e),function(){return document.removeEventListener("mousemove",e)}}),[s]);var v=c[0].pageHeight,h=c[0].pageWidth;return n.createPortal(a.createElement(a.Fragment,null,u.map((function(e,n){return a.createElement(f,{key:e,canvas:d,doc:t,pageIndex:e,pageRotation:i.has(e)?i.get(e):0,pageSize:c[e],rotation:l,shouldRender:n===r,onLoad:m})})),a.createElement("style",{dangerouslySetInnerHTML:{__html:"@page { size: ".concat(h,"pt ").concat(v,"pt }")}})),g)},v=function(e){var t=e.doc,n=e.pagesRotation,r=e.pageSizes,i=e.renderProgressBar,c=e.rotation,u=e.setPages,s=e.store,l=a.useState(o.Inactive),d=l[0],f=l[1],v=a.useState(0),h=v[0],E=v[1],P=a.useMemo((function(){var e=t.numPages;return u(t).filter((function(t){return t>=0&&t<e}))}),[t,u]),C=P.length,b=function(){E(0),f(o.Inactive)},_=function(e){return f(e)};return a.useEffect((function(){return s.subscribe("printStatus",_),function(){s.unsubscribe("printStatus",_)}}),[]),a.createElement(a.Fragment,null,d===o.CheckingPermission&&a.createElement(p,{doc:t,store:s}),d===o.Preparing&&(i?i(h,C,b):a.createElement(m,{numLoadedPages:h,numPages:C,onCancel:b})),(d===o.Preparing||d===o.Ready)&&h<=C&&a.createElement(g,{doc:t,numLoadedPages:h,pagesRotation:n,pageSizes:r,printPages:P,printStatus:d,rotation:c,onCancel:b,onLoad:function(){var e=h+1;e<=C&&(E(e),e===C&&f(o.Ready))}}))},h=function(t){var n=t.onClick,r=a.useContext(e.LocalizationContext).l10n,o=r&&r.print?r.print.print:"Print";return a.createElement(e.MenuItem,{icon:a.createElement(i,null),testId:"print__menu",onClick:n},o)},E=function(t){var n=t.containerRef,r=t.store,i=function(t){if(!t.shiftKey&&!t.altKey&&"p"===t.key&&(e.isMac()?t.metaKey:t.ctrlKey)){var a=n.current;a&&document.activeElement&&a.contains(document.activeElement)&&(t.preventDefault(),r.update("printStatus",o.Preparing))}};return a.useEffect((function(){if(n.current)return document.addEventListener("keydown",i),function(){document.removeEventListener("keydown",i)}}),[n.current]),a.createElement(a.Fragment,null)};exports.PrintIcon=i,exports.getAllPagesNumbers=function(e){return Array(e.numPages).fill(0).map((function(e,t){return t}))},exports.getCustomPagesNumbers=function(e){return function(t){var n,r=[];return e.replace(/\s+/g,"").split(",").forEach((function(e){var t,n,o=e.split("-").map((function(e){return parseInt(e,10)})).filter((function(e){return Number.isInteger(e)}));1===o.length?r.push(o[0]-1):2===o.length&&r.push.apply(r,(t=o[0]-1,n=o[1]-1,Array(n-t+1).fill(0).map((function(e,n){return t+n}))))})),(n=r,n.filter((function(e){return n.indexOf(e)===n.lastIndexOf(e)}))).filter((function(e){return e>=0&&e<t.numPages}))}},exports.getEvenPagesNumbers=function(e){return Array(e.numPages).fill(0).map((function(e,t){return t})).filter((function(e){return(e+1)%2==0}))},exports.getOddPagesNumbers=function(e){return Array(e.numPages).fill(0).map((function(e,t){return t})).filter((function(e){return(e+1)%2==1}))},exports.printPlugin=function(t){var n=a.useMemo((function(){return Object.assign({},{enableShortcuts:!0,setPages:function(e){return Array(e.numPages).fill(0).map((function(e,t){return t}))}},t)}),[]),r=a.useMemo((function(){return e.createStore({printStatus:o.Inactive})}),[]),i=function(e){return a.createElement(l,c({enableShortcuts:n.enableShortcuts},e,{store:r}))};return{print:function(){r.update("printStatus",o.CheckingPermission)},renderViewer:function(e){var o=e.slot,i={children:a.createElement(a.Fragment,null,n.enableShortcuts&&a.createElement(E,{containerRef:e.containerRef,store:r}),a.createElement(v,{doc:e.doc,pagesRotation:e.pagesRotation,pageSizes:e.pageSizes,renderProgressBar:null==t?void 0:t.renderProgressBar,rotation:e.rotation,setPages:n.setPages,store:r}),o.children)};return c(c({},o),i)},Print:i,PrintButton:function(){return a.createElement(i,null,(function(e){return a.createElement(s,c({},e))}))},PrintMenuItem:function(e){return a.createElement(i,null,(function(t){return a.createElement(h,{onClick:function(){t.onClick(),e.onClick()}})}))},setPages:function(e){n.setPages=e}}};
