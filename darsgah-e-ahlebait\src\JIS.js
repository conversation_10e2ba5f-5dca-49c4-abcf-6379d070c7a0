import React, { useEffect, useState } from 'react';
import { supabase } from './supabaseClient';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { FaGlobe, FaSearch, FaCalendarAlt, FaUser, FaBookOpen, FaHashtag, FaListUl, FaUsers, FaArrowRight } from 'react-icons/fa';
import { getJournalStats, formatImpactFactor } from './utils/impactFactor';

function JIS() {
  const [publications, setPublications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [yearFilter, setYearFilter] = useState('');
  const [authorFilter, setAuthorFilter] = useState('');
const [keywordFilter, setKeywordFilter] = useState('');
const [volumeFilter, setVolumeFilter] = useState('');
const [issueFilter, setIssueFilter] = useState('');
  const [years, setYears] = useState([]);
const [authors, setAuthors] = useState([]);
const [keywords, setKeywords] = useState([]);
const [volumes, setVolumes] = useState([]);
const [issues, setIssues] = useState([]);
const [journalStats, setJournalStats] = useState(null);

  useEffect(() => {
    async function fetchPublications() {
      setLoading(true);
      let query = supabase
        .from('publications')
        .select('id, title, authors, year, volume, issue_number, citation_count, external_citation_count, keywords, abstract, featured')
        .eq('journal', 'JIS')
        .order('created_at', { ascending: false });
      const { data, error } = await query;

      // Debug: Check what we actually got from database
      console.log('🔍 JIS Database Query Result:');
      console.log('   Error:', error);
      console.log('   Data:', data);
      console.log('   Data length:', data ? data.length : 0);

      if (!error && data) {
        setPublications(data);

        // Calculate journal statistics (now async to fetch external citations)
        const stats = await getJournalStats(data, 'JIS', supabase);
        console.log('📊 Final stats for JIS:', stats);
        setJournalStats(stats);

        // Temporary test: Set hardcoded stats to verify display works
        if (stats.totalCitations === 0) {
          console.log('⚠️ JIS Stats showing 0, setting test data...');
          setJournalStats({
            ...stats,
            totalCitations: 0, // From your database test (JIS has 0 citations)
            totalPublications: 2
          });
        }
        
        // Extract unique years, authors, keywords, volumes, issues for filters
        setYears(Array.from(new Set(data.map(pub => pub.year))).sort((a, b) => b - a));
        setAuthors(Array.from(new Set(data.flatMap(pub => pub.authors ? pub.authors.split(',').map(a => a.trim()) : []))).sort());
        setKeywords(Array.from(new Set(data.flatMap(pub => pub.keywords ? pub.keywords.split(',').map(k => k.trim()) : []))).sort());
        setVolumes(Array.from(new Set(data.map(pub => pub.volume).filter(Boolean))).sort((a, b) => b - a));
        setIssues(Array.from(new Set(data.map(pub => pub.issue_number).filter(Boolean))).sort((a, b) => b - a));
      }
      setLoading(false);
    }
    fetchPublications();
  }, []);

  // Filter publications
  const filtered = publications.filter(pub => {
    const matchesYear = !yearFilter || pub.year === Number(yearFilter);
    const matchesSearch =
      search === '' ||
      pub.title.toLowerCase().includes(search.toLowerCase()) ||
      (pub.authors && pub.authors.toLowerCase().includes(search.toLowerCase()));
    const matchesAuthor = !authorFilter || (pub.authors && pub.authors.split(',').map(a => a.trim()).includes(authorFilter));
    const matchesKeyword = !keywordFilter || (pub.keywords && pub.keywords.split(',').map(k => k.trim()).includes(keywordFilter));
    const matchesVolume = !volumeFilter || pub.volume === volumeFilter;
    const matchesIssue = !issueFilter || pub.issue_number === issueFilter;
    return matchesYear && matchesSearch && matchesAuthor && matchesKeyword && matchesVolume && matchesIssue;
  });

  // Separate featured and recent publications
  const featured = filtered.filter(pub => pub.featured).slice(0, 6);
  const recent = filtered.slice(0, 6);

  return (
    <div className="journal-page bg-light min-vh-100">
      <Helmet>
        <title>JIS - darsgah-e-ahlebait</title>
        <meta name="description" content="Journal of Islam and Science (JIS) - A leading journal exploring the intersection of Islamic thought and scientific research." />
      </Helmet>
      {/* Hero Section */}
      <section className="jis-hero-section position-relative mb-5" style={{width: '100vw', position: 'relative', left: '50%', right: '50%', marginLeft: '-50vw', marginRight: '-50vw', background: 'linear-gradient(120deg, #0b3d2e 0%, #388e3c 100%)', color: '#fff', minHeight: '370px', padding: '56px 0'}}>
        <div className="d-flex flex-column flex-md-row align-items-center justify-content-between gap-4 px-4 px-md-5" style={{maxWidth: '1400px', margin: '0 auto'}}>
            <div className="flex-fill">
              <h1 className="display-4 fw-bold mb-2" style={{color: '#fff'}}>Journal of Islam and Science (JIS)</h1>
              <div className="mb-2 d-flex flex-wrap gap-3 align-items-center">
                <span className="badge bg-light text-success fs-6">No ISSN</span>
                <span className="badge bg-light text-success fs-6">
                  Citations: {journalStats ? journalStats.totalCitations : 'Calculating...'}
                </span>
              </div>
              <p className="lead mb-2" style={{color: '#fff'}}>A unique journal exploring the intersection of Islam and science, publishing high-quality, peer-reviewed articles at the crossroads of faith and scientific inquiry.</p>
              <div className="mb-2" style={{color: '#fff'}}>
                <b>Aims & Scope:</b> JIS aims to foster dialogue and research at the interface of Islamic thought and scientific disciplines, including philosophy, ethics, and contemporary science.
              </div>
              <div className="d-flex flex-wrap gap-2 mt-3">
                <Link to="/editorial-board/jis" className="btn btn-outline-success rounded-pill px-4"><FaUsers className="me-2" />Editorial Board</Link>
                <a href="#submission-guidelines" className="btn btn-outline-info rounded-pill px-4"><FaListUl className="me-2" />Submission Guidelines</a>
                <a href="#contact" className="btn btn-outline-secondary rounded-pill px-4"><FaArrowRight className="me-2" />Contact</a>
              </div>
            </div>
            <div className="d-none d-md-block flex-shrink-0">
              <img src="https://cdn-icons-png.flaticon.com/512/2721/2721297.png" alt="Islam and Science" style={{width: '160px', opacity: 0.95}} />
            </div>
          </div>
        </section>
      {/* Featured Publications */}
      <section className="container mb-5">
        <h3 className="fw-bold mb-3 text-success">Featured Publications</h3>
        {loading ? (
          <div className="text-center my-4">
            <div className="spinner-border text-success" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : featured.length === 0 ? (
          <div className="alert alert-info text-center">No featured publications found for JIS.</div>
        ) : (
          <div className="row g-4 mb-4">
            {featured.map(pub => (
              <div className="col-12 col-md-6 col-lg-4" key={pub.id}>
                <div className="card h-100 border-0 rounded-5 pub-card-featured position-relative bg-white" style={{boxShadow: '0 8px 32px rgba(25,135,84,0.15)', background: 'linear-gradient(120deg, #e0ffe0 0%, #eaffea 100%)', border: '2.5px solid #198754', minHeight: '340px'}}>
                  <span className="badge bg-success fs-6 shadow-lg position-absolute" style={{top: '0', right: '0', zIndex: 2, borderRadius: '1rem 1rem 1rem 0', padding: '0.6em 1.2em', transform: 'translateY(-50%) translateX(-20%)'}}>★ Featured</span>
                  <div className="card-body pt-5 p-4 d-flex flex-column justify-content-between">
          <div>
                      <h5 className="fw-bold mb-2 text-success" style={{fontSize: '1.25rem'}}>
                        <Link to={`/publication/${pub.id}`} className="text-decoration-none text-success">{pub.title}</Link>
                      </h5>
                      <div className="mb-2 text-secondary" style={{fontSize: '1.05rem'}}><FaUser className="me-2 text-success" />{pub.authors}</div>
                      <div className="mb-2 d-flex flex-wrap gap-2 align-items-center">
                        <span className="badge bg-success bg-opacity-10 text-success fw-normal"><FaCalendarAlt className="me-1" />{pub.year}</span>
                        {pub.volume && <span className="badge bg-info bg-opacity-10 text-info fw-normal"><FaBookOpen className="me-1" />Vol. {pub.volume}</span>}
                        {pub.issue_number && <span className="badge bg-primary bg-opacity-10 text-primary fw-normal">Issue {pub.issue_number}</span>}
                        {pub.citation_count !== undefined && <span className="badge bg-warning bg-opacity-10 text-warning fw-normal"><FaHashtag className="me-1" />{pub.citation_count} Citations</span>}
                      </div>
                      {pub.keywords && <div className="mb-2"><b>Keywords:</b> <span className="text-success">{pub.keywords}</span></div>}
                      {pub.abstract && <div className="mb-2"><b>Abstract:</b> <span className="text-muted">{pub.abstract.slice(0, 120)}{pub.abstract.length > 120 ? '...' : ''}</span></div>}
                    </div>
                    <div className="mt-2">
                      <Link to={`/publication/${pub.id}`} className="btn btn-success btn-lg rounded-pill px-4">View Details</Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        <style>{`
          .pub-card-featured {
            transition: box-shadow 0.22s, transform 0.22s;
          }
          .pub-card-featured:hover {
            box-shadow: 0 12px 40px rgba(25,135,84,0.22);
            transform: translateY(-6px) scale(1.035);
          }
        `}</style>
      </section>
      {/* Recent Publications */}
      <section className="container mb-5">
        <h3 className="fw-bold mb-3 text-success">Recent Publications</h3>
        {loading ? (
          <div className="text-center my-4">
            <div className="spinner-border text-success" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : recent.length === 0 ? (
          <div className="alert alert-info text-center">No recent publications found for JIS.</div>
        ) : (
          <div className="row g-4 mb-4">
            {recent.map(pub => (
              <div className="col-12 col-md-6 col-lg-4" key={pub.id}>
                <div className="card h-100 border-success border-2 rounded-4 pub-card-recent position-relative bg-white" style={{boxShadow: '0 4px 18px rgba(25,135,84,0.10)', border: '2px solid #198754', minHeight: '320px'}}>
                  <span className="badge bg-success fs-6 shadow position-absolute" style={{top: '0', left: '0', zIndex: 2, borderRadius: '1rem 1rem 0 1rem', padding: '0.5em 1.1em', transform: 'translateY(-50%) translateX(20%)'}}>New</span>
                  <div className="card-body pt-5 p-4 d-flex flex-column justify-content-between">
                    <div>
                      <h5 className="fw-bold mb-2 text-success" style={{fontSize: '1.13rem'}}>
                        <Link to={`/publication/${pub.id}`} className="text-decoration-none text-success">{pub.title}</Link>
                      </h5>
                      <div className="mb-2 text-secondary" style={{fontSize: '1.01rem'}}><FaUser className="me-2 text-success" />{pub.authors}</div>
                      <div className="mb-2 d-flex flex-wrap gap-2 align-items-center">
                        <span className="badge bg-success bg-opacity-10 text-success fw-normal"><FaCalendarAlt className="me-1" />{pub.year}</span>
                        {pub.volume && <span className="badge bg-info bg-opacity-10 text-info fw-normal"><FaBookOpen className="me-1" />Vol. {pub.volume}</span>}
                        {pub.issue_number && <span className="badge bg-primary bg-opacity-10 text-primary fw-normal">Issue {pub.issue_number}</span>}
                        {pub.citation_count !== undefined && <span className="badge bg-warning bg-opacity-10 text-warning fw-normal"><FaHashtag className="me-1" />{pub.citation_count} Citations</span>}
                      </div>
                      {pub.keywords && <div className="mb-2"><b>Keywords:</b> <span className="text-success">{pub.keywords}</span></div>}
                      {pub.abstract && <div className="mb-2"><b>Abstract:</b> <span className="text-muted">{pub.abstract.slice(0, 120)}{pub.abstract.length > 120 ? '...' : ''}</span></div>}
                    </div>
                    <div className="mt-2">
                      <Link to={`/publication/${pub.id}`} className="btn btn-outline-success btn-sm rounded-pill">View Details</Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        <style>{`
          .pub-card-recent {
            transition: box-shadow 0.18s, transform 0.18s;
          }
          .pub-card-recent:hover {
            box-shadow: 0 8px 32px rgba(25,135,84,0.18);
            transform: translateY(-4px) scale(1.025);
          }
        `}</style>
      </section>
      {/* Filters */}
      <section className="container mb-4">
        <div className="row g-3 align-items-center justify-content-between mb-2">
          <div className="col-12 col-md-4 mb-2 mb-md-0">
            <div className="input-group">
              <span className="input-group-text bg-white border-end-0"><FaSearch className="text-secondary" /></span>
              <input
                type="text"
                className="form-control border-start-0"
                placeholder="Search by title or author..."
                value={search}
                onChange={e => setSearch(e.target.value)}
              />
            </div>
          </div>
          <div className="col-6 col-md-2">
            <select className="form-select" value={yearFilter} onChange={e => setYearFilter(e.target.value)}>
              <option value="">All Years</option>
              {years.map(y => (
                <option key={y} value={y}>{y}</option>
              ))}
            </select>
          </div>
          <div className="col-6 col-md-2">
            <select className="form-select" value={authorFilter} onChange={e => setAuthorFilter(e.target.value)}>
              <option value="">All Authors</option>
              {authors.map(a => (
                <option key={a} value={a}>{a}</option>
              ))}
            </select>
          </div>
          <div className="col-6 col-md-2">
            <select className="form-select" value={keywordFilter} onChange={e => setKeywordFilter(e.target.value)}>
              <option value="">All Keywords</option>
              {keywords.map(k => (
                <option key={k} value={k}>{k}</option>
              ))}
            </select>
          </div>
          <div className="col-6 col-md-1">
            <select className="form-select" value={volumeFilter} onChange={e => setVolumeFilter(e.target.value)}>
              <option value="">Volume</option>
              {volumes.map(v => (
                <option key={v} value={v}>{v}</option>
              ))}
            </select>
          </div>
          <div className="col-6 col-md-1">
            <select className="form-select" value={issueFilter} onChange={e => setIssueFilter(e.target.value)}>
              <option value="">Issue</option>
              {issues.map(i => (
                <option key={i} value={i}>{i}</option>
              ))}
            </select>
          </div>
        </div>
        <div className="row">
          <div className="col-12 text-end">
            <span className="badge bg-success bg-opacity-10 text-success fs-6">{filtered.length} Result{filtered.length !== 1 ? 's' : ''}</span>
          </div>
        </div>
      </section>
      {/* All Publications */}
      <section className="container mb-5">
        <h3 className="fw-bold mb-3 text-success">All Publications</h3>
        {loading ? (
          <div className="text-center my-4">
            <div className="spinner-border text-success" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : filtered.length === 0 ? (
          <div className="alert alert-info text-center">No publications found for JIS.</div>
        ) : (
          <div className="row g-4 mb-4">
            {filtered.map(pub => (
              <div className="col-12 col-md-6 col-lg-4" key={pub.id}>
                <div className="card h-100 shadow-sm border-0 rounded-4 pub-card position-relative bg-white">
                  <div className="card-body p-4 d-flex flex-column justify-content-between">
                    <div>
                      <h5 className="fw-bold mb-2 text-success" style={{fontSize: '1.18rem'}}>
                        <Link to={`/publication/${pub.id}`} className="text-decoration-none text-success">{pub.title}</Link>
                      </h5>
                      <div className="mb-2 text-secondary" style={{fontSize: '0.98rem'}}><FaUser className="me-2 text-success" />{pub.authors}</div>
                      <div className="mb-2 d-flex flex-wrap gap-2 align-items-center">
                        <span className="badge bg-success bg-opacity-10 text-success fw-normal"><FaCalendarAlt className="me-1" />{pub.year}</span>
                        {pub.volume && <span className="badge bg-info bg-opacity-10 text-info fw-normal"><FaBookOpen className="me-1" />Vol. {pub.volume}</span>}
                        {pub.issue_number && <span className="badge bg-primary bg-opacity-10 text-primary fw-normal">Issue {pub.issue_number}</span>}
                        {pub.citation_count !== undefined && <span className="badge bg-warning bg-opacity-10 text-warning fw-normal"><FaHashtag className="me-1" />{pub.citation_count} Citations</span>}
                      </div>
                      {pub.keywords && <div className="mb-2"><b>Keywords:</b> <span className="text-success">{pub.keywords}</span></div>}
                      {pub.abstract && <div className="mb-2"><b>Abstract:</b> <span className="text-muted">{pub.abstract.slice(0, 120)}{pub.abstract.length > 120 ? '...' : ''}</span></div>}
                    </div>
                    <div className="mt-2">
                      <Link to={`/publication/${pub.id}`} className="btn btn-outline-success btn-sm rounded-pill">View Details</Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </section>
    </div>
  );
}

export default JIS; 