// Impact Factor Calculator for Journals
// This calculates a provisional impact factor based on citation data

export const calculateImpactFactor = (publications) => {
  // Filter publications for the specific journal
  if (publications.length === 0) {
    return 0;
  }

  // Calculate total citations (internal + external) for all publications in this journal
  const totalCitations = publications.reduce((total, pub) =>
    total + (pub.citation_count || 0) + (pub.external_citation_count || 0), 0);

  // Impact Factor = Average citations per publication
  return (totalCitations / publications.length).toFixed(2);
};

export const getJournalStats = async (publications, journalType, supabase) => {
  console.log(`🔍 getJournalStats called for ${journalType}`);
  console.log(`📚 Input publications:`, publications.length);
  console.log(`📋 Sample publications:`, publications.slice(0, 3).map(p => ({ id: p.id, journal: p.journal, title: p.title?.substring(0, 30) })));

  const journalPublications = publications.filter(pub => pub.journal === journalType);
  console.log(`✅ Filtered ${journalType} publications:`, journalPublications.length);

  // Get external citations from database and extract actual citation numbers
  let externalCitationsCount = 0;
  if (supabase && journalPublications.length > 0) {
    try {
      const publicationIds = journalPublications.map(pub => pub.id);
      const { data: externalCitations, error } = await supabase
        .from('external_citations')
        .select('id, citation_context, metadata')
        .in('publication_id', publicationIds)
        .eq('is_active', true);

      if (!error && externalCitations) {
        // Extract actual citation numbers from citation_context
        for (const citation of externalCitations) {
          let citationNumber = 1; // default

          // First try metadata
          if (citation.metadata && citation.metadata.citationCount) {
            citationNumber = parseInt(citation.metadata.citationCount) || 1;
          } else if (citation.citation_context) {
            // Extract from text like "Found in Google Scholar profile with 7 citations"
            const patterns = [
              /with (\d+) citations/i,
              /(\d+) citations/i,
              /cited (\d+) times/i,
              /(\d+) times cited/i
            ];

            for (const pattern of patterns) {
              const match = citation.citation_context.match(pattern);
              if (match && match[1]) {
                citationNumber = parseInt(match[1]);
                break;
              }
            }
          }

          externalCitationsCount += citationNumber;
        }
      }
    } catch (error) {
      console.error('Error fetching external citations:', error);
    }
  }

  // Calculate total citations (internal + external from publications table + external from database)
  const internalCitations = journalPublications.reduce((total, pub) => total + (pub.citation_count || 0), 0);
  const externalFromPublications = journalPublications.reduce((total, pub) => total + (pub.external_citation_count || 0), 0);
  const totalCitations = internalCitations + externalFromPublications + externalCitationsCount;

  console.log(`📊 Citation calculation for ${journalType}:`);
  console.log(`   Internal citations: ${internalCitations}`);
  console.log(`   External from publications table: ${externalFromPublications}`);
  console.log(`   External from database extraction: ${externalCitationsCount}`);
  console.log(`   Total citations: ${totalCitations}`);

  const stats = {
    totalPublications: journalPublications.length,
    totalCitations: totalCitations,
    internalCitations: internalCitations,
    externalCitations: externalFromPublications + externalCitationsCount,
    externalFromPublications: externalFromPublications,
    externalFromDatabase: externalCitationsCount,
    averageCitations: journalPublications.length > 0
      ? (totalCitations / journalPublications.length).toFixed(2)
      : 0,
    impactFactor: journalPublications.length > 0
      ? (totalCitations / journalPublications.length).toFixed(2)
      : 0,
    publicationsByYear: {}
  };

  // Group publications by year
  journalPublications.forEach(pub => {
    if (!stats.publicationsByYear[pub.year]) {
      stats.publicationsByYear[pub.year] = 0;
    }
    stats.publicationsByYear[pub.year]++;
  });

  return stats;
};

export const formatImpactFactor = (impactFactor) => {
  const factor = parseFloat(impactFactor);

  if (factor === 0 || isNaN(factor)) return "0.00";
  return factor.toFixed(2);
};

// Debug function to help troubleshoot impact factor calculation
export const debugImpactFactor = async (publications, journalType, supabase) => {
  const journalPublications = publications.filter(pub => pub.journal === journalType);

  console.log(`\n🔍 Impact Factor Debug for ${journalType}:`);
  console.log(`📚 Total publications: ${journalPublications.length}`);

  // Get external citations from database and extract actual citation numbers
  let externalCitationsCount = 0;
  if (supabase && journalPublications.length > 0) {
    try {
      const publicationIds = journalPublications.map(pub => pub.id);
      const { data: externalCitations, error } = await supabase
        .from('external_citations')
        .select('*')
        .in('publication_id', publicationIds)
        .eq('is_active', true);

      if (!error && externalCitations) {
        console.log(`📋 Found ${externalCitations.length} external citation records`);

        // Extract actual citation numbers from citation_context
        for (const citation of externalCitations) {
          let citationNumber = 1; // default

          // First try metadata
          if (citation.metadata && citation.metadata.citationCount) {
            citationNumber = parseInt(citation.metadata.citationCount) || 1;
          } else if (citation.citation_context) {
            // Extract from text like "Found in Google Scholar profile with 7 citations"
            const patterns = [
              /with (\d+) citations/i,
              /(\d+) citations/i,
              /cited (\d+) times/i,
              /(\d+) times cited/i
            ];

            for (const pattern of patterns) {
              const match = citation.citation_context.match(pattern);
              if (match && match[1]) {
                citationNumber = parseInt(match[1]);
                break;
              }
            }
          }

          externalCitationsCount += citationNumber;
          console.log(`📊 Citation record: "${citation.citation_context}" → ${citationNumber} citations`);
        }

        console.log(`🌐 Total external citations: ${externalCitationsCount}`);
      } else {
        console.log(`❌ Error fetching external citations:`, error);
      }
    } catch (error) {
      console.error('❌ Database error:', error);
    }
  } else {
    console.log(`⚠️ No supabase client or no publications`);
  }

  const internalCitations = journalPublications.reduce((total, pub) => total + (pub.citation_count || 0), 0);
  const externalFromPublications = journalPublications.reduce((total, pub) => total + (pub.external_citation_count || 0), 0);
  const totalCitations = internalCitations + externalFromPublications + externalCitationsCount;

  console.log(`📊 Internal citations: ${internalCitations}`);
  console.log(`🌐 External from publications table: ${externalFromPublications}`);
  console.log(`🔍 External from database extraction: ${externalCitationsCount}`);
  console.log(`📈 Total citations: ${totalCitations}`);

  const impactFactor = journalPublications.length > 0 ?
    (totalCitations / journalPublications.length).toFixed(2) : 0;

  console.log(`🎯 Impact Factor (Total Citations ÷ Publications): ${totalCitations} ÷ ${journalPublications.length} = ${impactFactor}`);

  return impactFactor;
};