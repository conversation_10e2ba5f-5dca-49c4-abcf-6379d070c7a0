{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperCreateClassFeaturesPlugin", "_default", "exports", "default", "declare", "api", "options", "assertVersion", "createClassFeaturePlugin", "name", "feature", "FEATURES", "fields", "loose", "manipulateOptions", "opts", "parserOpts", "plugins", "push"], "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @babel/development/plugin-name */\n\nimport { declare } from \"@babel/helper-plugin-utils\";\nimport {\n  createClassFeaturePlugin,\n  FEATURES,\n} from \"@babel/helper-create-class-features-plugin\";\n\nexport interface Options {\n  loose?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return createClassFeaturePlugin({\n    name: \"transform-class-properties\",\n\n    api,\n    feature: FEATURES.fields,\n    loose: options.loose,\n\n    manipulateOptions(opts, parserOpts) {\n      if (!process.env.BABEL_8_BREAKING) {\n        // @ts-ignore(Babel 7 vs Babel 8) These plugins have been removed\n        parserOpts.plugins.push(\"classProperties\", \"classPrivateProperties\");\n      }\n    },\n  });\n});\n"], "mappings": ";;;;;;AAEA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,gCAAA,GAAAD,OAAA;AAGoD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMrC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAChDD,GAAG,CAACE,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO,IAAAC,yDAAwB,EAAC;IAC9BC,IAAI,EAAE,4BAA4B;IAElCJ,GAAG;IACHK,OAAO,EAAEC,yCAAQ,CAACC,MAAM;IACxBC,KAAK,EAAEP,OAAO,CAACO,KAAK;IAEpBC,iBAAiBA,CAACC,IAAI,EAAEC,UAAU,EAAE;MACC;QAEjCA,UAAU,CAACC,OAAO,CAACC,IAAI,CAAC,iBAAiB,EAAE,wBAAwB,CAAC;MACtE;IACF;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}