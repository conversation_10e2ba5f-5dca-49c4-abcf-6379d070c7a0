// REAL Web Crawler - Actually searches Google Scholar and ResearchGate
const https = require('https');
const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://rbvgtaqimzpsarvoxubn.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJidmd0YXFpbXpwc2Fydm94dWJuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjAxMDI0MSwiZXhwIjoyMDYxNTg2MjQxfQ.Y1Xcvq50peqVvgRhVVtHP7eEvpGBn-A2EG65H--8XX8';
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

class RealCrawler {
  constructor() {
    this.delay = 5000; // 5 seconds between requests
    this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
  }

  async makeRequest(url) {
    return new Promise((resolve, reject) => {
      const options = {
        headers: {
          'User-Agent': this.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Connection': 'keep-alive',
        },
        timeout: 15000
      };

      https.get(url, options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => resolve(data));
      }).on('error', reject).on('timeout', () => {
        reject(new Error('Request timeout'));
      });
    });
  }

  async searchGoogleScholar(query) {
    try {
      console.log(`🎓 REAL Google Scholar search: ${query}`);
      
      const searchUrl = `https://scholar.google.com/scholar?q=${encodeURIComponent(query)}&hl=en`;
      const html = await this.makeRequest(searchUrl);
      
      // Parse real Google Scholar results
      const results = [];
      const titleRegex = /<h3[^>]*><a[^>]*href="([^"]*)"[^>]*>([^<]+)<\/a><\/h3>/g;
      const citedByRegex = /Cited by (\d+)/g;
      
      let match;
      while ((match = titleRegex.exec(html)) !== null && results.length < 5) {
        const url = match[1];
        const title = match[2].replace(/&[^;]+;/g, '').trim();
        
        // Look for "Cited by" info
        const citedMatch = citedByRegex.exec(html);
        const citedBy = citedMatch ? parseInt(citedMatch[1]) : 0;
        
        if (title.length > 10) {
          results.push({
            url: url.startsWith('http') ? url : `https://scholar.google.com${url}`,
            title: title,
            source: 'Google Scholar',
            citedBy: citedBy
          });
        }
      }
      
      console.log(`✅ Found ${results.length} REAL Google Scholar results`);
      return results;
      
    } catch (error) {
      console.error(`❌ Google Scholar error: ${error.message}`);
      return [];
    }
  }

  async searchResearchGate(query) {
    try {
      console.log(`🔬 REAL ResearchGate search: ${query}`);
      
      const searchUrl = `https://www.researchgate.net/search?q=${encodeURIComponent(query)}`;
      const html = await this.makeRequest(searchUrl);
      
      // Parse real ResearchGate results
      const results = [];
      const linkRegex = /<a[^>]*href="([^"]*publication[^"]*)"[^>]*>([^<]+)<\/a>/g;
      
      let match;
      while ((match = linkRegex.exec(html)) !== null && results.length < 3) {
        const url = match[1];
        const title = match[2].replace(/&[^;]+;/g, '').trim();
        
        if (title.length > 10 && !title.includes('ResearchGate')) {
          results.push({
            url: url.startsWith('http') ? url : `https://www.researchgate.net${url}`,
            title: title,
            source: 'ResearchGate',
            citedBy: 0
          });
        }
      }
      
      console.log(`✅ Found ${results.length} REAL ResearchGate results`);
      return results;
      
    } catch (error) {
      console.error(`❌ ResearchGate error: ${error.message}`);
      return [];
    }
  }

  isRelevantCitation(result, publicationTitle, authors) {
    const resultTitle = result.title.toLowerCase();
    const pubTitle = publicationTitle.toLowerCase();
    const pubAuthors = (authors || '').toLowerCase();
    
    // Check if result title contains publication title words
    const pubWords = pubTitle.split(' ').filter(word => word.length > 3);
    const matchingWords = pubWords.filter(word => resultTitle.includes(word));
    
    // Check author matches
    const authorMatch = pubAuthors && pubAuthors.split(',').some(author => 
      resultTitle.includes(author.trim())
    );
    
    // Calculate relevance score
    const wordScore = matchingWords.length / pubWords.length;
    const authorScore = authorMatch ? 0.3 : 0;
    const sourceScore = result.source === 'Google Scholar' ? 0.2 : 0.1;
    
    const relevanceScore = wordScore + authorScore + sourceScore;
    
    return relevanceScore > 0.3; // Only return if reasonably relevant
  }

  async saveCitation(publicationId, result) {
    try {
      // Check if already exists
      const { data: existing } = await supabase
        .from('external_citations')
        .select('id')
        .eq('publication_id', publicationId)
        .eq('source_url', result.url)
        .single();

      if (existing) {
        return false; // Already exists
      }

      // Save new citation
      const { error } = await supabase
        .from('external_citations')
        .insert({
          publication_id: publicationId,
          source_url: result.url,
          source_title: result.title,
          source_domain: result.source === 'Google Scholar' ? 'scholar.google.com' : 'researchgate.net',
          citation_context: `Found via ${result.source} search`,
          citation_type: 'reference',
          confidence_score: 0.8,
          found_date: new Date().toISOString(),
          is_verified: false,
          is_active: true,
          metadata: { source: result.source, citedBy: result.citedBy }
        });

      if (error) {
        console.error('Database error:', error);
        return false;
      }

      console.log(`✅ SAVED REAL citation: ${result.title.substring(0, 50)}...`);
      return true;

    } catch (error) {
      console.error('Error saving citation:', error);
      return false;
    }
  }

  async updateCitationCount(publicationId) {
    try {
      const { data: citations } = await supabase
        .from('external_citations')
        .select('id')
        .eq('publication_id', publicationId)
        .eq('is_active', true);

      const count = citations ? citations.length : 0;

      await supabase
        .from('publications')
        .update({ external_citation_count: count })
        .eq('id', publicationId);

      console.log(`✅ Updated citation count: ${count}`);
      return count;

    } catch (error) {
      console.error('Error updating count:', error);
      return 0;
    }
  }

  async crawlPublication(publication) {
    try {
      console.log(`\n🚀 REAL crawling: ${publication.title}`);
      
      const queries = [
        `"${publication.title}"`,
        publication.authors ? `"${publication.title}" "${publication.authors.split(',')[0].trim()}"` : null
      ].filter(Boolean);

      let totalFound = 0;
      let totalSaved = 0;

      for (const query of queries) {
        console.log(`\n🔍 Searching: ${query}`);
        
        // Search Google Scholar
        const scholarResults = await this.searchGoogleScholar(query);
        await new Promise(resolve => setTimeout(resolve, this.delay));
        
        // Search ResearchGate  
        const rgResults = await this.searchResearchGate(query);
        await new Promise(resolve => setTimeout(resolve, this.delay));
        
        // Process all results
        const allResults = [...scholarResults, ...rgResults];
        
        for (const result of allResults) {
          if (this.isRelevantCitation(result, publication.title, publication.authors)) {
            totalFound++;
            console.log(`📋 RELEVANT: ${result.title.substring(0, 60)}...`);
            
            const saved = await this.saveCitation(publication.id, result);
            if (saved) {
              totalSaved++;
            }
          }
        }
      }

      await this.updateCitationCount(publication.id);

      console.log(`\n✅ REAL crawl completed: ${totalFound} found, ${totalSaved} saved`);
      
      return {
        publicationId: publication.id,
        citationsFound: totalFound,
        citationsSaved: totalSaved
      };

    } catch (error) {
      console.error(`❌ Crawl error: ${error.message}`);
      return {
        publicationId: publication.id,
        citationsFound: 0,
        citationsSaved: 0,
        error: error.message
      };
    }
  }

  async crawlAllPublications() {
    try {
      console.log('🚀 Starting REAL web crawl...');
      console.log('🌐 Actually searching Google Scholar and ResearchGate');
      
      const { data: publications, error } = await supabase
        .from('publications')
        .select('*')
        .limit(2); // Limit to 2 for real crawling

      if (error) throw new Error(`Database error: ${error.message}`);

      console.log(`📚 Will crawl ${publications.length} publications`);
      console.log(`⏰ Estimated time: ${publications.length * 2} minutes`);

      let totalFound = 0;
      let totalSaved = 0;

      for (const publication of publications) {
        const result = await this.crawlPublication(publication);
        totalFound += result.citationsFound;
        totalSaved += result.citationsSaved;
      }

      console.log(`\n🎉 REAL crawl completed!`);
      console.log(`📊 Total found: ${totalFound}, saved: ${totalSaved}`);

      return {
        publicationsProcessed: publications.length,
        totalCitationsFound: totalFound,
        totalSaved: totalSaved
      };

    } catch (error) {
      console.error(`❌ Real crawl error: ${error.message}`);
      throw error;
    }
  }
}

module.exports = { RealCrawler };
