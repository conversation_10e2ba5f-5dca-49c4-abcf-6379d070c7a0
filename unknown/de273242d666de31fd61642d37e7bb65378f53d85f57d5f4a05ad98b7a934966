import React from 'react';

function AuthorHandbook() {
  return (
    <div className="container py-5">
      <h2 className="mb-4 text-center fw-bold">Author Handbook</h2>
      <p>Welcome to the Darsgah-e-Ahlebait Author Handbook. This guide covers every step of the publication process, from manuscript preparation to post-publication support.</p>
      <ul>
        <li>Preparing your manuscript</li>
        <li>Submission and peer review process</li>
        <li>Ethics and best practices</li>
        <li>Revision and acceptance</li>
        <li>Post-publication support</li>
      </ul>
      <div className="alert alert-success">Download the full handbook: <a href="#">Author_Handbook.pdf</a> (coming soon)</div>
    </div>
  );
}

export default AuthorHandbook; 