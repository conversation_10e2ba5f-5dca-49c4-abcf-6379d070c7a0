import React, { useEffect, useState } from 'react';
import { useParams, Link, useLocation } from 'react-router-dom';
import { supabase } from './supabaseClient';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';

function useQuery() {
  return new URLSearchParams(useLocation().search);
}

function IssueDetail() {
  const { issue_number } = useParams();
  const query = useQuery();
  const journal_type = query.get('journal_type');
  const year = query.get('year');
  const volume_number = query.get('volume_number');
  const [publications, setPublications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selected, setSelected] = useState([]);
  const [currentVolumeId, setCurrentVolumeId] = useState(null);
  const [nextIssueNumber, setNextIssueNumber] = useState(null);
  const [nextVolumeId, setNextVolumeId] = useState(null);
  const [nextVolumeFirstIssue, setNextVolumeFirstIssue] = useState(null);

  useEffect(() => {
    async function fetchPublications() {
      setLoading(true);
      let queryBuilder = supabase
        .from('publications')
        .select('id, title, authors, pdf_url, journal, year, volume, issue_number, volume_id')
        .eq('issue_number', String(issue_number));
      if (journal_type) queryBuilder = queryBuilder.ilike('journal', journal_type);
      if (year) queryBuilder = queryBuilder.eq('year', Number(year));
      if (volume_number) queryBuilder = queryBuilder.eq('volume', Number(volume_number));
      const { data, error } = await queryBuilder.order('created_at', { ascending: false });
      console.log('Fetched publications:', data, 'Error:', error, 'Query:', {
        issue_number: String(issue_number),
        journal_type,
        year,
        volume_number
      });
      if (!error) setPublications(data);
      setLoading(false);
    }
    fetchPublications();
  }, [issue_number, journal_type, year, volume_number]);

  // Fetch current issue's volume_id
  useEffect(() => {
    async function fetchCurrentVolume() {
      const { data, error } = await supabase
        .from('publications')
        .select('volume_id')
        .eq('issue_number', String(issue_number))
        .limit(1)
        .single();
      if (data && data.volume_id) setCurrentVolumeId(data.volume_id);
    }
    fetchCurrentVolume();
  }, [issue_number]);

  // Fetch next issue in the same volume
  useEffect(() => {
    if (!currentVolumeId) return;
    async function fetchNextIssue() {
      const currentIssueNum = Number(issue_number);
      const { data, error } = await supabase
        .from('publications')
        .select('issue_number')
        .eq('volume_id', currentVolumeId)
        .order('issue_number', { ascending: true });
      if (data && data.length > 0) {
        // Find the next issue number numerically
        const allIssues = data.map(d => Number(d.issue_number)).sort((a, b) => a - b);
        const next = allIssues.find(num => num > currentIssueNum);
        setNextIssueNumber(next || null);
      } else setNextIssueNumber(null);
    }
    fetchNextIssue();
  }, [currentVolumeId, issue_number]);

  // Fetch next volume and its first issue
  useEffect(() => {
    if (!currentVolumeId) return;
    async function fetchNextVolume() {
      // Get current volume_number
      const { data: currentVol, error: volErr } = await supabase
        .from('volumes')
        .select('volume_number')
        .eq('id', currentVolumeId)
        .single();
      if (!currentVol) return;
      // Get next volume
      const { data: nextVol, error: nextVolErr } = await supabase
        .from('volumes')
        .select('id, volume_number')
        .order('volume_number', { ascending: true });
      if (nextVol && nextVol.length > 0) {
        // Find the next volume numerically
        const allVolumes = nextVol.map(v => ({ id: v.id, volume_number: Number(v.volume_number) })).sort((a, b) => a.volume_number - b.volume_number);
        const idx = allVolumes.findIndex(v => v.id === currentVolumeId);
        const nextVolume = allVolumes[idx + 1];
        if (nextVolume) {
          setNextVolumeId(nextVolume.id);
          // Get first issue of next volume
          const { data: firstIssue, error: firstIssueErr } = await supabase
            .from('publications')
            .select('issue_number')
            .eq('volume_id', nextVolume.id)
            .order('issue_number', { ascending: true });
          if (firstIssue && firstIssue.length > 0) {
            // Find the lowest issue number numerically
            const minIssue = Math.min(...firstIssue.map(f => Number(f.issue_number)));
            setNextVolumeFirstIssue(minIssue);
          } else setNextVolumeFirstIssue(null);
        } else {
          setNextVolumeId(null);
          setNextVolumeFirstIssue(null);
        }
      } else {
        setNextVolumeId(null);
        setNextVolumeFirstIssue(null);
      }
    }
    fetchNextVolume();
  }, [currentVolumeId]);

  const handleSelect = (pubId) => {
    setSelected(prev => prev.includes(pubId) ? prev.filter(id => id !== pubId) : [...prev, pubId]);
  };

  const handleSelectAll = () => {
    if (selected.length === publications.length) {
      setSelected([]);
    } else {
      setSelected(publications.map(pub => pub.id));
    }
  };

  const handleDownload = async (all = false) => {
    let pubsToDownload = publications.filter(pub => (all ? true : selected.includes(pub.id)) && pub.pdf_url);
    if (pubsToDownload.length === 0) return;
    if (pubsToDownload.length === 1) {
      window.open(`https://rbvgtaqimzpsarvoxubn.supabase.co/storage/v1/object/public/pdf/${pubsToDownload[0].pdf_url.split('/').pop()}`, '_blank');
      return;
    }
    const zip = new JSZip();
    const projectRef = 'rbvgtaqimzpsarvoxubn';
    const bucket = 'pdf';
    const baseUrl = `https://${projectRef}.supabase.co/storage/v1/object/public/${bucket}/`;
    await Promise.all(pubsToDownload.map(async pub => {
      const fileName = pub.pdf_url.split('/').pop();
      try {
        const response = await fetch(baseUrl + fileName);
        if (response.ok) {
          const blob = await response.blob();
          zip.file(fileName, blob);
        }
      } catch (e) {}
    }));
    const zipBlob = await zip.generateAsync({ type: 'blob' });
    saveAs(zipBlob, all ? 'full-issue.zip' : 'publications.zip');
  };

  return (
    <div className="issue-detail-page container-fluid px-0">
      <div className="row gx-0">
        <div className="col-md-3 d-none d-md-block">
          <div className="bg-white border-end vh-100 position-sticky" style={{top: '72px', zIndex: 1, minHeight: 'calc(100vh - 72px - 56px)'}}>
            <div className="p-4">
              <h5 className="mb-3">Actions</h5>
              <button type="button" className="btn btn-outline-primary w-100 mb-2" onClick={handleSelectAll} disabled={publications.length === 0}>
                {selected.length === publications.length ? 'Unselect All' : 'Select All Publications'}
              </button>
              <button type="button" className="btn btn-success w-100" onClick={() => { setSelected(publications.map(pub => pub.id)); handleDownload(true); }} disabled={publications.length === 0}>
                Download Full Issue
              </button>
            </div>
          </div>
        </div>
        <div className="col-md-9 col-12" style={{minHeight: 'calc(100vh - 72px - 56px)'}}>
          <div className="py-4 px-3 px-md-5">
            <div className="d-flex align-items-center justify-content-between mb-4">
              <h2 className="mb-0">Publications in this Issue</h2>
              <div className="d-flex gap-2">
                {/* Debug output for current/next issue/volume */}
                <span style={{fontSize: '12px', color: '#888'}}>
                  Issue: {issue_number} | Next: {nextIssueNumber || 'none'} | Next Vol Issue: {nextVolumeFirstIssue || 'none'}
                </span>
                {nextIssueNumber && (
                  <Link to={`/issue/${nextIssueNumber}`} className="btn btn-outline-primary">Next Issue</Link>
                )}
                {nextVolumeFirstIssue && (
                  <Link to={`/issue/${nextVolumeFirstIssue}`} className="btn btn-outline-success">Next Volume (First Issue)</Link>
                )}
              </div>
            </div>
            {loading ? (
              <div className="text-center my-5">
                <div className="spinner-border text-primary" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              </div>
            ) : publications.length === 0 ? (
              <div className="alert alert-info">No publications found for this issue.</div>
            ) : (
              <form>
                <ul className="list-group mb-3">
                  {publications.map(pub => (
                    <li key={pub.id} className="list-group-item d-flex align-items-center py-4" style={{fontSize: '1.15rem'}}>
                      <input type="checkbox" className="form-check-input me-3" checked={selected.includes(pub.id)} onChange={() => handleSelect(pub.id)} style={{width: '1.3em', height: '1.3em'}} />
                      <span className="flex-grow-1">
                        <Link to={`/publication/${pub.id}`}>{pub.title}</Link> <span className="text-muted">({pub.authors})</span>
                      </span>
                      {pub.pdf_url && (
                        <a
                          href={`https://rbvgtaqimzpsarvoxubn.supabase.co/storage/v1/object/public/pdf/${pub.pdf_url.split('/').pop()}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="btn btn-sm btn-outline-secondary ms-2"
                        >
                          PDF
                        </a>
                      )}
                    </li>
                  ))}
                </ul>
                {selected.length > 0 && (
                  <button type="button" className="btn btn-success" onClick={() => handleDownload(false)}>Download Selected Publications</button>
                )}
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default IssueDetail; 