import React, { useState, useEffect } from 'react';
import { supabase } from './supabaseClient';

export default function ReviewerApplication() {
  const [form, setForm] = useState({
    name: '',
    email: '',
    orcid: '',
    institution: '',
    department: '',
    qualification: '',
    profile_link: '',
    publications: [{ title: '', journal: '', link: '' }],
    experience: '',
    reviewed_before: 'no',
    reviewed_journals: '',
    languages: '',
    topics: '',
    statement: '',
    availability: '',
    awards: '',
    country: '',
    timezone: '',
    agreement: false,
    signature: '',
  });
  const [cvFile, setCvFile] = useState(null);
  const [profilePic, setProfilePic] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    // Prefill email from localStorage user
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      const user = JSON.parse(storedUser);
      if (user.email) setForm(f => ({ ...f, email: user.email }));
    }
  }, []);

  function handlePubChange(idx, field, value) {
    setForm(f => {
      const pubs = [...f.publications];
      pubs[idx][field] = value;
      return { ...f, publications: pubs };
    });
  }

  function addPublication() {
    setForm(f => ({ ...f, publications: [...f.publications, { title: '', journal: '', link: '' }] }));
  }

  function removePublication(idx) {
    setForm(f => ({ ...f, publications: f.publications.filter((_, i) => i !== idx) }));
  }

  async function handleSubmit(e) {
    e.preventDefault();
    setSubmitting(true);
    setError('');
    let cv_url = '', pic_url = '';
    if (cvFile) {
      const { data, error } = await supabase.storage.from('pdfuploads').upload(`cv-${Date.now()}-${cvFile.name}`, cvFile);
      if (error) { setError('CV upload failed'); setSubmitting(false); return; }
      cv_url = supabase.storage.from('pdfuploads').getPublicUrl(data.path).data.publicUrl;
    }
    if (profilePic) {
      const { data, error } = await supabase.storage.from('avatars').upload(`pic-${Date.now()}-${profilePic.name}`, profilePic);
      if (error) { setError('Profile picture upload failed'); setSubmitting(false); return; }
      pic_url = supabase.storage.from('avatars').getPublicUrl(data.path).data.publicUrl;
    }
    const { error: insertError } = await supabase.from('editorial_board_submissions').insert([
      {
        name: form.name,
        email: form.email,
        orcid: form.orcid,
        institution: form.institution,
        department: form.department,
        qualification: form.qualification,
        profile_link: form.profile_link,
        publications: JSON.stringify(form.publications),
        experience: form.experience,
        reviewed_before: form.reviewed_before,
        reviewed_journals: form.reviewed_journals,
        languages: form.languages,
        topics: form.topics,
        statement: form.statement,
        availability: form.availability,
        awards: form.awards,
        country: form.country,
        timezone: form.timezone,
        agreement: form.agreement,
        signature: form.signature,
        cv_url,
        profile_pic_url: pic_url,
      },
    ]);
    if (insertError) { setError('Submission failed'); setSubmitting(false); return; }
    setSubmitting(false);
    setSuccess(true);
  }

  return (
    <div className="container py-5" style={{maxWidth: 800}}>
      {success ? (
        <div className="alert alert-success">Your application has been submitted! The editorial team will contact you soon.</div>
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="row g-3">
            <div className="col-md-6">
              <label className="form-label">Full Name (As per Passport or Degree)</label>
              <input type="text" className="form-control" value={form.name} onChange={e => setForm(f => ({ ...f, name: e.target.value }))} required />
            </div>
            <div className="col-md-6">
              <label className="form-label">Email Address (Institutional Preferred)</label>
              <input type="email" className="form-control" value={form.email} readOnly required />
            </div>
            <div className="col-md-6">
              <label className="form-label">ORCID ID (if available)</label>
              <input type="text" className="form-control" value={form.orcid} onChange={e => setForm(f => ({ ...f, orcid: e.target.value }))} />
            </div>
            <div className="col-md-6">
              <label className="form-label">Affiliation / Institution</label>
              <input type="text" className="form-control" value={form.institution} onChange={e => setForm(f => ({ ...f, institution: e.target.value }))} required />
            </div>
            <div className="col-md-6">
              <label className="form-label">Department / Field of Expertise</label>
              <input type="text" className="form-control" value={form.department} onChange={e => setForm(f => ({ ...f, department: e.target.value }))} required />
            </div>
            <div className="col-md-6">
              <label className="form-label">Highest Academic Qualification</label>
              <input type="text" className="form-control" value={form.qualification} onChange={e => setForm(f => ({ ...f, qualification: e.target.value }))} required />
            </div>
            <div className="col-md-6">
              <label className="form-label">LinkedIn / Google Scholar / ResearchGate Profile</label>
              <input type="text" className="form-control" value={form.profile_link} onChange={e => setForm(f => ({ ...f, profile_link: e.target.value }))} />
            </div>
            <div className="col-md-6">
              <label className="form-label">Upload CV/Resume (PDF Only)</label>
              <input type="file" className="form-control" accept="application/pdf" onChange={e => setCvFile(e.target.files[0])} required />
            </div>
            <div className="col-md-6">
              <label className="form-label">Profile Picture (optional)</label>
              <input type="file" className="form-control" accept="image/*" onChange={e => setProfilePic(e.target.files[0])} />
            </div>
            <div className="col-md-6">
              <label className="form-label">Country</label>
              <input type="text" className="form-control" value={form.country} onChange={e => setForm(f => ({ ...f, country: e.target.value }))} />
            </div>
            <div className="col-md-6">
              <label className="form-label">Timezone</label>
              <input type="text" className="form-control" value={form.timezone} onChange={e => setForm(f => ({ ...f, timezone: e.target.value }))} />
            </div>
            <div className="col-md-6">
              <label className="form-label">Awards / Fellowships (if any)</label>
              <input type="text" className="form-control" value={form.awards} onChange={e => setForm(f => ({ ...f, awards: e.target.value }))} />
            </div>
            <div className="col-md-6">
              <label className="form-label">Availability per month (papers you can review)</label>
              <input type="number" className="form-control" value={form.availability} onChange={e => setForm(f => ({ ...f, availability: e.target.value }))} />
            </div>
          </div>
          <div className="mt-4">
            <label className="form-label">Published Research (At least 2–5 references)</label>
            {form.publications.map((pub, idx) => (
              <div className="row g-2 mb-2" key={idx}>
                <div className="col-md-4"><input type="text" className="form-control" placeholder="Title" value={pub.title} onChange={e => handlePubChange(idx, 'title', e.target.value)} required /></div>
                <div className="col-md-4"><input type="text" className="form-control" placeholder="Journal" value={pub.journal} onChange={e => handlePubChange(idx, 'journal', e.target.value)} required /></div>
                <div className="col-md-3"><input type="text" className="form-control" placeholder="Link (optional)" value={pub.link} onChange={e => handlePubChange(idx, 'link', e.target.value)} /></div>
                <div className="col-md-1 d-flex align-items-center"><button type="button" className="btn btn-sm btn-danger" onClick={() => removePublication(idx)} disabled={form.publications.length <= 2}>-</button></div>
              </div>
            ))}
            <button type="button" className="btn btn-sm btn-outline-primary mt-2" onClick={addPublication}>+ Add Publication</button>
          </div>
          <div className="mt-4">
            <label className="form-label">Reviewing / Editorial Experience</label>
            <div className="mb-2">
              <select className="form-select" value={form.reviewed_before} onChange={e => setForm(f => ({ ...f, reviewed_before: e.target.value }))} required>
                <option value="no">No</option>
                <option value="yes">Yes</option>
              </select>
            </div>
            {form.reviewed_before === 'yes' && (
              <input type="text" className="form-control mb-2" placeholder="List journals" value={form.reviewed_journals} onChange={e => setForm(f => ({ ...f, reviewed_journals: e.target.value }))} />
            )}
          </div>
          <div className="mt-4">
            <label className="form-label">Languages You Can Review In</label>
            <input type="text" className="form-control" value={form.languages} onChange={e => setForm(f => ({ ...f, languages: e.target.value }))} />
          </div>
          <div className="mt-4">
            <label className="form-label">Topics You Can Handle (Keywords)</label>
            <input type="text" className="form-control" value={form.topics} onChange={e => setForm(f => ({ ...f, topics: e.target.value }))} />
          </div>
          <div className="mt-4">
            <label className="form-label">Statement of Purpose (Short Paragraph)</label>
            <textarea className="form-control" rows={3} value={form.statement} onChange={e => setForm(f => ({ ...f, statement: e.target.value }))} required />
          </div>
          <div className="mt-4">
            <div className="form-check mb-2">
              <input className="form-check-input" type="checkbox" checked={form.agreement} onChange={e => setForm(f => ({ ...f, agreement: e.target.checked }))} id="agreeCheck" required />
              <label className="form-check-label" htmlFor="agreeCheck">
                I understand my role as an editorial board member and agree to review papers ethically and on time.
              </label>
            </div>
            <label className="form-label">Signature (Draw or Type)</label>
            <input type="text" className="form-control" value={form.signature} onChange={e => setForm(f => ({ ...f, signature: e.target.value }))} required />
          </div>
          {error && <div className="alert alert-danger mt-3">{error}</div>}
          <button type="submit" className="btn btn-success rounded-pill px-4 mt-4" disabled={submitting}>{submitting ? 'Submitting...' : 'Submit Application'}</button>
        </form>
      )}
    </div>
  );
} 