-- Create comment_likes table
CREATE TABLE IF NOT EXISTS comment_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    comment_id UUID NOT NULL REFERENCES blog_comments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(comment_id, user_id) -- Prevent duplicate likes
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_comment_likes_comment_id ON comment_likes(comment_id);
CREATE INDEX IF NOT EXISTS idx_comment_likes_user_id ON comment_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_comment_likes_created_at ON comment_likes(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE comment_likes ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Allow users to read all likes
CREATE POLICY "Allow users to read likes" ON comment_likes
    FOR SELECT USING (true);

-- Allow authenticated users to insert their own likes
CREATE POLICY "Allow users to insert likes" ON comment_likes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Allow users to delete their own likes
CREATE POLICY "Allow users to delete their own likes" ON comment_likes
    FOR DELETE USING (auth.uid() = user_id); 