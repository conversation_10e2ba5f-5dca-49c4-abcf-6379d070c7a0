import React from 'react';
import { Helmet } from 'react-helmet';

const PublicationMetaTags = ({ publication }) => {
  if (!publication) return null;

  // Clean and format data
  const title = publication.title || '';
  const authors = publication.authors || '';
  const year = publication.year || publication.published_date?.split('-')[0] || '';
  const journal = publication.journal_type?.toUpperCase() || 'JBS';
  const volume = publication.volume || '';
  const issue = publication.issue || '';
  const doi = publication.doi || '';
  const abstract = publication.abstract || publication.summary || '';
  const pdfUrl = publication.pdf_url || '';
  
  // Generate keywords from title, authors, and journal
  const keywords = [
    journal,
    'scholarly journal',
    'academic research',
    'peer-reviewed',
    'open access',
    ...title.toLowerCase().split(' ').filter(word => word.length > 3),
    ...authors.toLowerCase().split(' ').filter(word => word.length > 2)
  ].join(', ');

  // Truncate description to 160 characters
  const description = abstract.length > 160 
    ? abstract.substring(0, 157) + '...' 
    : abstract;

  // Truncate title to 60 characters
  const pageTitle = title.length > 60 
    ? title.substring(0, 57) + '...' 
    : title;

  return (
    <Helmet>
      {/* Standard SEO Meta Tags */}
      <title>{pageTitle} | {journal} Journal</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content={authors} />
      
      {/* Google Scholar Citation Meta Tags */}
      <meta name="citation_title" content={title} />
      <meta name="citation_author" content={authors} />
      <meta name="citation_publication_date" content={year} />
      <meta name="citation_journal_title" content={`${journal} Journal`} />
      <meta name="citation_volume" content={volume} />
      <meta name="citation_issue" content={issue} />
      
      {/* Optional citation tags */}
      {doi && <meta name="citation_doi" content={doi} />}
      {pdfUrl && <meta name="citation_pdf_url" content={pdfUrl} />}
      
      {/* Additional SEO tags */}
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content="article" />
      <meta property="og:site_name" content="Darsgah-e-Ahlebait" />
      
      <meta name="twitter:card" content="summary" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={description} />
      
      {/* Scholarly-specific tags */}
      <meta name="citation_abstract" content={abstract} />
      <meta name="citation_language" content="en" />
      <meta name="citation_publisher" content="Darsgah-e-Ahlebait" />
      <meta name="citation_online_date" content={year} />
      
      {/* Open Graph for social sharing */}
      <meta property="og:url" content={window.location.href} />
      <meta property="og:image" content="/logo.png" />
      
      {/* Schema.org structured data for better SEO */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "ScholarlyArticle",
          "headline": title,
          "author": {
            "@type": "Person",
            "name": authors
          },
          "publisher": {
            "@type": "Organization",
            "name": "Darsgah-e-Ahlebait",
            "url": "https://darsgah-e-ahlebait.com"
          },
          "datePublished": year,
          "description": description,
          "journalName": `${journal} Journal`,
          "volumeNumber": volume,
          "issueNumber": issue,
          "doi": doi,
          "url": window.location.href,
          "pdfUrl": pdfUrl
        })}
      </script>
    </Helmet>
  );
};

export default PublicationMetaTags; 