const { createClient } = require('@supabase/supabase-js');
const pdfjsLib = require('pdfjs-dist/legacy/build/pdf.js');
const fs = require('fs');
const path = require('path');
const cron = require('node-cron');

// --- CONFIGURATION ---
const SUPABASE_URL = 'https://rbvgtaqimzpsarvoxubn.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJidmd0YXFpbXpwc2Fydm94dWJuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjAxMDI0MSwiZXhwIjoyMDYxNTg2MjQxfQ.Y1Xcvq50peqVvgRhVVtHP7eEvpGBn-A2EG65H--8XX8';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.worker.min.js';

// --- Helper: Extract references from PDF ---
async function extractReferencesFromPdf(pdfUrl) {
    try {
        console.log(`Processing PDF: ${pdfUrl}`);
        const loadingTask = pdfjsLib.getDocument({ 
            url: pdfUrl, 
            isEvalSupported: false, 
            disableFontFace: true, 
            nativeImageDecoderSupport: 'none' 
        });
        const pdf = await loadingTask.promise;
        
        let allText = '';
        // Extract text from all pages
        for (let i = 1; i <= pdf.numPages; i++) {
            const page = await pdf.getPage(i);
            const content = await page.getTextContent();
            allText += content.items.map(item => item.str).join(' ') + '\n';
        }
        
        // Look for references section
        const lines = allText.split(/\n|\r/).map(l => l.trim()).filter(l => l.length > 0);
        const refIdx = lines.findIndex(l => /references?|bibliography/i.test(l));
        
        let references = [];
        if (refIdx !== -1) {
            // Extract references from the references section
            references = lines.slice(refIdx + 1).filter(line => {
                // Look for patterns that indicate a reference
                return /^\[\d+\]|^\(\d+\)|^[A-Z][a-z]+,\s*[A-Z]\.|^[A-Z][a-z]+,\s*\d{4}|^[A-Z][a-z]+\s+et\s+al\.|^[A-Z][a-z]+\s+and\s+[A-Z]/.test(line) ||
                       /doi:|https?:\/\/|www\.|\.com|\.org|\.edu/.test(line) ||
                       line.length > 20 && /[A-Z][a-z]/.test(line);
            });
        }
        
        return references;
    } catch (error) {
        console.error(`Error processing PDF ${pdfUrl}:`, error.message);
        return [];
    }
}

// --- Helper: Match references to publications ---
function matchReferenceToPublication(reference, publications) {
    // Try to match by DOI first
    const doiMatch = reference.match(/10\.\d{4,9}\/[-._;()/:A-Z0-9]+/i);
    if (doiMatch) {
        const doi = doiMatch[0].toLowerCase();
        const pub = publications.find(p => p.doi && p.doi.toLowerCase() === doi);
        if (pub) return pub.id;
    }
    
    // Try to match by title (fuzzy matching)
    const refText = reference.toLowerCase().replace(/[^a-zA-Z0-9 ]/g, ' ').replace(/\s+/g, ' ').trim();
    
    for (const pub of publications) {
        if (!pub.title) continue;
        
        const pubTitle = pub.title.toLowerCase().replace(/[^a-zA-Z0-9 ]/g, ' ').replace(/\s+/g, ' ').trim();
        
        // Check if reference contains the publication title
        if (refText.includes(pubTitle) && pubTitle.length > 10) {
            return pub.id;
        }
        
        // Check if publication title contains key parts of reference
        const refWords = refText.split(' ').filter(word => word.length > 3);
        const titleWords = pubTitle.split(' ').filter(word => word.length > 3);
        
        const commonWords = refWords.filter(word => titleWords.includes(word));
        if (commonWords.length >= Math.min(3, Math.min(refWords.length, titleWords.length))) {
            return pub.id;
        }
    }
    
    return null;
}

// --- Helper: Update citation counts in database ---
async function updateCitationCounts(citationCounts) {
    console.log('Updating citation counts in database...');
    
    for (const [pubId, count] of Object.entries(citationCounts)) {
        try {
            const { error } = await supabase
                .from('publications')
                .update({ citation_count: count })
                .eq('id', pubId);
                
            if (error) {
                console.error(`Error updating publication ${pubId}:`, error);
            } else {
                console.log(`Updated publication ${pubId} with ${count} citations`);
            }
        } catch (error) {
            console.error(`Failed to update publication ${pubId}:`, error);
        }
    }
}

// --- Helper: Log crawling statistics ---
async function logCrawlingStats(stats) {
    const logEntry = {
        timestamp: new Date().toISOString(),
        total_publications: stats.totalPublications,
        processed_pdfs: stats.processedPdfs,
        total_citations_found: stats.totalCitationsFound,
        successful_matches: stats.successfulMatches,
        processing_time: stats.processingTime,
        errors: stats.errors
    };
    
    try {
        await supabase
            .from('crawling_logs')
            .insert(logEntry);
    } catch (error) {
        console.error('Failed to log crawling stats:', error);
    }
}

// --- Main Crawler Function ---
async function crawlCitations() {
    console.log('Starting citation crawling process...');
    const startTime = Date.now();
    
    const stats = {
        totalPublications: 0,
        processedPdfs: 0,
        totalCitationsFound: 0,
        successfulMatches: 0,
        processingTime: 0,
        errors: 0
    };
    
    try {
        // 1. Fetch all publications
        console.log('Fetching publications from database...');
        const { data: publications, error } = await supabase
            .from('publications')
            .select('id, title, authors, pdf_url, doi, journal, year');
            
        if (error) {
            console.error('Error fetching publications:', error);
            return;
        }
        
        stats.totalPublications = publications.length;
        console.log(`Found ${publications.length} publications to process`);
        
        // 2. Initialize citation counts
        const citationCounts = {};
        publications.forEach(pub => {
            citationCounts[pub.id] = 0;
        });
        
        // 3. Process each publication with PDF
        for (const pub of publications) {
            if (!pub.pdf_url) {
                console.log(`Skipping ${pub.title} - no PDF URL`);
                continue;
            }
            
            try {
                console.log(`Processing: ${pub.title}`);
                stats.processedPdfs++;
                
                // Build full PDF URL
                let pdfUrl = pub.pdf_url;
                if (!pdfUrl.startsWith('http')) {
                    pdfUrl = `https://rbvgtaqimzpsarvoxubn.supabase.co/storage/v1/object/public/pdf/${pdfUrl.split('/').pop()}`;
                }
                
                // Extract references from PDF
                const references = await extractReferencesFromPdf(pdfUrl);
                stats.totalCitationsFound += references.length;
                
                console.log(`Found ${references.length} references in ${pub.title}`);
                
                // Match references to publications
                for (const reference of references) {
                    const matchedPubId = matchReferenceToPublication(reference, publications);
                    if (matchedPubId) {
                        citationCounts[matchedPubId]++;
                        stats.successfulMatches++;
                    }
                }
                
            } catch (error) {
                console.error(`Error processing ${pub.title}:`, error.message);
                stats.errors++;
            }
        }
        
        // 4. Update database with citation counts
        await updateCitationCounts(citationCounts);
        
        // 5. Calculate and log statistics
        stats.processingTime = Date.now() - startTime;
        await logCrawlingStats(stats);
        
        console.log('Citation crawling completed!');
        console.log('Statistics:', stats);
        
    } catch (error) {
        console.error('Fatal error in citation crawler:', error);
        stats.errors++;
        await logCrawlingStats(stats);
    }
}

// --- Schedule daily crawling ---
function scheduleDailyCrawling() {
    // Run every day at 2:00 AM
    cron.schedule('0 2 * * *', async () => {
        console.log('Starting scheduled citation crawling...');
        await crawlCitations();
    }, {
        scheduled: true,
        timezone: "Asia/Karachi" // Pakistan timezone
    });
    
    console.log('Citation crawler scheduled to run daily at 2:00 AM (Pakistan time)');
}

// --- Manual run function ---
async function runManualCrawl() {
    console.log('Running manual citation crawl...');
    await crawlCitations();
}

// --- Export functions for external use ---
module.exports = {
    crawlCitations,
    scheduleDailyCrawling,
    runManualCrawl
};

// --- Run if called directly ---
if (require.main === module) {
    // Check command line arguments
    const args = process.argv.slice(2);
    
    if (args.includes('--schedule')) {
        scheduleDailyCrawling();
        console.log('Crawler scheduled. Press Ctrl+C to exit.');
    } else if (args.includes('--run')) {
        runManualCrawl().then(() => {
            console.log('Manual crawl completed.');
            process.exit(0);
        }).catch(error => {
            console.error('Manual crawl failed:', error);
            process.exit(1);
        });
    } else {
        console.log('Usage:');
        console.log('  node citation_crawler.js --schedule  # Schedule daily crawling');
        console.log('  node citation_crawler.js --run       # Run manual crawl');
    }
} 