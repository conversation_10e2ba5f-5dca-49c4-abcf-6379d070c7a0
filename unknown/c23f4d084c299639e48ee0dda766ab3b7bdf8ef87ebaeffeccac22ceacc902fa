import React, { useEffect, useState } from 'react';
import { supabase } from './supabaseClient';
import { Link } from 'react-router-dom';
import { FaBookOpen, FaUsers, FaGlobe } from 'react-icons/fa';

export default function ProfileAuthoredPublications({ userDetails }) {
  const [publications, setPublications] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchAuthoredPubs() {
      setLoading(true);
      if (!userDetails || !userDetails.full_name) {
        setPublications([]);
        setLoading(false);
        return;
      }
      // Use ilike for case-insensitive search, but filter in JS for robust name match
      const { data, error } = await supabase
        .from('publications')
        .select('id, title, authors, journal, year')
        .ilike('authors', `%${userDetails.full_name}%`);
      let results = [];
      if (!error && data) {
        // Normalize function: lowercase, remove non-letters except spaces, collapse spaces
        const normalize = str => str
          .toLowerCase()
          .replace(/[^a-z\s]/g, '')
          .replace(/\s+/g, ' ')
          .trim();
        const target = normalize(userDetails.full_name);
        results = data.filter(pub => {
          if (!pub.authors) return false;
          const authorsNorm = normalize(pub.authors);
          return authorsNorm.includes(target);
        });
      }
      setPublications(results);
      setLoading(false);
    }
    fetchAuthoredPubs();
  }, [userDetails]);

  return (
    <div>
      <h4 className="fw-bold mb-4 text-success">My Authored Publications</h4>
      {loading ? (
        <div className="text-center my-4">
          <div className="spinner-border text-success" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      ) : publications.length === 0 ? (
        <div className="alert alert-info text-center">No publications found where you are listed as an author.</div>
      ) : (
        <div className="row g-4">
          {publications.map(pub => (
            <div className="col-12 col-md-6 col-lg-4" key={pub.id}>
              <div className="card h-100 shadow-sm border-0 rounded-4 pub-card position-relative bg-white">
                <div className="card-body p-4 d-flex flex-column justify-content-between">
                  <div>
                    <h5 className="fw-bold mb-2 text-success" style={{fontSize: '1.18rem'}}>
                      <Link to={`/publication/${pub.id}`} className="text-decoration-none text-success">{pub.title}</Link>
                    </h5>
                    <div className="mb-2 text-secondary" style={{fontSize: '0.98rem'}}><FaUsers className="me-2 text-info" />{pub.authors}</div>
                  </div>
                  <div className="d-flex flex-wrap align-items-center gap-2 mt-3">
                    <span className="badge bg-primary bg-opacity-10 text-primary fw-normal"><FaBookOpen className="me-1" />{pub.journal}</span>
                    <span className="badge bg-secondary bg-opacity-10 text-secondary fw-normal"><FaGlobe className="me-1" />{pub.year}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      <style>{`
        .pub-card {
          transition: box-shadow 0.2s, transform 0.2s;
        }
        .pub-card:hover {
          box-shadow: 0 8px 32px rgba(0,0,0,0.10);
          transform: translateY(-4px) scale(1.03);
        }
      `}</style>
    </div>
  );
} 