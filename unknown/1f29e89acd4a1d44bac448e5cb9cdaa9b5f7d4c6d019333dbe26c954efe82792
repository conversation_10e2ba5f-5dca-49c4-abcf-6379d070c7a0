import React from 'react';
import { FaUsers, FaLaptopCode, FaBullhorn, FaPenNib, FaHandshake, FaGraduationCap, FaEnvelope, FaStar, FaQuoteLeft } from 'react-icons/fa';
import { Helmet } from 'react-helmet';

function Careers() {
  return (
    <div className="careers-page">
      <Helmet>
        <title>Careers - darsgah-e-ahlebait</title>
        <meta name="description" content="Explore career opportunities at darsgah-e-ahlebait." />
      </Helmet>
      <div className="container">
      <h2 className="mb-4 text-center fw-bold">Careers</h2>
      <div className="row g-4 mb-4">
        <div className="col-md-6">
          <div className="card shadow-sm h-100 border-0">
            <div className="card-body">
              <h4 className="fw-bold text-primary mb-2"><FaHandshake className="me-2" />Why Work With Us?</h4>
              <p>At Darsgah-e-Ahlebait, you’ll join a passionate team dedicated to advancing science, education, and the integration of faith and knowledge. We value diversity, innovation, and a collaborative spirit. Our work environment is flexible, supportive, and mission-driven.</p>
            </div>
          </div>
        </div>
        <div className="col-md-6">
          <div className="card shadow-sm h-100 border-0 bg-success bg-opacity-10">
            <div className="card-body">
              <h4 className="fw-bold text-success mb-2"><FaStar className="me-2" />Day in the Life</h4>
              <p>"Every day brings new opportunities to learn, collaborate, and make a difference. Whether reviewing manuscripts, building new features, or connecting with scholars, our team is always growing."</p>
              <div className="text-end text-muted small"><FaQuoteLeft className="me-1" />Editorial Team Member</div>
            </div>
          </div>
        </div>
      </div>
      <div className="row g-4 mb-4">
        <div className="col-md-6">
          <div className="card shadow-sm h-100 border-0">
            <div className="card-body">
              <h4 className="fw-bold text-info mb-2"><FaUsers className="me-2" />Open Positions</h4>
              <ul>
                <li><FaPenNib className="me-2 text-primary" /> <b>Editorial Assistant</b> – Coordinate manuscript processing, communicate with authors and reviewers, and support the editorial board.</li>
                <li><FaLaptopCode className="me-2 text-success" /> <b>Web Developer</b> – Maintain and enhance our online platform, ensuring a seamless user experience for researchers and readers.</li>
                <li><FaBullhorn className="me-2 text-warning" /> <b>Outreach Coordinator</b> – Build partnerships, organize events, and promote our journals to the global academic community.</li>
                <li><FaPenNib className="me-2 text-danger" /> <b>Content Writer</b> – Create engaging articles, blog posts, and educational materials for our website and publications.</li>
              </ul>
            </div>
          </div>
        </div>
        <div className="col-md-6">
          <div className="card shadow-sm h-100 border-0">
            <div className="card-body">
              <h4 className="fw-bold text-warning mb-2"><FaGraduationCap className="me-2" />Volunteering & Internships</h4>
              <p>We offer volunteer and internship opportunities for students and early-career professionals. Gain hands-on experience in publishing, research, and science communication. Roles include:</p>
              <ul>
                <li>Peer Reviewer</li>
                <li>Social Media Intern</li>
                <li>Event Organizer</li>
                <li>Editorial Intern</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div className="row g-4 mb-4">
        <div className="col-md-6">
          <div className="card shadow-sm h-100 border-0 bg-light">
            <div className="card-body">
              <h4 className="fw-bold text-secondary mb-2"><FaEnvelope className="me-2" />How to Apply</h4>
              <p>Send your CV and a cover letter to <a href="mailto:<EMAIL>"><EMAIL></a>. Please specify the position or area of interest in your email subject. Our team will review your application and contact you for next steps.</p>
              <div className="alert alert-info mt-3">Tip: Highlight your relevant experience and why you want to join our mission!</div>
            </div>
          </div>
        </div>
        <div className="col-md-6">
          <div className="card shadow-sm h-100 border-0 bg-light">
            <div className="card-body">
              <h4 className="fw-bold text-success mb-2"><FaStar className="me-2" />Benefits</h4>
              <ul>
                <li>Flexible remote work options</li>
                <li>Opportunities for professional growth and networking</li>
                <li>Mentorship from experienced scholars and editors</li>
                <li>Certificate of experience for volunteers and interns</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div className="row g-4 mb-4">
        <div className="col-md-12">
          <div className="card shadow-sm border-0 bg-light">
            <div className="card-body d-flex align-items-center">
              <FaQuoteLeft className="me-3 text-primary" size={32} />
              <div>
                <h5 className="fw-bold mb-1">What Our Team Says</h5>
                <p className="mb-0">“Working here has given me the chance to grow professionally and contribute to a meaningful mission. The team is supportive and the work is rewarding!”</p>
                <div className="text-end text-muted small">— Current Volunteer</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  );
}

export default Careers; 