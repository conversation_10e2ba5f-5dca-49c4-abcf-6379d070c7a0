import React, { useEffect, useState } from 'react';
import { supabase } from './supabaseClient';

const ROLES = [
  'admin',
  'editor',
  'reviewer',
  'editor_in_chief',
];

export default function EditorialBoardManager() {
  const [journalType, setJournalType] = useState('JBS');
  const [members, setMembers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editMember, setEditMember] = useState(null);
  const [editForm, setEditForm] = useState({});
  const [saving, setSaving] = useState(false);
  const [addForm, setAddForm] = useState({
    name: '',
    organization: '',
    country: '',
    bio: '',
    research_interests: '',
    profile_image_url: '',
    role: '',
    journal_type: journalType,
  });
  const [adding, setAdding] = useState(false);
  const [addError, setAddError] = useState('');

  useEffect(() => {
    fetchMembers();
    // eslint-disable-next-line
  }, [journalType]);

  async function fetchMembers() {
    setLoading(true);
    const { data } = await supabase
      .from('editorial_board')
      .select('*')
      .eq('journal_type', journalType.toLowerCase());
    setMembers(data || []);
    setLoading(false);
  }

  function handleEditChange(e) {
    setEditForm({ ...editForm, [e.target.name]: e.target.value });
  }

  function handleAddChange(e) {
    setAddForm({ ...addForm, [e.target.name]: e.target.value });
  }

  function openEditModal(member) {
    setEditMember(member);
    setEditForm({ ...member });
  }

  function closeEditModal() {
    setEditMember(null);
    setEditForm({});
  }

  async function handleEditSave() {
    setSaving(true);
    await supabase
      .from('editorial_board')
      .update(editForm)
      .eq('id', editMember.id);
    setSaving(false);
    closeEditModal();
    fetchMembers();
  }

  async function handleAddMember(e) {
    e.preventDefault();
    setAdding(true);
    setAddError('');
    const { error } = await supabase
      .from('editorial_board')
      .insert([{ ...addForm, journal_type: journalType.toLowerCase() }]);
    setAdding(false);
    if (error) {
      setAddError(error.message);
      return;
    }
    setAddForm({
      name: '',
      organization: '',
      country: '',
      bio: '',
      research_interests: '',
      profile_image_url: '',
      role: '',
      journal_type: journalType.toLowerCase(),
    });
    fetchMembers();
  }

  // Group members by role
  const grouped = ROLES.reduce((acc, role) => {
    acc[role] = members.filter(m => (m.role || '').toLowerCase() === role);
    return acc;
  }, {});

  return (
    <div className="container mt-4">
      <h2 className="mb-4">Editorial Board Manager</h2>
      <div className="mb-4">
        <label className="form-label me-2">Select Journal:</label>
        <select className="form-select d-inline-block w-auto" value={journalType} onChange={e => setJournalType(e.target.value)}>
          <option value="jbs">JBS</option>
          <option value="jns">JNS</option>
          <option value="jis">JIS</option>
        </select>
      </div>
      <div className="mb-5">
        <h5>Add Editorial Board Member</h5>
        <form className="row g-2 align-items-end" onSubmit={handleAddMember}>
          <div className="col-md-2"><input className="form-control" name="name" placeholder="Name" value={addForm.name} onChange={handleAddChange} required /></div>
          <div className="col-md-2"><input className="form-control" name="organization" placeholder="Organization" value={addForm.organization} onChange={handleAddChange} /></div>
          <div className="col-md-2"><input className="form-control" name="country" placeholder="Country" value={addForm.country} onChange={handleAddChange} /></div>
          <div className="col-md-2"><input className="form-control" name="profile_image_url" placeholder="Image URL" value={addForm.profile_image_url} onChange={handleAddChange} /></div>
          <div className="col-md-2"><input className="form-control" name="bio" placeholder="Bio" value={addForm.bio} onChange={handleAddChange} /></div>
          <div className="col-md-2"><input className="form-control" name="research_interests" placeholder="Research Interests" value={addForm.research_interests} onChange={handleAddChange} /></div>
          <div className="col-md-2">
            <select className="form-select" name="role" value={addForm.role} onChange={handleAddChange} required>
              <option value="">Role</option>
              {ROLES.map(r => <option value={r} key={r}>{r === 'editor_in_chief' ? 'Editor in Chief' : r.charAt(0).toUpperCase() + r.slice(1)}</option>)}
            </select>
          </div>
          <div className="col-md-2"><button className="btn btn-success w-100" type="submit" disabled={adding}>{adding ? 'Adding...' : 'Add'}</button></div>
        </form>
        {addError && <div className="alert alert-danger mt-2">{addError}</div>}
      </div>
      {loading ? (
        <div className="text-center my-5"><div className="spinner-border text-primary" role="status"><span className="visually-hidden">Loading...</span></div></div>
      ) : (
        ROLES.map(role => (
          <div key={role} className="mb-5">
            <h4 className="mb-3 text-capitalize">{role === 'editor_in_chief' ? 'Editor in Chief' : role.charAt(0).toUpperCase() + role.slice(1)}</h4>
            {grouped[role].length === 0 ? (
              <div className="alert alert-info">No {role} found for this journal.</div>
            ) : (
              <div className="table-responsive">
                <table className="table table-bordered align-middle">
                  <thead className="table-light">
                    <tr>
                      <th>Name</th>
                      <th>Organization</th>
                      <th>Country</th>
                      <th>Bio</th>
                      <th>Research Interests</th>
                      <th>Image</th>
                      <th>Order</th>
                      <th>Created At</th>
                      <th>Updated At</th>
                      <th>Roles (Array)</th>
                      <th>Role</th>
                      <th>Roles JSON</th>
                      <th>Edit</th>
                    </tr>
                  </thead>
                  <tbody>
                    {grouped[role].map(member => (
                      <tr key={member.id}>
                        <td>{member.name}</td>
                        <td>{member.organization}</td>
                        <td>{member.country}</td>
                        <td>{member.bio}</td>
                        <td>{member.research_interests}</td>
                        <td>{member.profile_image_url ? <img src={member.profile_image_url} alt="profile" style={{width:40, height:40, objectFit:'cover', borderRadius: '50%'}} /> : '-'}</td>
                        <td>{member.order}</td>
                        <td>{member.created_at ? new Date(member.created_at).toLocaleString() : ''}</td>
                        <td>{member.updated_at ? new Date(member.updated_at).toLocaleString() : ''}</td>
                        <td>{Array.isArray(member.roles) ? member.roles.join(', ') : ''}</td>
                        <td>{member.role}</td>
                        <td>{member.roles_json}</td>
                        <td><button className="btn btn-outline-primary btn-sm" onClick={() => openEditModal(member)}>Edit</button></td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        ))
      )}
      {/* Edit Modal */}
      {editMember && (
        <div className="modal fade show" style={{display:'block'}} tabIndex="-1" role="dialog">
          <div className="modal-dialog modal-lg" role="document">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Edit Editorial Board Member</h5>
                <button type="button" className="btn-close" onClick={closeEditModal}></button>
              </div>
              <div className="modal-body">
                <div className="row g-2">
                  <div className="col-md-4 mb-2"><input className="form-control" name="name" placeholder="Name" value={editForm.name || ''} onChange={handleEditChange} /></div>
                  <div className="col-md-4 mb-2"><input className="form-control" name="organization" placeholder="Organization" value={editForm.organization || ''} onChange={handleEditChange} /></div>
                  <div className="col-md-4 mb-2"><input className="form-control" name="country" placeholder="Country" value={editForm.country || ''} onChange={handleEditChange} /></div>
                  <div className="col-md-4 mb-2"><input className="form-control" name="profile_image_url" placeholder="Image URL" value={editForm.profile_image_url || ''} onChange={handleEditChange} /></div>
                  <div className="col-md-4 mb-2"><input className="form-control" name="bio" placeholder="Bio" value={editForm.bio || ''} onChange={handleEditChange} /></div>
                  <div className="col-md-4 mb-2"><input className="form-control" name="research_interests" placeholder="Research Interests" value={editForm.research_interests || ''} onChange={handleEditChange} /></div>
                  <div className="col-md-4 mb-2">
                    <select className="form-select" name="role" value={editForm.role || ''} onChange={handleEditChange} required>
                      <option value="">Role</option>
                      {ROLES.map(r => <option value={r} key={r}>{r === 'editor_in_chief' ? 'Editor in Chief' : r.charAt(0).toUpperCase() + r.slice(1)}</option>)}
                    </select>
                  </div>
                  <div className="col-md-4 mb-2"><input className="form-control" name="order" placeholder="Order" value={editForm.order || ''} onChange={handleEditChange} /></div>
                  <div className="col-md-4 mb-2"><input className="form-control" name="roles" placeholder="Roles (comma separated)" value={Array.isArray(editForm.roles) ? editForm.roles.join(', ') : (editForm.roles || '')} onChange={e => setEditForm({ ...editForm, roles: e.target.value.split(',').map(s => s.trim()) })} /></div>
                  <div className="col-md-4 mb-2"><input className="form-control" name="roles_json" placeholder="Roles JSON" value={editForm.roles_json || ''} onChange={handleEditChange} /></div>
                </div>
              </div>
              <div className="modal-footer">
                <button type="button" className="btn btn-secondary" onClick={closeEditModal}>Cancel</button>
                <button type="button" className="btn btn-primary" onClick={handleEditSave} disabled={saving}>{saving ? 'Saving...' : 'Save Changes'}</button>
              </div>
            </div>
          </div>
          <div className="modal-backdrop fade show"></div>
        </div>
      )}
    </div>
  );
} 