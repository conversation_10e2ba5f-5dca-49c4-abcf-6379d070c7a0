// Simple test for web crawler
const { WebCitationCrawler } = require('./web_crawler');

async function testCrawler() {
  console.log('🧪 Testing Web Crawler...');
  
  try {
    const crawler = new WebCitationCrawler();
    
    // Test search function
    console.log('\n1. Testing DuckDuckGo search...');
    const results = await crawler.searchDuckDuckGo('machine learning', 3);
    console.log(`✅ Search results: ${results.length}`);
    
    if (results.length > 0) {
      console.log(`📋 First result: ${results[0].title}`);
    }
    
    // Test citation analysis
    console.log('\n2. Testing citation analysis...');
    if (results.length > 0) {
      const citation = crawler.analyzeCitation(
        results[0], 
        'machine learning', 
        '<PERSON>'
      );
      console.log(`📊 Confidence score: ${citation.confidenceScore}`);
    }
    
    console.log('\n✅ Basic crawler test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testCrawler();
