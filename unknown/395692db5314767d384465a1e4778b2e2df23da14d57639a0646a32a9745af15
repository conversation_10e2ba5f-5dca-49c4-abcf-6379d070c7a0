# Blog Comments System Setup

This guide explains how to set up the comments system for the blog posts.

## Database Setup

1. **Run the SQL script** in your Supabase SQL editor:
   ```sql
   -- Copy and paste the contents of database/blog_comments_table.sql
   ```

2. **Verify the table was created** by checking your Supabase dashboard:
   - Go to Table Editor
   - Look for the `blog_comments` table
   - Verify it has the following columns:
     - `id` (UUID, Primary Key)
     - `post_id` (UUID, Foreign Key to blog_posts)
     - `user_id` (UUID, Foreign Key to users)
     - `parent_id` (UUID, Self-referencing for replies)
     - `content` (TEXT)
     - `created_at` (TIMESTAMP)
     - `updated_at` (TIMESTAMP)

## Features

The comments system includes the following features:

### ✅ Core Features
- **Add comments** - Users can post comments on blog posts
- **Edit comments** - Users can edit their own comments
- **Delete comments** - Users can delete their own comments
- **Reply to comments** - Users can reply to existing comments (nested replies)
- **Admin moderation** - Admins can delete any comment

### ✅ User Experience
- **Real-time updates** - Comments appear immediately after posting
- **User avatars** - Shows user profile images or default avatar
- **Timestamps** - Shows relative time (e.g., "2 hours ago")
- **Edit indicators** - Shows when a comment has been edited
- **Responsive design** - Works on mobile and desktop

### ✅ Security
- **Row Level Security (RLS)** - Database-level security policies
- **User authentication** - Only logged-in users can comment
- **Ownership validation** - Users can only edit/delete their own comments
- **Admin privileges** - Admins can delete any comment

## Usage

### For Users
1. **View comments** - Comments appear at the bottom of each blog post
2. **Add a comment** - Click the text area and type your comment
3. **Reply to comments** - Click "Reply" on any comment
4. **Edit your comment** - Click "Edit" on your own comments
5. **Delete your comment** - Click "Delete" on your own comments

### For Admins
- Admins can delete any comment using the "Delete" button
- Admins can manage comments through the admin panel (future feature)

## Database Schema

```sql
blog_comments
├── id (UUID, Primary Key)
├── post_id (UUID, Foreign Key to blog_posts)
├── user_id (UUID, Foreign Key to users)
├── parent_id (UUID, Self-referencing for replies)
├── content (TEXT)
├── created_at (TIMESTAMP)
└── updated_at (TIMESTAMP)
```

## Security Policies

The system includes the following Row Level Security policies:

1. **Read Policy** - Anyone can read comments
2. **Insert Policy** - Only authenticated users can insert their own comments
3. **Update Policy** - Users can only update their own comments
4. **Delete Policy** - Users can delete their own comments, admins can delete any comment

## Troubleshooting

### Common Issues

1. **Comments not loading**
   - Check if the `blog_comments` table exists
   - Verify RLS policies are enabled
   - Check browser console for errors

2. **Cannot post comments**
   - Ensure user is logged in
   - Check if user account is not banned
   - Verify RLS insert policy

3. **Cannot edit/delete comments**
   - Ensure user owns the comment
   - Check RLS update/delete policies
   - Verify user permissions

### Database Queries

To check if everything is working:

```sql
-- Check if table exists
SELECT * FROM information_schema.tables WHERE table_name = 'blog_comments';

-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'blog_comments';

-- Test comment insertion (replace with actual values)
INSERT INTO blog_comments (post_id, user_id, content) 
VALUES ('your-post-id', 'your-user-id', 'Test comment');
```

## Future Enhancements

Potential features to add:
- Comment moderation queue
- Comment notifications
- Comment likes/dislikes
- Comment reporting
- Rich text editor for comments
- Comment search functionality
- Comment analytics 