import React, { useEffect, useState } from 'react';
import { supabase } from './supabaseClient';
import { Link } from 'react-router-dom';
import { FaBookOpen, FaCalendarAlt, FaHeart } from 'react-icons/fa';

export default function ProfileFavorites() {
  const [user, setUser] = useState(null);
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) setUser(JSON.parse(storedUser));
  }, []);

  useEffect(() => {
    async function fetchFavorites() {
      if (!user) return;
      setLoading(true);
      // Get all favorite publication IDs for this user
      const { data: favs } = await supabase
        .from('favorites')
        .select('publication_id, created_at')
        .eq('user_id', user.id);
      if (!favs || favs.length === 0) {
        setFavorites([]);
        setLoading(false);
        return;
      }
      const pubIds = favs.map(f => f.publication_id);
      // Fetch publication details
      const { data: pubs } = await supabase
        .from('publications')
        .select('*')
        .in('id', pubIds);
      // Merge favorite created_at with publication info
      const favList = pubs.map(pub => ({
        ...pub,
        favorited_at: favs.find(f => f.publication_id === pub.id)?.created_at
      }));
      setFavorites(favList);
      setLoading(false);
    }
    if (user) fetchFavorites();
  }, [user]);

  if (!user) return <div className="container mt-5"><div className="alert alert-warning">You must be logged in to view your favorites.</div></div>;
  if (loading) return <div className="container mt-5 text-center"><div className="spinner-border text-primary" role="status"><span className="visually-hidden">Loading...</span></div></div>;

  return (
    <div className="container mt-5" style={{maxWidth: 900}}>
      <h3 className="mb-4">My Favorites & Bookmarks</h3>
      {favorites.length === 0 ? (
        <div className="alert alert-info">You have not saved any publications to favorites yet.</div>
      ) : (
        <div className="row g-4">
          {favorites.map(pub => (
            <div className="col-12 col-md-6 col-lg-4" key={pub.id}>
              <div className="card shadow-sm border-0 rounded-4 h-100">
                <div className="card-body d-flex flex-column">
                  <div className="d-flex align-items-center mb-2">
                    <FaHeart className="text-danger me-2" />
                    <span className="text-muted small">Favorited: {pub.favorited_at ? new Date(pub.favorited_at).toLocaleString() : '-'}</span>
                  </div>
                  <h5 className="fw-bold mb-2 text-primary" style={{minHeight:48}}>{pub.title}</h5>
                  <div className="mb-2 text-secondary" style={{fontSize:'1.05rem'}}>{pub.authors}</div>
                  <div className="mb-2 d-flex flex-wrap gap-2 align-items-center">
                    <span className="badge bg-primary bg-opacity-10 text-primary fw-normal"><FaBookOpen className="me-1" />{pub.journal}</span>
                    <span className="badge bg-secondary bg-opacity-10 text-secondary fw-normal"><FaCalendarAlt className="me-1" />{pub.year}</span>
                  </div>
                  <div className="mt-auto d-flex justify-content-end">
                    <Link to={`/publication/${pub.id}`} className="btn btn-outline-primary btn-sm">View</Link>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 