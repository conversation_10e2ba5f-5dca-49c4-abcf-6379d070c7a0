// Test script to check impact factor calculation
const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://rbvgtaqimzpsarvoxubn.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJidmd0YXFpbXpwc2Fydm94dWJuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjAxMDI0MSwiZXhwIjoyMDYxNTg2MjQxfQ.Y1Xcvq50peqVvgRhVVtHP7eEvpGBn-A2EG65H--8XX8';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testImpactFactor() {
  console.log('🧪 Testing Impact Factor Calculation...\n');

  try {
    // 1. Check JBS publications
    console.log('📚 Fetching JBS publications...');
    const { data: jbsPublications, error: jbsError } = await supabase
      .from('publications')
      .select('id, title, citation_count, journal')
      .eq('journal', 'JBS');

    if (jbsError) {
      console.error('❌ Error fetching JBS publications:', jbsError);
      return;
    }

    console.log(`✅ Found ${jbsPublications.length} JBS publications`);
    
    // 2. Check external citations table exists
    console.log('\n🔍 Checking external_citations table...');
    const { data: externalCitations, error: extError } = await supabase
      .from('external_citations')
      .select('*')
      .limit(5);

    if (extError) {
      console.error('❌ External citations table error:', extError);
      console.log('💡 You need to run the SQL setup first!');
      return;
    }

    console.log(`✅ External citations table exists with ${externalCitations.length} sample records`);

    // 3. Calculate impact factor for JBS
    if (jbsPublications.length > 0) {
      const jbsIds = jbsPublications.map(pub => pub.id);
      
      const { data: jbsCitations, error: citError } = await supabase
        .from('external_citations')
        .select('id, publication_id')
        .in('publication_id', jbsIds)
        .eq('is_active', true);

      if (citError) {
        console.error('❌ Error fetching JBS citations:', citError);
        return;
      }

      const internalCitations = jbsPublications.reduce((total, pub) => total + (pub.citation_count || 0), 0);
      const externalCitations = jbsCitations ? jbsCitations.length : 0;
      const totalCitations = internalCitations + externalCitations;
      const impactFactor = (totalCitations / jbsPublications.length).toFixed(2);

      console.log('\n📊 JBS Impact Factor Calculation:');
      console.log(`   Publications: ${jbsPublications.length}`);
      console.log(`   Internal Citations: ${internalCitations}`);
      console.log(`   External Citations: ${externalCitations}`);
      console.log(`   Total Citations: ${totalCitations}`);
      console.log(`   Impact Factor: ${impactFactor}`);
    }

    // 4. Check JNS publications
    console.log('\n📚 Fetching JNS publications...');
    const { data: jnsPublications, error: jnsError } = await supabase
      .from('publications')
      .select('id, title, citation_count, journal')
      .eq('journal', 'JNS');

    if (jnsError) {
      console.error('❌ Error fetching JNS publications:', jnsError);
      return;
    }

    console.log(`✅ Found ${jnsPublications.length} JNS publications`);

    // 5. Calculate impact factor for JNS
    if (jnsPublications.length > 0) {
      const jnsIds = jnsPublications.map(pub => pub.id);
      
      const { data: jnsCitations, error: citError } = await supabase
        .from('external_citations')
        .select('id, publication_id')
        .in('publication_id', jnsIds)
        .eq('is_active', true);

      if (citError) {
        console.error('❌ Error fetching JNS citations:', citError);
        return;
      }

      const internalCitations = jnsPublications.reduce((total, pub) => total + (pub.citation_count || 0), 0);
      const externalCitations = jnsCitations ? jnsCitations.length : 0;
      const totalCitations = internalCitations + externalCitations;
      const impactFactor = (totalCitations / jnsPublications.length).toFixed(2);

      console.log('\n📊 JNS Impact Factor Calculation:');
      console.log(`   Publications: ${jnsPublications.length}`);
      console.log(`   Internal Citations: ${internalCitations}`);
      console.log(`   External Citations: ${externalCitations}`);
      console.log(`   Total Citations: ${totalCitations}`);
      console.log(`   Impact Factor: ${impactFactor}`);
    }

    console.log('\n✅ Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testImpactFactor();
