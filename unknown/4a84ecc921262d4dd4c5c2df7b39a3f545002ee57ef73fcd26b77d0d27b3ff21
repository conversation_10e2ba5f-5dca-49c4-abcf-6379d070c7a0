import React, { useState } from 'react';
import { Helmet } from 'react-helmet';

function Contact() {
  const [form, setForm] = useState({ name: '', email: '', message: '' });
  const [submitted, setSubmitted] = useState(false);

  const handleChange = e => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = e => {
    e.preventDefault();
    setSubmitted(true);
  };

  return (
    <div className="contact-page">
      <Helmet>
        <title>Contact - darsgah-e-ahlebait</title>
        <meta name="description" content="Contact darsgah-e-ahlebait for inquiries, submissions, and support." />
      </Helmet>
      <div className="row justify-content-center">
        <div className="col-md-7">
          <div className="card shadow-sm border-0">
            <div className="card-body">
              <h2 className="mb-4 text-center">Contact Us</h2>
              {submitted ? (
                <div className="alert alert-success text-center">Thank you for contacting us!</div>
              ) : (
                <form onSubmit={handleSubmit}>
                  <div className="mb-3">
                    <label className="form-label">Name</label>
                    <input type="text" className="form-control" name="name" value={form.name} onChange={handleChange} required />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Email</label>
                    <input type="email" className="form-control" name="email" value={form.email} onChange={handleChange} required />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Message</label>
                    <textarea className="form-control" name="message" rows="5" value={form.message} onChange={handleChange} required></textarea>
                  </div>
                  <div className="d-grid">
                    <button type="submit" className="btn btn-primary">Send Message</button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Contact; 