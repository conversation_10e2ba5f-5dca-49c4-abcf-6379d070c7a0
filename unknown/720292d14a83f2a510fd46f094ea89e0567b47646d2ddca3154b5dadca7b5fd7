#!/usr/bin/env node

/**
 * Quick Start Script for Testing Citation Crawler APIs
 * This creates a minimal server to test the enhanced Citation Manager UI
 */

const express = require('express');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

const app = express();
const PORT = process.env.PORT || 3001;

// Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://rbvgtaqimzpsarvoxubn.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJidmd0YXFpbXpwc2Fydm94dWJuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjAxMDI0MSwiZXhwIjoyMDYxNTg2MjQxfQ.Y1Xcvq50peqVvgRhVVtHP7eEvpGBn-A2EG65H--8XX8';

let supabase;
try {
  supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
} catch (error) {
  console.error('Supabase connection error:', error.message);
}

app.use(express.json());

// CORS middleware for development
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:3000');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Mock citation crawler (for testing without Python setup)
const { runManualCrawl } = (() => {
  try {
    return require('./citation_crawler');
  } catch (error) {
    console.log('⚠️  Python citation crawler not available, using mock implementation');
    return {
      runManualCrawl: async () => {
        console.log('Mock internal crawl completed');
        return { message: 'Mock crawl completed' };
      }
    };
  }
})();

// Internal citation crawling endpoint
app.post('/api/crawl-citations', async (req, res) => {
  try {
    console.log('Starting internal citation crawl...');
    await runManualCrawl();
    res.status(200).json({ 
      message: 'Internal citation crawling completed successfully!',
      type: 'internal',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Internal citation crawl error:', error);
    res.status(500).json({ 
      error: error.message || 'Internal citation crawling failed',
      timestamp: new Date().toISOString()
    });
  }
});

// Web citation crawling endpoint (mock for now)
app.post('/api/crawl-web-citations', async (req, res) => {
  try {
    const { publicationId, async: isAsync = true } = req.body;
    
    console.log('Starting web citation crawl...');
    
    // Mock implementation - replace with actual Python crawler call
    setTimeout(() => {
      console.log('Mock web crawl completed');
    }, 2000);
    
    res.status(200).json({
      message: 'Web citation crawl started (mock implementation)',
      type: 'web_external',
      async: isAsync,
      publicationId: publicationId || null,
      note: 'This is a mock implementation. Install Python dependencies for full functionality.',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Web citation crawl error:', error);
    res.status(500).json({
      error: error.message || 'Web citation crawling failed',
      timestamp: new Date().toISOString()
    });
  }
});

// Get external citations for a publication (mock data)
app.get('/api/publications/:publicationId/citations', async (req, res) => {
  try {
    const { publicationId } = req.params;
    
    // Mock citations data
    const mockCitations = [
      {
        id: 'mock-citation-1',
        source_url: 'https://example.com/article1',
        source_title: 'Example Article Citing Your Work',
        source_domain: 'example.com',
        citation_context: 'According to the research by [authors], this study demonstrates...',
        citation_type: 'mention',
        confidence_score: 0.85,
        is_verified: false,
        found_date: new Date().toISOString()
      },
      {
        id: 'mock-citation-2',
        source_url: 'https://scholar.google.com/article2',
        source_title: 'Academic Paper Reference',
        source_domain: 'scholar.google.com',
        citation_context: 'This work builds upon the findings of [publication title]...',
        citation_type: 'reference',
        confidence_score: 0.92,
        is_verified: true,
        found_date: new Date().toISOString()
      }
    ];
    
    res.json({
      publicationId: publicationId,
      citations: mockCitations,
      count: mockCitations.length,
      note: 'Mock data - install full system for real citations',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error fetching citations:', error);
    res.status(500).json({
      error: 'Failed to fetch citations',
      details: error.message
    });
  }
});

// Verify citation endpoint (mock)
app.patch('/api/citations/:citationId/verify', async (req, res) => {
  try {
    const { citationId } = req.params;
    const { verified } = req.body;
    
    res.json({
      message: `Citation ${verified ? 'verified' : 'rejected'} successfully (mock)`,
      citationId: citationId,
      verified: verified,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error verifying citation:', error);
    res.status(500).json({
      error: 'Failed to verify citation',
      details: error.message
    });
  }
});

// Get crawling statistics (mock)
app.get('/api/crawl-stats', async (req, res) => {
  try {
    const mockStats = [
      {
        id: 'mock-stat-1',
        timestamp: new Date().toISOString(),
        crawl_type: 'internal',
        total_publications: 10,
        total_citations_found: 25,
        processing_time: 5000,
        errors: 0
      },
      {
        id: 'mock-stat-2',
        timestamp: new Date(Date.now() - 86400000).toISOString(),
        crawl_type: 'external',
        total_publications: 10,
        external_citations_found: 15,
        processing_time: 12000,
        errors: 1
      }
    ];
    
    res.json({
      stats: mockStats,
      count: mockStats.length,
      note: 'Mock data - install full system for real statistics',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error fetching crawl stats:', error);
    res.status(500).json({
      error: 'Failed to fetch crawl statistics',
      details: error.message
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'Enhanced Citation Crawler API is running',
    timestamp: new Date().toISOString(),
    mode: 'development'
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Enhanced Citation Crawler API Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔗 CORS enabled for: http://localhost:3000`);
  console.log('');
  console.log('Available endpoints:');
  console.log(`  POST http://localhost:${PORT}/api/crawl-citations`);
  console.log(`  POST http://localhost:${PORT}/api/crawl-web-citations`);
  console.log(`  GET  http://localhost:${PORT}/api/publications/:id/citations`);
  console.log(`  GET  http://localhost:${PORT}/api/crawl-stats`);
  console.log('');
  console.log('⚠️  Note: This is a development server with mock data.');
  console.log('   Install Python dependencies for full functionality.');
});
