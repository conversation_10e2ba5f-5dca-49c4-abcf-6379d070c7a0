import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { supabase } from './supabaseClient';
import { FaDownload, FaShareAlt, FaFilePdf, FaUser, FaBookOpen, FaCalendarAlt, FaHashtag, FaArrowLeft, FaHeart, FaRegHeart } from 'react-icons/fa';
// Removed PDF viewer imports
import * as pdfjsLib from 'pdfjs-dist/build/pdf';
import { GlobalWorkerOptions } from 'pdfjs-dist/build/pdf';
import PublicationMetaTags from './components/PublicationMetaTags';
GlobalWorkerOptions.workerSrc = '//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.worker.min.js';

function PublicationDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [publication, setPublication] = useState(null);
  const [loading, setLoading] = useState(true);
  const [shareOpen, setShareOpen] = useState(false);
  const [favorite, setFavorite] = useState(false);
  const [favLoading, setFavLoading] = useState(false);
  const [user, setUser] = useState(null);
  const [references, setReferences] = useState([]);
  const [extractingRefs, setExtractingRefs] = useState(false);
  const [relatedArticles, setRelatedArticles] = useState([]);

  useEffect(() => {
    async function fetchPublication() {
      setLoading(true);
      const { data, error } = await supabase
        .from('publications')
        .select('*')
        .eq('id', id)
        .single();
      if (!error) setPublication(data);
      setLoading(false);
    }
    fetchPublication();
  }, [id]);

  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) setUser(JSON.parse(storedUser));
  }, []);

  useEffect(() => {
    async function fetchFavorite() {
      if (!user || !id) return;
      const { data } = await supabase
        .from('favorites')
        .select('id')
        .eq('user_id', user.id)
        .eq('publication_id', id)
        .single();
      setFavorite(!!data);
    }
    if (user && id) fetchFavorite();
  }, [user, id]);

  useEffect(() => {
    async function extractReferences() {
      if (!publication || !publication.pdf_url) return;
      setExtractingRefs(true);
      try {
        const url = getPdfUrl(publication);
        const loadingTask = pdfjsLib.getDocument(url);
        const pdf = await loadingTask.promise;
        let text = '';
        // Extract text from the last 2 pages
        for (let i = Math.max(1, pdf.numPages - 1); i <= pdf.numPages; i++) {
          const page = await pdf.getPage(i);
          const content = await page.getTextContent();
          text += content.items.map(item => item.str).join(' ') + '\n';
        }
        // Find the references section
        const lines = text.split(/\n|\r/).map(l => l.trim());
        const refIdx = lines.findIndex(l => /references?/i.test(l));
        let refs = [];
        if (refIdx !== -1) {
          refs = lines.slice(refIdx + 1).filter(l => l.length > 5);
        }
        setReferences(refs);
      } catch (err) {
        setReferences([]);
      }
      setExtractingRefs(false);
    }
    extractReferences();
    // eslint-disable-next-line
  }, [publication]);

  useEffect(() => {
    async function fetchOtherArticles() {
      if (!publication) return;
      const { data } = await supabase
        .from('publications')
        .select('id, title, authors, journal, year')
        .neq('id', publication.id)
        .limit(5);
      setRelatedArticles(data || []);
    }
    fetchOtherArticles();
  }, [publication]);

  const getPdfUrl = (pub) => {
    if (!pub || !pub.pdf_url) return null;
    // If already a full URL, return as is; else, build Supabase storage URL
    if (pub.pdf_url.startsWith('http')) return pub.pdf_url;
    return `https://rbvgtaqimzpsarvoxubn.supabase.co/storage/v1/object/public/pdf/${pub.pdf_url.split('/').pop()}`;
  };

  const handleShare = () => {
    setShareOpen(true);
    if (navigator.share) {
      navigator.share({
        title: publication.title,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
    setTimeout(() => setShareOpen(false), 2000);
  };

  const handleToggleFavorite = async () => {
    if (!user) {
      alert('You must be logged in to save favorites.');
      return;
    }
    setFavLoading(true);
    if (favorite) {
      // Remove from favorites
      await supabase
        .from('favorites')
        .delete()
        .eq('user_id', user.id)
        .eq('publication_id', id);
      setFavorite(false);
    } else {
      // Add to favorites
      await supabase
        .from('favorites')
        .insert({ user_id: user.id, publication_id: id });
      setFavorite(true);
    }
    setFavLoading(false);
  };

  // Remove the following line:
  // const pdfPlugin = defaultLayoutPlugin();

  if (loading) {
    return (
      <div className="text-center my-5">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (!publication) {
    return <div className="alert alert-danger mt-4">Publication not found.</div>;
  }

  return (
    <div className="py-4">
      <PublicationMetaTags publication={publication} />
      <div className="container">
        <div className="row g-4">
          {/* Left/Main Content */}
          <div className="col-lg-8">
            <div className="mb-4">
              <div className="p-4">
                <h2 className="fw-bold mb-2 text-primary" style={{fontSize: '2.2rem'}}>{publication.title}</h2>
                <div className="mb-2 text-secondary" style={{fontSize: '1.13rem'}}><FaUser className="me-2 text-info" />{publication.authors}</div>
                <div className="mb-2 d-flex flex-wrap gap-2 align-items-center">
                  <span className="badge bg-dark text-white px-3 py-2" style={{fontSize: '1rem', letterSpacing: '0.5px'}}>
                    <FaBookOpen className="me-1" />Journal: <span className="fw-normal ms-1">{publication.journal}</span>
                  </span>
                  <span className="badge bg-secondary text-white px-3 py-2" style={{fontSize: '1rem', letterSpacing: '0.5px'}}>
                    <FaCalendarAlt className="me-1" />Year: <span className="fw-normal ms-1">{publication.year}</span>
                  </span>
                  {publication.keywords && publication.keywords.split(',').map((kw, idx) => (
                    <span key={idx} className="badge bg-info text-dark px-3 py-2" style={{fontSize: '1rem', letterSpacing: '0.5px', background: '#e3f6fc', color: '#0a3d62', border: '1px solid #b2ebf2', marginRight: '0.25rem'}}>
                      {kw.trim()}
                    </span>
                  ))}
                  {publication.doi && <span className="badge bg-light text-dark border px-3 py-2" style={{fontSize: '1rem'}}><FaHashtag className="me-1" />{publication.doi}</span>}
                </div>
                <hr />
                <div className="mb-3">
                  <h5 className="fw-bold mb-2">Abstract</h5>
                  <div style={{fontSize: '1.13rem', color: '#333', padding: 0, marginBottom: '0.5rem'}}>
                    {publication.abstract}
                  </div>
                </div>
                <div className="row g-3 mb-2">
                  <div className="col-md-4"><b>Volume:</b> {publication.volume}</div>
                  <div className="col-md-4"><b>Issue:</b> {publication.issue_number}</div>
                  <div className="col-md-4"><b>Citation Count:</b> {publication.citation_count}</div>
                  <div className="col-md-4"><b>Created At:</b> {new Date(publication.created_at).toLocaleString()}</div>
                </div>
                <div className="d-flex flex-row gap-2 align-items-center mt-4 mb-3">
                  {getPdfUrl(publication) && (
                    <a href={getPdfUrl(publication)} target="_blank" rel="noopener noreferrer" className="btn btn-outline-danger d-flex align-items-center gap-2 px-3">
                      <FaFilePdf /> Download PDF
                    </a>
                  )}
                  <button className="btn btn-outline-primary d-flex align-items-center gap-2 px-3" onClick={handleShare} title="Share">
                    <FaShareAlt /> Share
                  </button>
                  <button className={`btn ${favorite ? 'btn-danger' : 'btn-outline-danger'} d-flex align-items-center gap-2 px-3`} onClick={handleToggleFavorite} disabled={favLoading} title={favorite ? 'Remove from Favorites' : 'Save to Favorites'}>
                    {favorite ? <FaHeart /> : <FaRegHeart />} {favorite ? 'Remove from Favorites' : 'Save to Favorites'}
                  </button>
                </div>
                <div className="mt-4">
                  <h5 className="fw-bold mb-2">Citation</h5>
                  <div className="border rounded-3 p-3 bg-light text-dark" style={{fontSize: '1.08rem'}}>
                    {publication.authors} ({publication.year}). <b>{publication.title}</b>. <i>{publication.journal}</i>{publication.volume ? `, ${publication.volume}` : ''}{publication.issue_number ? `(${publication.issue_number})` : ''}{publication.doi ? `, https://doi.org/${publication.doi}` : ''}
                  </div>
                </div>
                <div className="mt-4">
                  <button className="btn btn-outline-secondary d-flex align-items-center gap-2" onClick={() => navigate(-1)}><FaArrowLeft /> Back</button>
                </div>
                {shareOpen && (
                  <div className="alert alert-success position-fixed top-0 start-50 translate-middle-x mt-3" style={{zIndex: 9999, minWidth: 220}}>
                    Link copied to clipboard!
                  </div>
                )}
              </div>
            </div>
          </div>
          {/* Right Sidebar */}
          <div className="col-lg-4">
            <div className="mb-4">
              <div className="p-4">
                <h5 className="fw-bold mb-3 text-primary">Related Articles</h5>
                <ul className="list-unstyled mb-0">
                  {relatedArticles.length === 0 ? (
                    <li className="mb-2"><span className="text-secondary">No related articles found.</span></li>
                  ) : (
                    relatedArticles.map(article => (
                      <li className="mb-3" key={article.id}>
                        <Link to={`/publication/${article.id}`} className="fw-bold text-decoration-none text-dark">{article.title}</Link>
                        <div className="small text-secondary">{article.authors}</div>
                        <div className="small text-muted">{article.journal} {article.year}</div>
                      </li>
                    ))
                  )}
                </ul>
              </div>
            </div>
            <div className="mb-4">
              <div className="p-4">
                <h5 className="fw-bold mb-3 text-success">Quick Stats</h5>
                <div><b>Citations:</b> {publication.citation_count}</div>
                <div><b>Year:</b> {publication.year}</div>
                <div><b>Journal:</b> {publication.journal}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <style>{`
        .publication-detail-page .card {
          max-width: 100%;
          margin: 0;
        }
      `}</style>
    </div>
  );
}

export default PublicationDetail; 