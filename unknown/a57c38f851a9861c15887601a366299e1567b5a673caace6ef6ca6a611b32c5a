# Comments System Troubleshooting Guide

## Common Errors and Solutions

### Error: "Error submitting comment: Object"
**Cause**: Database table not set up or RLS policies not working
**Solution**:
1. Run the database setup script: `database/blog_comments_table_fixed.sql`
2. Check browser console for specific error details
3. Verify user is logged in and not banned

### Error: "Permission denied" (42501)
**Cause**: Row Level Security (RLS) blocking the operation
**Solution**:
1. Ensure user is authenticated
2. Check RLS policies are correctly set up
3. Verify user has proper permissions

### Error: "Table does not exist" (42P01)
**Cause**: Database table not created
**Solution**:
1. Run the complete database setup script
2. Check if the script executed successfully
3. Verify table exists in Supabase dashboard

### Error: "Foreign key constraint" (23503)
**Cause**: Invalid post_id or user_id reference
**Solution**:
1. Ensure the blog post exists
2. Verify user account is valid
3. Check foreign key constraints are set up

## Debugging Steps

### Step 1: Check Database Setup
Run the test script: `database/test_comments.sql`
This will show:
- Table structure
- RLS policies
- Foreign key constraints
- Indexes

### Step 2: Check Browser Console
Look for detailed error messages in the browser console:
- Error codes (42501, 23503, etc.)
- Specific error messages
- Request/response details

### Step 3: Verify User Authentication
Ensure the user is properly logged in:
```javascript
// Check in browser console
console.log('User:', JSON.parse(localStorage.getItem('user')));
```

### Step 4: Test Database Connection
Try a simple query to test the connection:
```sql
SELECT COUNT(*) FROM blog_comments;
```

## Quick Fixes

### If table doesn't exist:
```sql
-- Run the complete setup
-- Copy and paste database/blog_comments_table_fixed.sql
```

### If RLS policies are wrong:
```sql
-- Drop and recreate policies
DROP POLICY IF EXISTS "Allow users to read comments" ON blog_comments;
DROP POLICY IF EXISTS "Allow users to insert comments" ON blog_comments;
DROP POLICY IF EXISTS "Allow users to update their own comments" ON blog_comments;
DROP POLICY IF EXISTS "Allow users to delete their own comments" ON blog_comments;
DROP POLICY IF EXISTS "Allow admins to delete any comment" ON blog_comments;

-- Recreate policies
CREATE POLICY "Allow users to read comments" ON blog_comments
    FOR SELECT USING (true);

CREATE POLICY "Allow users to insert comments" ON blog_comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users to update their own comments" ON blog_comments
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Allow users to delete their own comments" ON blog_comments
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Allow admins to delete any comment" ON blog_comments
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );
```

### If foreign keys are missing:
```sql
-- Add missing foreign keys
ALTER TABLE blog_comments 
ADD CONSTRAINT IF NOT EXISTS blog_comments_post_id_fkey 
FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE;

ALTER TABLE blog_comments 
ADD CONSTRAINT IF NOT EXISTS blog_comments_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE blog_comments 
ADD CONSTRAINT IF NOT EXISTS blog_comments_parent_id_fkey 
FOREIGN KEY (parent_id) REFERENCES blog_comments(id) ON DELETE CASCADE;
```

## Testing the Setup

1. **Run the test script** in Supabase SQL Editor
2. **Check browser console** for detailed error messages
3. **Try posting a comment** and see what specific error appears
4. **Verify user authentication** is working properly

## Common Issues

### Issue: Comments not loading
- Check if table exists
- Verify RLS policies allow reading
- Check browser console for errors

### Issue: Cannot post comments
- Ensure user is logged in
- Check if user is banned
- Verify RLS insert policy
- Check foreign key constraints

### Issue: Cannot edit/delete comments
- Ensure user owns the comment
- Check RLS update/delete policies
- Verify user permissions

## Getting Help

If you're still having issues:

1. **Check the browser console** for specific error messages
2. **Run the test script** and share the results
3. **Verify the database setup** is complete
4. **Test with a simple comment** to isolate the issue

The improved error handling in the Comments component will now show more specific error messages to help identify the exact issue. 