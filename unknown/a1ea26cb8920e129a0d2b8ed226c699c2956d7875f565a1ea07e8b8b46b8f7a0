import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from './supabaseClient';
import { Helmet } from 'react-helmet';

export default function Login({ setUser }) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .eq('password', password)
      .single();
    if (error || !data) {
      setError('Invalid email or password');
    } else {
      // Check if user is banned
      if (data.is_banned) {
        // Store banned user data and redirect to banned page
        localStorage.setItem('bannedUser', JSON.stringify(data));
        navigate('/banned');
        return;
      }
      
      localStorage.setItem('user', JSON.stringify(data));
      setUser(data);
      navigate('/');
    }
  };

  return (
    <div className="login-page">
      <Helmet>
        <title>Login - darsgah-e-ahlebait</title>
        <meta name="description" content="Login to your darsgah-e-ahlebait account." />
      </Helmet>
      <div className="row justify-content-center">
        <div className="container mt-5" style={{maxWidth: 400}}>
          <h2 className="mb-4">Login</h2>
          <form onSubmit={handleSubmit}>
            <div className="mb-3">
              <label className="form-label">Email</label>
              <input type="email" className="form-control" value={email} onChange={e => setEmail(e.target.value)} required />
            </div>
            <div className="mb-3">
              <label className="form-label">Password</label>
              <input type="password" className="form-control" value={password} onChange={e => setPassword(e.target.value)} required />
            </div>
            {error && <div className="alert alert-danger">{error}</div>}
            <button type="submit" className="btn btn-primary w-100">Login</button>
          </form>
        </div>
      </div>
    </div>
  );
} 