-- Create external_citations table if it doesn't exist
CREATE TABLE IF NOT EXISTS external_citations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    publication_id UUID NOT NULL REFERENCES publications(id) ON DELETE CASCADE,
    source_url TEXT NOT NULL,
    source_title TEXT,
    source_domain TEXT,
    citation_context TEXT,
    citation_type VARCHAR(50) DEFAULT 'mention',
    confidence_score DECIMAL(3,2) DEFAULT 0.5,
    found_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_verified TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add external_citation_count column to publications table if it doesn't exist
ALTER TABLE publications ADD COLUMN IF NOT EXISTS external_citation_count INTEGER DEFAULT 0;
ALTER TABLE publications ADD COLUMN IF NOT EXISTS last_crawled TIMESTAMP WITH TIME ZONE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_external_citations_publication_id ON external_citations(publication_id);
CREATE INDEX IF NOT EXISTS idx_external_citations_is_active ON external_citations(is_active);
CREATE INDEX IF NOT EXISTS idx_external_citations_confidence ON external_citations(confidence_score);

-- Create function to update external citation count
CREATE OR REPLACE FUNCTION update_external_citation_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE publications 
        SET external_citation_count = (
            SELECT COUNT(*) 
            FROM external_citations 
            WHERE publication_id = NEW.publication_id AND is_active = TRUE
        )
        WHERE id = NEW.publication_id;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        UPDATE publications 
        SET external_citation_count = (
            SELECT COUNT(*) 
            FROM external_citations 
            WHERE publication_id = NEW.publication_id AND is_active = TRUE
        )
        WHERE id = NEW.publication_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE publications 
        SET external_citation_count = (
            SELECT COUNT(*) 
            FROM external_citations 
            WHERE publication_id = OLD.publication_id AND is_active = TRUE
        )
        WHERE id = OLD.publication_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update external citation count
DROP TRIGGER IF EXISTS trigger_update_external_citation_count ON external_citations;
CREATE TRIGGER trigger_update_external_citation_count
    AFTER INSERT OR UPDATE OR DELETE ON external_citations
    FOR EACH ROW EXECUTE FUNCTION update_external_citation_count();
