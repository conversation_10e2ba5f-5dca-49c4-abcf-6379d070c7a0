#!/bin/bash

# Development startup script for Enhanced Citation Crawler
echo "🚀 Starting Enhanced Citation Crawler Development Environment"
echo "============================================================"

# Check if Node.js dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
fi

# Check if Python dependencies are installed
if [ ! -d "../citation_crawler_env" ]; then
    echo "🐍 Python virtual environment not found. Please run the main setup script first:"
    echo "   cd .. && ./setup_enhanced_crawler.sh"
    exit 1
fi

# Start the development environment
echo "🔄 Starting development servers..."
echo ""
echo "This will start:"
echo "  - Node.js API server on http://localhost:3001"
echo "  - React development server on http://localhost:3000"
echo ""
echo "Press Ctrl+C to stop all servers"
echo ""

# Run both servers concurrently
npm run dev
