const express = require('express');
const { WebCitationCrawler } = require('./simple_web_crawler');
const { createClient } = require('@supabase/supabase-js');

const app = express();
const PORT = 3001;

// Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://rbvgtaqimzpsarvoxubn.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJidmd0YXFpbXpwc2Fydm94dWJuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjAxMDI0MSwiZXhwIjoyMDYxNTg2MjQxfQ.Y1Xcvq50peqVvgRhVVtHP7eEvpGBn-A2EG65H--8XX8';
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

app.use(express.json());

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:3000');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Server is running' });
});

// Internal crawl endpoint
app.post('/api/crawl-citations', (req, res) => {
  console.log('Internal crawl requested');
  res.json({ 
    message: 'Internal citation crawling completed successfully!',
    type: 'internal',
    timestamp: new Date().toISOString()
  });
});

// Web crawl endpoint - REAL IMPLEMENTATION
app.post('/api/crawl-web-citations', async (req, res) => {
  try {
    const { publicationId } = req.body;
    console.log('🚀 Starting comprehensive web crawl...');

    const crawler = new WebCitationCrawler();

    if (publicationId) {
      // Crawl specific publication
      const { data: publication, error } = await supabase
        .from('publications')
        .select('*')
        .eq('id', publicationId)
        .single();

      if (error || !publication) {
        return res.status(404).json({
          error: 'Publication not found',
          publicationId
        });
      }

      console.log(`🎯 Crawling specific publication: ${publication.title}`);
      console.log(`⏰ Estimated time: 1-2 minutes`);

      const startTime = Date.now();
      const result = await crawler.crawlPublication(publication);
      const endTime = Date.now();
      const processingTime = Math.round((endTime - startTime) / 1000);

      res.json({
        message: `Web crawl completed for "${publication.title}" in ${processingTime}s`,
        type: 'web_external',
        publicationId,
        citationsFound: result.citationsFound,
        citationsSaved: result.citationsSaved,
        queriesProcessed: result.queriesProcessed,
        processingTimeSeconds: processingTime,
        timestamp: new Date().toISOString()
      });

    } else {
      // Crawl all publications
      console.log('🌐 Starting comprehensive crawl for all publications...');
      console.log('⏰ This will take 3-10 minutes - please be patient');

      const results = await crawler.crawlAllPublications();

      res.json({
        message: `Web crawl completed for all publications in ${Math.floor(results.processingTimeSeconds / 60)}m ${results.processingTimeSeconds % 60}s`,
        type: 'web_external',
        publicationsProcessed: results.publicationsProcessed,
        totalCitationsFound: results.totalCitationsFound,
        totalSaved: results.totalSaved,
        processingTimeSeconds: results.processingTimeSeconds,
        averageCitationsPerPublication: (results.totalCitationsFound / results.publicationsProcessed).toFixed(1),
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('❌ Web crawl error:', error);
    res.status(500).json({
      error: 'Web crawl failed',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get citations - REAL IMPLEMENTATION
app.get('/api/publications/:publicationId/citations', async (req, res) => {
  try {
    const { publicationId } = req.params;
    console.log('📋 Getting citations for:', publicationId);

    const { data: citations, error } = await supabase
      .from('external_citations')
      .select('*')
      .eq('publication_id', publicationId)
      .eq('is_active', true)
      .order('confidence_score', { ascending: false });

    if (error) {
      throw error;
    }

    res.json({
      publicationId,
      citations: citations || [],
      count: citations ? citations.length : 0,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error fetching citations:', error);
    res.status(500).json({
      error: 'Failed to fetch citations',
      details: error.message
    });
  }
});

// Crawl stats
app.get('/api/crawl-stats', (req, res) => {
  console.log('Crawl stats requested');
  res.json({
    stats: [],
    count: 0,
    timestamp: new Date().toISOString()
  });
});

// Verify citation
app.patch('/api/citations/:citationId/verify', (req, res) => {
  console.log('Citation verification requested');
  res.json({
    message: 'Citation verified successfully',
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Simple API Server running on http://localhost:${PORT}`);
  console.log(`📊 Test: http://localhost:${PORT}/api/health`);
});
