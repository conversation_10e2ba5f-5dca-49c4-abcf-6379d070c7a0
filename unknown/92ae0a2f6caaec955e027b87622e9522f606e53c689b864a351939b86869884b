-- Quick setup for external citations
-- Run this in Supabase SQL Editor

-- Create external_citations table
CREATE TABLE IF NOT EXISTS external_citations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    publication_id UUID NOT NULL,
    source_url TEXT NOT NULL,
    source_title TEXT,
    source_domain TEXT,
    citation_context TEXT,
    citation_type VARCHAR(50) DEFAULT 'mention',
    confidence_score DECIMAL(3,2) DEFAULT 0.5,
    found_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add external_citation_count column to publications
ALTER TABLE publications ADD COLUMN IF NOT EXISTS external_citation_count INTEGER DEFAULT 0;

-- Create simple function to update citation count
CREATE OR REPLACE FUNCTION update_citation_count(pub_id UUID)
RETURNS INTEGER AS $$
DECLARE
    citation_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO citation_count
    FROM external_citations 
    WHERE publication_id = pub_id AND is_active = TRUE;
    
    UPDATE publications 
    SET external_citation_count = citation_count
    WHERE id = pub_id;
    
    RETURN citation_count;
END;
$$ LANGUAGE plpgsql;
