-- Test script for blog_comments table
-- Run this to verify everything is working

-- 1. Check if table exists and show structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'blog_comments'
ORDER BY ordinal_position;

-- 2. Check if there are any existing comments
SELECT COUNT(*) as total_comments FROM blog_comments;

-- 3. Check RLS policies
SELECT 
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'blog_comments';

-- 4. Test insert (replace with actual values)
-- This will only work if you're authenticated and have proper permissions
-- INSERT INTO blog_comments (post_id, user_id, content) 
-- VALUES ('your-post-id', 'your-user-id', 'Test comment');

-- 5. Check if foreign key constraints exist
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name = 'blog_comments';

-- 6. Check indexes
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'blog_comments';

-- 7. Test RLS is enabled
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE tablename = 'blog_comments'; 