// Simple Web Crawler using only built-in Node.js modules
const https = require('https');
const { URL } = require('url');
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://rbvgtaqimzpsarvoxubn.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJidmd0YXFpbXpwc2Fydm94dWJuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjAxMDI0MSwiZXhwIjoyMDYxNTg2MjQxfQ.Y1Xcvq50peqVvgRhVVtHP7eEvpGBn-A2EG65H--8XX8';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

class SimpleWebCrawler {
  constructor() {
    this.delay = 2000;
    this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
  }

  async makeRequest(url) {
    return new Promise((resolve, reject) => {
      const options = {
        headers: {
          'User-Agent': this.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        },
        timeout: 10000
      };

      https.get(url, options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          resolve(data);
        });
      }).on('error', (err) => {
        reject(err);
      });
    });
  }

  parseSearchResults(html) {
    const results = [];
    
    // Simple regex-based parsing for DuckDuckGo results
    const resultRegex = /<div class="result"[\s\S]*?<\/div>/g;
    const titleRegex = /<a[^>]*class="result__a"[^>]*href="([^"]*)"[^>]*>([^<]*)<\/a>/;
    const snippetRegex = /<a[^>]*class="result__snippet"[^>]*>([^<]*)<\/a>/;
    
    let match;
    while ((match = resultRegex.exec(html)) !== null && results.length < 5) {
      const resultHtml = match[0];
      
      const titleMatch = titleRegex.exec(resultHtml);
      const snippetMatch = snippetRegex.exec(resultHtml);
      
      if (titleMatch && snippetMatch) {
        results.push({
          url: titleMatch[1],
          title: titleMatch[2].trim(),
          snippet: snippetMatch[1].trim(),
          domain: this.extractDomain(titleMatch[1])
        });
      }
    }
    
    return results;
  }

  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch {
      return 'unknown';
    }
  }

  async searchDuckDuckGo(query, maxResults = 5) {
    try {
      console.log(`🔍 Searching for: ${query}`);
      
      const searchUrl = `https://html.duckduckgo.com/html/?q=${encodeURIComponent(query)}`;
      const html = await this.makeRequest(searchUrl);
      const results = this.parseSearchResults(html);
      
      console.log(`✅ Found ${results.length} search results`);
      return results.slice(0, maxResults);
      
    } catch (error) {
      console.error(`❌ Search error: ${error.message}`);
      return [];
    }
  }

  analyzeCitation(result, publicationTitle, authors = '') {
    const title = result.title.toLowerCase();
    const snippet = result.snippet.toLowerCase();
    const url = result.url.toLowerCase();
    
    const pubTitleLower = publicationTitle.toLowerCase();
    const authorsLower = authors.toLowerCase();
    
    let confidenceScore = 0.0;
    let citationType = 'mention';

    // Check for title matches
    if (snippet.includes(pubTitleLower) || title.includes(pubTitleLower)) {
      confidenceScore += 0.4;
    }

    // Check for author matches
    if (authorsLower) {
      const authorList = authorsLower.split(',').map(a => a.trim());
      const authorMatches = authorList.some(author => 
        snippet.includes(author) || title.includes(author)
      );
      if (authorMatches) {
        confidenceScore += 0.3;
      }
    }

    // Check for academic indicators
    const academicIndicators = ['citation', 'reference', 'scholar', 'research', 'study'];
    if (academicIndicators.some(indicator => snippet.includes(indicator) || title.includes(indicator))) {
      confidenceScore += 0.2;
      citationType = 'reference';
    }

    // Check for academic domains
    const academicDomains = ['.edu', 'scholar.google', 'researchgate', 'academia.edu'];
    if (academicDomains.some(domain => url.includes(domain))) {
      confidenceScore += 0.3;
    }

    return {
      url: result.url,
      title: result.title,
      snippet: result.snippet,
      domain: result.domain,
      confidenceScore: Math.min(confidenceScore, 1.0),
      citationType,
      foundDate: new Date().toISOString()
    };
  }

  async saveCitationToDatabase(publicationId, citation) {
    try {
      // Check if citation already exists
      const { data: existing } = await supabase
        .from('external_citations')
        .select('id')
        .eq('publication_id', publicationId)
        .eq('source_url', citation.url)
        .single();

      if (existing) {
        console.log(`📝 Citation already exists: ${citation.url}`);
        return false;
      }

      // Insert new citation
      const { data, error } = await supabase
        .from('external_citations')
        .insert({
          publication_id: publicationId,
          source_url: citation.url,
          source_title: citation.title,
          source_domain: citation.domain,
          citation_context: citation.snippet,
          citation_type: citation.citationType,
          confidence_score: citation.confidenceScore,
          found_date: citation.foundDate,
          is_verified: citation.confidenceScore > 0.7,
          is_active: true
        });

      if (error) {
        console.error(`❌ Database error: ${error.message}`);
        return false;
      }

      console.log(`✅ Saved citation: ${citation.title}`);
      return true;

    } catch (error) {
      console.error(`❌ Error saving citation: ${error.message}`);
      return false;
    }
  }

  async updatePublicationCitationCount(publicationId) {
    try {
      const { data: citations, error } = await supabase
        .from('external_citations')
        .select('id')
        .eq('publication_id', publicationId)
        .eq('is_active', true);

      if (error) {
        console.error(`❌ Error counting citations: ${error.message}`);
        return false;
      }

      const externalCount = citations.length;

      const { error: updateError } = await supabase
        .from('publications')
        .update({
          external_citation_count: externalCount
        })
        .eq('id', publicationId);

      if (updateError) {
        console.error(`❌ Error updating publication: ${updateError.message}`);
        return false;
      }

      console.log(`✅ Updated publication with ${externalCount} external citations`);
      return true;

    } catch (error) {
      console.error(`❌ Error updating citation count: ${error.message}`);
      return false;
    }
  }

  async crawlPublication(publication) {
    try {
      console.log(`\n🚀 Starting crawl for: ${publication.title}`);
      
      const queries = [
        `"${publication.title}"`,
        `"${publication.title}" citation`
      ];

      let totalCitationsFound = 0;
      let totalSaved = 0;

      for (const query of queries) {
        console.log(`\n🔍 Query: ${query}`);
        
        const searchResults = await this.searchDuckDuckGo(query, 3);
        
        for (const result of searchResults) {
          const citation = this.analyzeCitation(result, publication.title, publication.authors);
          
          if (citation.confidenceScore > 0.3) {
            totalCitationsFound++;
            console.log(`📋 Found citation (confidence: ${citation.confidenceScore.toFixed(2)}): ${citation.title}`);
            
            const saved = await this.saveCitationToDatabase(publication.id, citation);
            if (saved) {
              totalSaved++;
            }
          }
        }

        // Rate limiting
        await new Promise(resolve => setTimeout(resolve, this.delay));
      }

      await this.updatePublicationCitationCount(publication.id);

      console.log(`\n✅ Crawl completed for "${publication.title}"`);
      console.log(`   Citations found: ${totalCitationsFound}`);
      console.log(`   New citations saved: ${totalSaved}`);

      return {
        publicationId: publication.id,
        citationsFound: totalCitationsFound,
        citationsSaved: totalSaved
      };

    } catch (error) {
      console.error(`❌ Error crawling publication: ${error.message}`);
      return {
        publicationId: publication.id,
        citationsFound: 0,
        citationsSaved: 0,
        error: error.message
      };
    }
  }

  async crawlAllPublications() {
    try {
      console.log('🚀 Starting web crawl for all publications...');
      
      const { data: publications, error } = await supabase
        .from('publications')
        .select('id, title, authors, doi, journal, year')
        .limit(3);

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      console.log(`📚 Found ${publications.length} publications to crawl`);

      const results = [];
      let totalCitationsFound = 0;
      let totalSaved = 0;

      for (const publication of publications) {
        const result = await this.crawlPublication(publication);
        results.push(result);
        totalCitationsFound += result.citationsFound;
        totalSaved += result.citationsSaved;

        await new Promise(resolve => setTimeout(resolve, this.delay));
      }

      console.log(`\n🎉 Web crawl completed!`);
      console.log(`   Publications processed: ${publications.length}`);
      console.log(`   Total citations found: ${totalCitationsFound}`);
      console.log(`   Total new citations saved: ${totalSaved}`);

      return {
        publicationsProcessed: publications.length,
        totalCitationsFound,
        totalSaved,
        results
      };

    } catch (error) {
      console.error(`❌ Error in crawl all publications: ${error.message}`);
      throw error;
    }
  }
}

module.exports = { WebCitationCrawler: SimpleWebCrawler };
