-- Manual step-by-step setup for blog_comments table
-- Run each section separately if you encounter issues

-- STEP 1: Create the table (if it doesn't exist)
CREATE TABLE IF NOT EXISTS blog_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL,
    user_id UUID NOT NULL,
    parent_id UUID,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- STEP 2: Add parent_id column if missing
-- Run this separately if you get column errors
ALTER TABLE blog_comments ADD COLUMN IF NOT EXISTS parent_id UUID;

-- STEP 3: Add foreign key constraints one by one
-- Run these separately to identify which one fails

-- Add post_id foreign key
ALTER TABLE blog_comments 
ADD CONSTRAINT IF NOT EXISTS blog_comments_post_id_fkey 
FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE;

-- Add user_id foreign key  
ALTER TABLE blog_comments 
ADD CONSTRAINT IF NOT EXISTS blog_comments_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Add parent_id foreign key (self-referencing)
ALTER TABLE blog_comments 
ADD CONSTRAINT IF NOT EXISTS blog_comments_parent_id_fkey 
FOREIGN KEY (parent_id) REFERENCES blog_comments(id) ON DELETE CASCADE;

-- STEP 4: Create indexes
CREATE INDEX IF NOT EXISTS idx_blog_comments_post_id ON blog_comments(post_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_user_id ON blog_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_parent_id ON blog_comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_created_at ON blog_comments(created_at);

-- STEP 5: Enable RLS
ALTER TABLE blog_comments ENABLE ROW LEVEL SECURITY;

-- STEP 6: Create RLS policies
-- Drop existing policies first
DROP POLICY IF EXISTS "Allow users to read comments" ON blog_comments;
DROP POLICY IF EXISTS "Allow users to insert comments" ON blog_comments;
DROP POLICY IF EXISTS "Allow users to update their own comments" ON blog_comments;
DROP POLICY IF EXISTS "Allow users to delete their own comments" ON blog_comments;
DROP POLICY IF EXISTS "Allow admins to delete any comment" ON blog_comments;

-- Create new policies
CREATE POLICY "Allow users to read comments" ON blog_comments
    FOR SELECT USING (true);

CREATE POLICY "Allow users to insert comments" ON blog_comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users to update their own comments" ON blog_comments
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Allow users to delete their own comments" ON blog_comments
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Allow admins to delete any comment" ON blog_comments
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

-- STEP 7: Create trigger function and trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_blog_comments_updated_at ON blog_comments;

CREATE TRIGGER update_blog_comments_updated_at 
    BEFORE UPDATE ON blog_comments 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column(); 