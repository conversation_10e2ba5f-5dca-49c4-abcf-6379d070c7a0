# Client-Side Routing Setup Guide

This guide explains how to handle client-side routing so that page refreshes work correctly on all routes.

## Problem
When you refresh a page on a route like `/jbs`, the server looks for a file at that path instead of serving the React app. This causes a 404 error.

## Solution
We've created configuration files for different server types to redirect all requests to `index.html`, allowing React Router to handle the routing.

## Configuration Files Created

### 1. `public/_redirects` (for Netlify)
```
/*    /index.html   200
```

### 2. `public/web.config` (for IIS/Windows)
Contains rewrite rules to redirect all requests to index.html

### 3. `public/.htaccess` (for Apache)
Contains mod_rewrite rules to redirect all requests to index.html

### 4. `server.js` (Express.js server)
A simple Express server that handles routing for development and production

## Deployment Options

### Option 1: Express.js Server (Recommended)
1. Install dependencies: `npm install`
2. Build the app: `npm run build`
3. Start the server: `npm run serve`
4. The app will be available at `http://localhost:3000`

### Option 2: Netlify
1. Deploy to Netlify
2. The `_redirects` file will automatically handle routing
3. No additional configuration needed

### Option 3: Apache Server
1. Upload the `build` folder to your Apache server
2. Ensure `.htaccess` is in the root directory
3. Make sure mod_rewrite is enabled

### Option 4: IIS Server
1. Upload the `build` folder to your IIS server
2. Ensure `web.config` is in the root directory
3. Configure IIS to handle the rewrite rules

### Option 5: Nginx
Add this to your nginx configuration:
```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

## Testing
After deployment, test these URLs to ensure they work on refresh:
- `/jbs`
- `/jns` 
- `/jis`
- `/contact`
- `/submit`
- `/volumes-issues`
- `/login`
- `/register`
- `/profile`
- `/blog`
- `/events`
- `/resources`
- `/community`
- `/careers`
- `/help`

## Troubleshooting

### If pages still show 404 on refresh:
1. Check that your server configuration is correct
2. Ensure the configuration files are in the right location
3. Restart your web server after making changes
4. Clear browser cache and try again

### For development:
- Use `npm start` for development (runs on port 3000)
- Use `npm run serve` for production testing (runs on port 3000)

### For production:
- Build with `npm run build`
- Deploy the `build` folder with the appropriate server configuration 