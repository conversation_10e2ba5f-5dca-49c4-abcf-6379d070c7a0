import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from './supabaseClient';
import { Helmet } from 'react-helmet';
import { FaBookOpen, FaUsers, FaLightbulb, FaGlobe, FaFlask, FaHandsHelping } from 'react-icons/fa';

function Home() {
  const [publications, setPublications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [featuredArticles, setFeaturedArticles] = useState([]);
  const [stats, setStats] = useState({
    publications: 0,
    authors: 0,
    journals: 0,
    countries: 0 // Placeholder, update if country data becomes available
  });
  const [announcement, setAnnouncement] = useState(null);
  const [showAnnouncement, setShowAnnouncement] = useState(true);

  useEffect(() => {
    async function fetchPublications() {
      setLoading(true);
      const { data, error } = await supabase
        .from('publications')
        .select('id, title, authors, journal, year')
        .order('created_at', { ascending: false })
        .limit(5);
      if (!error) setPublications(data);
      setLoading(false);
    }
    async function fetchFeaturedArticles() {
      const { data, error } = await supabase
        .from('publications')
        .select('id, title, authors, journal, year, abstract')
        .eq('featured', true)
        .order('created_at', { ascending: false })
        .limit(3);
      if (!error) setFeaturedArticles(data);
    }
    async function fetchStats() {
      // Fetch publications stats
      const { data: pubData, error: pubError } = await supabase
        .from('publications')
        .select('id, authors, journal');
      // Fetch unique countries from users
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('country');
      let countries = 0;
      if (!userError && userData) {
        const countrySet = new Set();
        userData.forEach(u => {
          if (u.country && u.country.trim()) countrySet.add(u.country.trim());
        });
        countries = countrySet.size;
      }
      if (!pubError && pubData) {
        // Publications count
        const publications = pubData.length;
        // Unique authors (split by comma, trim, flatten, dedupe)
        const authorsSet = new Set();
        pubData.forEach(pub => {
          if (pub.authors) {
            pub.authors.split(',').map(a => a.trim()).forEach(a => a && authorsSet.add(a));
          }
        });
        // Unique journals
        const journalsSet = new Set();
        pubData.forEach(pub => {
          if (pub.journal) journalsSet.add(pub.journal);
        });
        setStats({
          publications,
          authors: authorsSet.size,
          journals: journalsSet.size,
          countries
        });
      }
    }
    async function fetchAnnouncement() {
      const { data } = await supabase
        .from('announcements')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(1);
      if (data && data.length > 0) setAnnouncement(data[0]);
    }
    fetchPublications();
    fetchFeaturedArticles();
    fetchStats();
    fetchAnnouncement();
    // Only show modal if not already shown in this session
    if (!sessionStorage.getItem('announcement_shown')) {
      setShowAnnouncement(true);
      sessionStorage.setItem('announcement_shown', '1');
    } else {
      setShowAnnouncement(false);
    }
  }, []);

  return (
    <div className="home-page bg-light min-vh-100">
      <Helmet>
        <title>darsgah-e-ahlebait</title>
        <meta name="description" content="A platform for scholarly excellence in science and Islamic research." />
      </Helmet>
      {/* Announcement Modal */}
      {announcement && showAnnouncement && (
        <div className="modal fade show" style={{display:'block', background:'rgba(0,0,0,0.05)'}} tabIndex="-1" role="dialog">
          <div className="modal-dialog modal-dialog-centered" role="document">
            <div className="modal-content rounded-4">
              <div className="modal-header border-0">
                <h5 className="modal-title fw-bold text-primary">Announcement</h5>
                <button type="button" className="btn-close" onClick={() => { setShowAnnouncement(false); sessionStorage.setItem('announcement_shown', '1'); }}></button>
              </div>
              <div className="modal-body">
                <h4 className="fw-bold mb-2">{announcement.title}</h4>
                <div className="mb-2 text-secondary">{announcement.content}</div>
              </div>
              <div className="modal-footer border-0">
                <button type="button" className="btn btn-secondary rounded-pill px-4" onClick={() => { setShowAnnouncement(false); sessionStorage.setItem('announcement_shown', '1'); }}>Close</button>
              </div>
            </div>
          </div>
          <div className="modal-backdrop fade show" onClick={() => { setShowAnnouncement(false); sessionStorage.setItem('announcement_shown', '1'); }} style={{background:'rgba(0,0,0,0.05)'}}></div>
        </div>
      )}
      {/* Welcome Section (Hero) */}
      <section className="home-hero-section position-relative mb-5" style={{width: '100vw', position: 'relative', left: '50%', right: '50%', marginLeft: '-50vw', marginRight: '-50vw', background: 'linear-gradient(120deg, #fffde4 0%, #ffe066 100%)', color: '#3b3b1a', minHeight: '340px', padding: '56px 0'}}>
        <div className="d-flex flex-column flex-md-row align-items-center justify-content-between gap-4 px-4 px-md-5" style={{maxWidth: '1400px', margin: '0 auto'}}>
          <div className="flex-fill">
            <h1 className="display-4 fw-bold mb-2" style={{color: '#3b3b1a'}}>Welcome to <span className="text-warning">darsgah-e-ahlebait</span></h1>
            <p className="lead mb-2" style={{color: '#3b3b1a'}}>A platform for scholarly excellence in science and Islamic research.</p>
            <div className="mb-2" style={{color: '#3b3b1a'}}>
              <b>Explore our journals, discover new research, and join a vibrant academic community bridging science and faith.</b>
            </div>
            <div className="d-flex flex-wrap gap-2 mt-3">
              <Link to="/jbs" className="btn btn-outline-danger rounded-pill px-4">JBS</Link>
              <Link to="/jns" className="btn btn-outline-primary rounded-pill px-4">JNS</Link>
              <Link to="/jis" className="btn btn-outline-success rounded-pill px-4">JIS</Link>
            </div>
          </div>
          <div className="d-none d-md-block flex-shrink-0">
            <FaGlobe size={160} style={{opacity: 0.95, color: '#ffd600'}} />
          </div>
        </div>
      </section>
      {/* About Section */}
      <section className="about-website mb-5">
        <div className="card shadow-sm border-0 p-4 bg-white mb-4 rounded-4">
          <h2 className="mb-3 text-center text-primary fw-bold">About darsgah-e-ahlebait</h2>
          <p className="lead text-center mb-4">
            darsgah-e-ahlebait is a premier online platform dedicated to advancing scholarly research in the fields of biotechnology, natural sciences, and the intersection of Islam and science. Our mission is to foster a vibrant academic community, promote open access to high-quality research, and bridge the gap between science and society.
          </p>
          <div className="row mb-3 g-4">
            <div className="col-md-4">
              <div className="bg-primary bg-opacity-10 rounded-4 p-3 h-100 text-center">
                <FaLightbulb className="mb-2 text-primary" size={32} />
                <h5 className="text-success fw-bold">Our Mission</h5>
                <p className="mb-0">To provide a trusted, accessible, and innovative platform for researchers, scholars, and students to share and discover cutting-edge scientific knowledge and Islamic perspectives.</p>
              </div>
            </div>
            <div className="col-md-4">
              <div className="bg-info bg-opacity-10 rounded-4 p-3 h-100 text-center">
                <FaGlobe className="mb-2 text-info" size={32} />
                <h5 className="text-info fw-bold">Our Vision</h5>
                <p className="mb-0">To become a global leader in academic publishing, recognized for excellence, inclusivity, and the integration of science and faith for the betterment of humanity.</p>
              </div>
            </div>
            <div className="col-md-4">
              <div className="bg-warning bg-opacity-10 rounded-4 p-3 h-100 text-center">
                <FaBookOpen className="mb-2 text-warning" size={32} />
                <h5 className="text-warning fw-bold">What We Offer</h5>
                <ul className="list-unstyled mb-0 text-start mx-auto" style={{maxWidth: '320px'}}>
                  <li><FaFlask className="me-2 text-primary" />Peer-reviewed journals in biotechnology, natural sciences, and Islam & science</li>
                  <li><FaGlobe className="me-2 text-info" />Open access to all publications</li>
                  <li><FaHandsHelping className="me-2 text-success" />Easy manuscript submission and tracking</li>
                  <li><FaUsers className="me-2 text-secondary" />Expert editorial and review boards</li>
                  <li><FaGlobe className="me-2 text-warning" />Global reach and impact</li>
                  <li><FaLightbulb className="me-2 text-danger" />Support for early-career researchers</li>
                </ul>
              </div>
            </div>
          </div>
          <div className="text-center mt-3">
            <b>Join us in our journey to advance knowledge, inspire innovation, and build bridges between science and faith.</b>
          </div>
        </div>
      </section>
      {/* Secondary Navbar */}
      <nav className="navbar navbar-expand-lg navbar-light bg-white shadow-sm rounded mb-4 secondary-navbar" style={{border: '1px solid #e3e6f0'}}>
        <div className="container justify-content-center">
          <ul className="navbar-nav flex-row gap-2 gap-md-4">
            <li className="nav-item"><Link className="nav-link fw-bold text-primary px-3" to="/resources">Resources</Link></li>
            <li className="nav-item"><Link className="nav-link fw-bold text-success px-3" to="/community">Community</Link></li>
            <li className="nav-item"><Link className="nav-link fw-bold text-info px-3" to="/events">Events</Link></li>
            <li className="nav-item"><Link className="nav-link fw-bold text-warning px-3" to="/careers">Careers</Link></li>
            <li className="nav-item"><Link className="nav-link fw-bold text-danger px-3" to="/blog">Blog</Link></li>
            <li className="nav-item"><Link className="nav-link fw-bold text-secondary px-3" to="/help">Help</Link></li>
          </ul>
        </div>
      </nav>
      {/* Journal Cards Section */}
      <section className="mb-5">
        <h2 className="mb-4 text-center fw-bold text-dark">Our Journals</h2>
        <div className="row g-4 justify-content-center">
          <div className="col-md-4 mb-3">
            <div className="card h-100 shadow-lg border-0 rounded-4 journal-card position-relative" style={{background: 'linear-gradient(120deg, #e3f2fd 0%, #fff 100%)', overflow: 'hidden'}}>
              <div className="position-absolute top-0 start-50 translate-middle-x" style={{zIndex: 2, marginTop: '-28px'}}>
                <span className="badge bg-primary text-white px-3 py-2 fs-6 shadow">JBS</span>
              </div>
              <div className="card-body text-center py-5 d-flex flex-column justify-content-center align-items-center">
                <FaFlask size={44} className="mb-3 text-primary" />
                <h5 className="card-title fw-bold mb-2">Journal of Biotechnological Sciences (JBS)</h5>
                <div className="mb-3" style={{minHeight: '28px'}}>
                  ISSN: <b>2308-0043</b>
                </div>
                <div className="d-flex justify-content-center gap-2 mb-2">
                  <Link to="/jbs" className="btn btn-outline-primary rounded-pill px-4">Explore JBS</Link>
                  <Link to="/editorial-board/jbs" className="btn btn-link text-primary">Editorial Board</Link>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4 mb-3">
            <div className="card h-100 shadow-lg border-0 rounded-4 journal-card position-relative" style={{background: 'linear-gradient(120deg, #e8f5e9 0%, #fff 100%)', overflow: 'hidden'}}>
              <div className="position-absolute top-0 start-50 translate-middle-x" style={{zIndex: 2, marginTop: '-28px'}}>
                <span className="badge bg-success text-white px-3 py-2 fs-6 shadow">JNS</span>
              </div>
              <div className="card-body text-center py-5 d-flex flex-column justify-content-center align-items-center">
                <FaBookOpen size={44} className="mb-3 text-success" />
                <h5 className="card-title fw-bold mb-2">Journal of Natural Sciences (JNS)</h5>
                <div className="mb-3" style={{minHeight: '28px'}}>
                  ISSN: <b>2308-5061</b>
                </div>
                <div className="d-flex justify-content-center gap-2 mb-2">
                  <Link to="/jns" className="btn btn-outline-success rounded-pill px-4">Explore JNS</Link>
                  <Link to="/editorial-board/jns" className="btn btn-link text-success">Editorial Board</Link>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4 mb-3">
            <div className="card h-100 shadow-lg border-0 rounded-4 journal-card position-relative" style={{background: 'linear-gradient(120deg, #fffde7 0%, #fff 100%)', overflow: 'hidden'}}>
              <div className="position-absolute top-0 start-50 translate-middle-x" style={{zIndex: 2, marginTop: '-28px'}}>
                <span className="badge bg-warning text-dark px-3 py-2 fs-6 shadow">JIS</span>
              </div>
              <div className="card-body text-center py-5 d-flex flex-column justify-content-center align-items-center">
                <FaGlobe size={44} className="mb-3 text-warning" />
                <h5 className="card-title fw-bold mb-2">Journal of Islam and Science (JIS)</h5>
                <div className="mb-3" style={{minHeight: '28px'}}></div> {/* Empty ISSN row for alignment */}
                <div className="d-flex justify-content-center gap-2 mb-2">
                  <Link to="/jis" className="btn btn-outline-warning rounded-pill px-4">Explore JIS</Link>
                  <Link to="/editorial-board/jis" className="btn btn-link text-warning">Editorial Board</Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Latest Publications Section */}
      <section className="latest-publications mb-5">
        <h2 className="mb-4 text-center fw-bold text-dark">Latest Publications</h2>
        <div className="row justify-content-center g-4">
          {loading ? (
            <div className="text-center my-4">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : publications.length === 0 ? (
            <div className="alert alert-info text-center">No publications found.</div>
          ) : (
            publications.map(pub => (
              <div className="col-12 col-md-6 col-lg-4" key={pub.id}>
                <div className="card h-100 shadow-sm border-0 rounded-4 pub-card position-relative bg-white">
                  <div className="card-body p-4 d-flex flex-column justify-content-between">
                    <div>
                      <h5 className="fw-bold mb-2 text-primary" style={{fontSize: '1.18rem'}}>
                        <Link to={`/publication/${pub.id}`} className="text-decoration-none text-primary">{pub.title}</Link>
                      </h5>
                      <div className="mb-2 text-secondary" style={{fontSize: '0.98rem'}}><FaUsers className="me-2 text-info" />{pub.authors}</div>
                    </div>
                    <div className="d-flex flex-wrap align-items-center gap-2 mt-3">
                      <span className="badge bg-primary bg-opacity-10 text-primary fw-normal"><FaBookOpen className="me-1" />{pub.journal}</span>
                      <span className="badge bg-secondary bg-opacity-10 text-secondary fw-normal"><FaGlobe className="me-1" />{pub.year}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
        <style>{`
          .pub-card {
            transition: box-shadow 0.2s, transform 0.2s;
          }
          .pub-card:hover {
            box-shadow: 0 8px 32px rgba(0,0,0,0.10);
            transform: translateY(-4px) scale(1.03);
          }
        `}</style>
      </section>
      {/* Quick Stats / Highlights */}
      <section className="quick-stats mb-5">
        <div className="container">
          <div className="row text-center g-4">
            <div className="col-6 col-md-3">
              <div className="bg-white rounded-4 shadow-sm p-4 h-100">
                <FaBookOpen className="text-primary mb-2" size={32} />
                <h3 className="fw-bold mb-0">{stats.publications}</h3>
                <div className="text-secondary">Publications</div>
              </div>
            </div>
            <div className="col-6 col-md-3">
              <div className="bg-white rounded-4 shadow-sm p-4 h-100">
                <FaUsers className="text-success mb-2" size={32} />
                <h3 className="fw-bold mb-0">{stats.authors}</h3>
                <div className="text-secondary">Authors</div>
              </div>
            </div>
            <div className="col-6 col-md-3">
              <div className="bg-white rounded-4 shadow-sm p-4 h-100">
                <FaGlobe className="text-warning mb-2" size={32} />
                <h3 className="fw-bold mb-0">{stats.journals}</h3>
                <div className="text-secondary">Journals</div>
              </div>
            </div>
            <div className="col-6 col-md-3">
              <div className="bg-white rounded-4 shadow-sm p-4 h-100">
                <FaLightbulb className="text-danger mb-2" size={32} />
                <h3 className="fw-bold mb-0">{stats.countries || '—'}</h3>
                <div className="text-secondary">Countries</div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Featured Articles / Spotlight */}
      <section className="featured-articles mb-5">
        <div className="container">
          <h2 className="mb-4 text-center fw-bold text-dark">Featured Articles</h2>
          <div className="row g-4 justify-content-center">
            {featuredArticles.length === 0 ? (
              <div className="col-12 text-center text-muted">No featured articles found.</div>
            ) : (
              featuredArticles.map(article => (
                <div className="col-md-4" key={article.id}>
                  <div className="card h-100 shadow-sm border-0 rounded-4">
                    {/* Optionally add an image here if available */}
                    <div className="card-body">
                      <h5 className="card-title fw-bold">{article.title}</h5>
                      <div className="mb-2 text-secondary" style={{fontSize: '0.98rem'}}><FaUsers className="me-2 text-info" />{article.authors}</div>
                      <p className="card-text" style={{minHeight: '60px'}}>{article.abstract ? article.abstract.slice(0, 110) + (article.abstract.length > 110 ? '...' : '') : ''}</p>
                      <div className="d-flex flex-wrap align-items-center gap-2 mt-2">
                        <span className="badge bg-primary bg-opacity-10 text-primary fw-normal"><FaBookOpen className="me-1" />{article.journal}</span>
                        <span className="badge bg-secondary bg-opacity-10 text-secondary fw-normal"><FaGlobe className="me-1" />{article.year}</span>
                      </div>
                      <Link to={`/publication/${article.id}`} className="btn btn-outline-primary rounded-pill px-4 mt-3">Read More</Link>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </section>
      {/* Announcements / News */}
      <section className="announcements mb-5">
        <div className="container">
          <h2 className="mb-4 text-center fw-bold text-dark">Announcements & News</h2>
          <div className="row g-4 justify-content-center">
            <div className="col-md-6">
              <div className="alert alert-info shadow-sm rounded-4">
                <b>Call for Papers:</b> Submit your manuscript for the upcoming issue of JBS. <Link to="/submit">Submit Now</Link>
              </div>
            </div>
            <div className="col-md-6">
              <div className="alert alert-warning shadow-sm rounded-4">
                <b>New Feature:</b> Track your submissions and reviews in your dashboard. <Link to="/dashboard">Go to Dashboard</Link>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Upcoming Events */}
      <section className="upcoming-events mb-5">
        <div className="container">
          <h2 className="mb-4 text-center fw-bold text-dark">Upcoming Events</h2>
          <div className="row g-4 justify-content-center">
            <div className="col-md-4">
              <div className="card h-100 shadow-sm border-0 rounded-4 p-3">
                <h5 className="fw-bold text-primary">International Science Conference</h5>
                <div className="text-secondary mb-2">15 July 2024</div>
                <p>Join leading scientists and scholars for a 3-day event on the latest in biotechnology and natural sciences.</p>
                <a href="#" className="btn btn-outline-primary btn-sm rounded-pill">Learn More</a>
              </div>
            </div>
            <div className="col-md-4">
              <div className="card h-100 shadow-sm border-0 rounded-4 p-3">
                <h5 className="fw-bold text-success">Webinar: Science & Faith</h5>
                <div className="text-secondary mb-2">28 August 2024</div>
                <p>Explore the intersection of scientific discovery and Islamic thought with renowned speakers.</p>
                <a href="#" className="btn btn-outline-success btn-sm rounded-pill">Register</a>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Testimonials / Community Voices */}
      <section className="testimonials mb-5">
        <div className="container">
          <h2 className="mb-4 text-center fw-bold text-dark">Community Voices</h2>
          <div className="row g-4 justify-content-center">
            <div className="col-md-4">
              <div className="bg-white rounded-4 shadow-sm p-4 h-100">
                <blockquote className="blockquote mb-0">
                  <p>"Darsgah-e-Ahlebait is a beacon for interdisciplinary research. The submission process was smooth and the editorial team is top-notch!"</p>
                  <footer className="blockquote-footer mt-2">Prof. Ayesha Siddiqui, India</footer>
                </blockquote>
              </div>
            </div>
            <div className="col-md-4">
              <div className="bg-white rounded-4 shadow-sm p-4 h-100">
                <blockquote className="blockquote mb-0">
                  <p>"A unique platform that truly bridges science and faith. Highly recommended for early-career researchers."</p>
                  <footer className="blockquote-footer mt-2">Dr. John Smith, UK</footer>
                </blockquote>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Newsletter Signup */}
      <section className="newsletter-signup mb-5">
        <div className="container">
          <div className="bg-white rounded-4 shadow-sm p-4 mx-auto" style={{maxWidth: '600px'}}>
            <h4 className="fw-bold mb-2 text-center">Subscribe to Our Newsletter</h4>
            <form className="d-flex flex-column flex-md-row gap-2 justify-content-center align-items-center">
              <input type="email" className="form-control rounded-pill" placeholder="Enter your email" style={{maxWidth: '320px'}} />
              <button type="submit" className="btn btn-warning rounded-pill px-4 fw-bold">Subscribe</button>
            </form>
          </div>
        </div>
      </section>
      {/* Enhanced Footer */}
      <style>{`
        .secondary-navbar .nav-link {
          font-size: 1.13rem;
          border-radius: 0.5rem;
          transition: background 0.18s, color 0.18s;
        }
        .secondary-navbar .nav-link:hover {
          background: #f0f4fa;
          color: #0d6efd !important;
        }
        .journal-card {
          transition: box-shadow 0.2s, transform 0.2s;
          border-radius: 1.5rem !important;
        }
        .journal-card:hover {
          box-shadow: 0 12px 36px rgba(0,0,0,0.13);
          transform: translateY(-6px) scale(1.04);
        }
      `}</style>
    </div>
  );
}

export default Home; 

