// Enhanced Citation System for Journals
// Supports multiple citation sources and real-time updates

import { supabase } from '../supabaseClient';

// Citation sources
export const CITATION_SOURCES = {
  INTERNAL: 'internal',      // Citations from your own publications
  GOOGLE_SCHOLAR: 'google_scholar',
  WEB_OF_SCIENCE: 'web_of_science',
  SCOPUS: 'scopus',
  PUBMED: 'pubmed',
  MANUAL: 'manual'           // Manually added citations
};

// Citation tracking object
export class CitationTracker {
  constructor() {
    this.citations = new Map();
  }

  // Add citation from a specific source
  addCitation(publicationId, source, count = 1, metadata = {}) {
    if (!this.citations.has(publicationId)) {
      this.citations.set(publicationId, {
        total: 0,
        sources: {},
        lastUpdated: new Date()
      });
    }

    const citation = this.citations.get(publicationId);
    citation.sources[source] = (citation.sources[source] || 0) + count;
    citation.total = Object.values(citation.sources).reduce((sum, val) => sum + val, 0);
    citation.lastUpdated = new Date();
    citation.metadata = { ...citation.metadata, ...metadata };

    return citation;
  }

  // Get total citations for a publication
  getTotalCitations(publicationId) {
    const citation = this.citations.get(publicationId);
    return citation ? citation.total : 0;
  }

  // Get citations by source
  getCitationsBySource(publicationId) {
    const citation = this.citations.get(publicationId);
    return citation ? citation.sources : {};
  }

  // Update database with citation counts
  async updateDatabase() {
    const updates = [];
    for (const [publicationId, citation] of this.citations) {
      updates.push({
        id: publicationId,
        citation_count: citation.total,
        citation_sources: citation.sources,
        citation_last_updated: citation.lastUpdated.toISOString()
      });
    }

    // Batch update all publications
    const { data, error } = await supabase
      .from('publications')
      .upsert(updates, { onConflict: 'id' });

    if (error) {
      console.error('Error updating citations:', error);
      return false;
    }

    return true;
  }
}

// Google Scholar citation fetcher (simulated)
export const fetchGoogleScholarCitations = async (title, authors) => {
  // This would normally use Google Scholar API or web scraping
  // For now, we'll simulate the process
  
  try {
    // Simulate API call to Google Scholar
    const searchQuery = `${title} ${authors}`.replace(/\s+/g, '+');
    const response = await fetch(`https://scholar.google.com/scholar?q=${searchQuery}`);
    
    // Parse response to extract citation count
    // This is a simplified example - real implementation would need proper parsing
    
    // For now, return a simulated count
    return Math.floor(Math.random() * 50); // Simulated citation count
  } catch (error) {
    console.error('Error fetching Google Scholar citations:', error);
    return 0;
  }
};

// Manual citation adder
export const addManualCitation = async (publicationId, count, source = CITATION_SOURCES.MANUAL) => {
  try {
    const { data, error } = await supabase
      .from('publications')
      .select('citation_count')
      .eq('id', publicationId)
      .single();

    if (error) throw error;

    const newCount = (data.citation_count || 0) + count;
    
    const { error: updateError } = await supabase
      .from('publications')
      .update({ 
        citation_count: newCount,
        citation_last_updated: new Date().toISOString()
      })
      .eq('id', publicationId);

    if (updateError) throw updateError;

    return newCount;
  } catch (error) {
    console.error('Error adding manual citation:', error);
    return false;
  }
};

// Citation statistics calculator
export const calculateCitationStats = (publications) => {
  const stats = {
    totalCitations: 0,
    averageCitations: 0,
    mostCited: null,
    citationDistribution: {},
    topCited: []
  };

  if (publications.length === 0) return stats;

  // Calculate totals
  publications.forEach(pub => {
    const citations = pub.citation_count || 0;
    stats.totalCitations += citations;
    
    // Track distribution
    const range = Math.floor(citations / 10) * 10;
    stats.citationDistribution[`${range}-${range + 9}`] = 
      (stats.citationDistribution[`${range}-${range + 9}`] || 0) + 1;
  });

  // Calculate averages
  stats.averageCitations = (stats.totalCitations / publications.length).toFixed(2);

  // Find most cited
  const sorted = [...publications].sort((a, b) => 
    (b.citation_count || 0) - (a.citation_count || 0)
  );
  
  stats.mostCited = sorted[0];
  stats.topCited = sorted.slice(0, 10);

  return stats;
};

// Citation impact calculator
export const calculateCitationImpact = (publication) => {
  const citations = publication.citation_count || 0;
  const year = publication.year || new Date().getFullYear();
  const currentYear = new Date().getFullYear();
  const age = currentYear - year;

  if (age === 0) return { impact: 'New', score: 0 };

  const annualCitations = citations / age;
  
  let impact = 'Low';
  let score = annualCitations;

  if (annualCitations >= 10) {
    impact = 'Very High';
    score = 5;
  } else if (annualCitations >= 5) {
    impact = 'High';
    score = 4;
  } else if (annualCitations >= 2) {
    impact = 'Medium';
    score = 3;
  } else if (annualCitations >= 1) {
    impact = 'Low';
    score = 2;
  } else {
    impact = 'Very Low';
    score = 1;
  }

  return { impact, score, annualCitations: annualCitations.toFixed(2) };
};

// Export citation data for external services
export const exportCitationData = (publications) => {
  return publications.map(pub => ({
    id: pub.id,
    title: pub.title,
    authors: pub.authors,
    year: pub.year,
    journal: pub.journal,
    doi: pub.doi,
    citation_count: pub.citation_count || 0,
    url: `${window.location.origin}/publication/${pub.id}`
  }));
}; 