import React, { useEffect, useState } from 'react';
import { supabase } from './supabaseClient';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet';

function Community() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchUsers() {
      setLoading(true);
      const { data, error } = await supabase
        .from('users')
        .select('id, full_name, username, email, profession, country, institution, profile_image_url, profile_visibility, role')
        .eq('profile_visibility', 'public');
      if (!error) setUsers(data || []);
      setLoading(false);
    }
    fetchUsers();
  }, []);

  // Add global CSS for row backgrounds
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      .table-admin-row { background: #ffcccc !important; border-left: 6px solid #dc3545 !important; }
      .table-editor-row { background: #d4f8e8 !important; border-left: 6px solid #198754 !important; }
      .table-reviewer-row { background: #cceeff !important; border-left: 6px solid #0dcaf0 !important; }
      .animated-admin-text {
        background: linear-gradient(90deg, #dc3545, #ff7b7b, #dc3545);
        background-size: 200% 200%;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        color: #b30000 !important;
        animation: animated-admin-gradient 2s linear infinite;
        font-weight: bold;
        letter-spacing: 0.5px;
      }
      @keyframes animated-admin-gradient {
        0% { background-position: 0% 50%; }
        100% { background-position: 100% 50%; }
      }
      .animated-editor-text {
        background: linear-gradient(90deg, #198754, #43e97b, #38f9d7, #198754);
        background-size: 200% 200%;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        color: #0a4d2c !important;
        animation: animated-editor-gradient 2s linear infinite;
        font-weight: bold;
        letter-spacing: 0.5px;
      }
      @keyframes animated-editor-gradient {
        0% { background-position: 0% 50%; }
        100% { background-position: 100% 50%; }
      }
      .animated-reviewer-text {
        background: linear-gradient(90deg, #0dcaf0, #005bea, #00c6fb, #0dcaf0);
        background-size: 200% 200%;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        color: #005bea !important;
        animation: animated-reviewer-gradient 2s linear infinite;
        font-weight: bold;
        letter-spacing: 0.5px;
      }
      @keyframes animated-reviewer-gradient {
        0% { background-position: 0% 50%; }
        100% { background-position: 100% 50%; }
      }
    `;
    document.head.appendChild(style);
    return () => { document.head.removeChild(style); };
  }, []);

  return (
    <div className="community-page">
      <Helmet>
        <title>Community - darsgah-e-ahlebait</title>
        <meta name="description" content="Join the darsgah-e-ahlebait community of researchers and scholars." />
      </Helmet>
      <div className="container py-5">
        <h2 className="mb-4 text-center fw-bold">Community</h2>
        {loading ? (
          <div className="text-center my-5">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : users.length === 0 ? (
          <div className="alert alert-info text-center">No public community members found.</div>
        ) : (
          <div className="table-responsive">
            <table className="table table-bordered align-middle">
              <thead className="table-light">
                <tr>
                  <th>Avatar</th>
                  <th>Name</th>
                  <th>Username</th>
                  <th>Email</th>
                  <th>Profession</th>
                  <th>Country</th>
                  <th>Institution</th>
                </tr>
              </thead>
              <tbody>
                {users.map(user => {
                  let rowClass = '';
                  let textClass = '';
                  let roleBadge = null;
                  if (user.role === 'admin') {
                    rowClass = 'table-admin-row';
                    textClass = 'animated-admin-text';
                    roleBadge = <span className="badge bg-danger ms-2">Admin</span>;
                  } else if (user.role === 'editor') {
                    rowClass = 'table-editor-row';
                    textClass = 'animated-editor-text';
                    roleBadge = <span className="badge bg-success ms-2">Editor</span>;
                  } else if (user.role === 'reviewer') {
                    rowClass = 'table-reviewer-row';
                    textClass = 'animated-reviewer-text';
                    roleBadge = <span className="badge bg-info text-dark ms-2">Reviewer</span>;
                  }
                  return (
                    <tr key={user.id} className={rowClass}>
                      <td style={{position:'relative', width:56, height:56}}>
                        {user.profile_image_url ? (
                          <img src={user.profile_image_url} alt="avatar" style={{width: 40, height: 40, borderRadius: '50%', objectFit: 'cover', border:'3px solid #fff', zIndex:2}} />
                        ) : (
                          <span className="bi bi-person-circle" style={{fontSize: '2rem', color: '#ccc', zIndex:2}}></span>
                        )}
                      </td>
                      <td><span className={textClass}>{user.full_name || '-'} {roleBadge}</span></td>
                      <td><span className={textClass}>{user.username || '-'}</span></td>
                      <td><span className={textClass}>{user.email || '-'}</span></td>
                      <td><span className={textClass}>{user.profession || '-'}</span></td>
                      <td><span className={textClass}>{user.country || '-'}</span></td>
                      <td><span className={textClass}>{user.institution || '-'}</span></td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}

export default Community; 