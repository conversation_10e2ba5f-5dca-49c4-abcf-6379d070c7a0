import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { supabase } from './supabaseClient';

function VolumeIssues() {
  const { volume_number, year, journal_type } = useParams();
  const [issues, setIssues] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchIssues() {
      setLoading(true);
      const { data, error } = await supabase
        .from('volumes')
        .select('issue_number')
        .eq('volume_number', volume_number)
        .eq('year', year)
        .eq('journal_type', journal_type)
        .order('issue_number', { ascending: true });
      if (!error && data) {
        const uniqueIssues = Array.from(new Set(data.map(d => d.issue_number))).sort((a, b) => a - b);
        setIssues(uniqueIssues);
      }
      setLoading(false);
    }
    fetchIssues();
  }, [volume_number, year, journal_type]);

  return (
    <div className="container py-4">
      <h2 className="mb-4 text-center">Issues in Volume {volume_number} ({year}) - {journal_type.toUpperCase()}</h2>
      {loading ? (
        <div className="text-center my-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      ) : (
        <div className="d-flex flex-wrap gap-4 justify-content-center">
          {issues.length === 0 ? (
            <div className="alert alert-info">No issues found for this volume.</div>
          ) : (
            issues.map(issueNum => (
              <Link key={issueNum} to={`/issue/${issueNum}?journal_type=${journal_type}&year=${year}&volume_number=${volume_number}`} className="card shadow-sm p-4 text-decoration-none text-dark" style={{minWidth: 180, textAlign: 'center', border: '2px solid #eee', transition: 'border 0.2s'}}>
                <div style={{fontSize: '1.3rem', fontWeight: 600}}>Issue {issueNum}</div>
              </Link>
            ))
          )}
        </div>
      )}
    </div>
  );
}

export default VolumeIssues; 