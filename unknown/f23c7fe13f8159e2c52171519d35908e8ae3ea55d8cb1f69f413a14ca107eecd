.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Comments Styles */
.comments-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #dee2e6;
}

.comment-item {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.comment-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border-color: #ced4da;
}

.comment-item.border-warning {
  border-color: #ffc107 !important;
  background-color: #fff3cd;
}

.comment-item .btn-link {
  color: #6c757d;
  font-size: 0.875rem;
}

.comment-item .btn-link:hover {
  color: #495057;
  text-decoration: underline !important;
}

.comment-item .btn-link.text-danger:hover {
  color: #dc3545 !important;
}

/* Like and Pin button styles */
.comment-item .btn-sm {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.comment-item .btn-outline-danger:hover {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

.comment-item .btn-outline-warning:hover {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #000;
}

.comment-item .btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
}

.comment-item .btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #000;
}

.comments-list {
  max-height: 600px;
  overflow-y: auto;
}

.comment-item .border-start {
  border-left: 3px solid #dee2e6 !important;
}

.comment-item .border-start:hover {
  border-left-color: #adb5bd !important;
}

/* Comment form styles */
.comments-section form {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
}

.comments-section textarea,
.comments-section input[type="text"] {
  border-radius: 6px;
  border: 1px solid #ced4da;
  transition: border-color 0.2s ease;
  direction: ltr !important;
  text-align: left !important;
  unicode-bidi: bidi-override !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
}

.comments-section textarea:focus,
.comments-section input[type="text"]:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
  direction: ltr !important;
  text-align: left !important;
  unicode-bidi: bidi-override !important;
}

/* Force LTR for all inputs in comments */
.comments-section input,
.comments-section textarea {
  direction: ltr !important;
  text-align: left !important;
  unicode-bidi: bidi-override !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
}

/* Reply styles */
.comment-item .ms-4 {
  margin-left: 2rem !important;
}

.comment-item .ps-3 {
  padding-left: 1rem !important;
}

/* Loading and empty states */
.comments-section .text-center {
  color: #6c757d;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .comment-item .ms-4 {
    margin-left: 1rem !important;
  }
  
  .comment-item .ps-3 {
    padding-left: 0.5rem !important;
  }
  
  .comments-section form {
    padding: 1rem;
  }
}
