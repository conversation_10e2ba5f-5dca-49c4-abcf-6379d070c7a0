#!/usr/bin/env node

/**
 * Diagnostic script to check if everything is set up correctly
 */

console.log('🔍 CITATION CRAWLER DIAGNOSTIC TOOL');
console.log('=====================================\n');

// Check 1: Node.js version
console.log('1. Checking Node.js version...');
console.log(`   ✅ Node.js version: ${process.version}`);

// Check 2: Current directory
console.log('\n2. Checking current directory...');
console.log(`   📁 Current directory: ${process.cwd()}`);

// Check 3: Required files
console.log('\n3. Checking required files...');
const fs = require('fs');
const requiredFiles = [
  'package.json',
  'simple-server.js',
  'web_crawler.js',
  'src/admin/CitationManager.js'
];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file} - Found`);
  } else {
    console.log(`   ❌ ${file} - Missing`);
  }
});

// Check 4: Dependencies
console.log('\n4. Checking dependencies...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = ['express', 'axios', 'cheerio', '@supabase/supabase-js'];
  
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`   ✅ ${dep} - Listed in package.json`);
    } else {
      console.log(`   ❌ ${dep} - Missing from package.json`);
    }
  });
} catch (error) {
  console.log(`   ❌ Error reading package.json: ${error.message}`);
}

// Check 5: Try to load modules
console.log('\n5. Checking if modules can be loaded...');
const modules = [
  { name: 'express', module: 'express' },
  { name: 'axios', module: 'axios' },
  { name: 'cheerio', module: 'cheerio' },
  { name: 'supabase', module: '@supabase/supabase-js' }
];

modules.forEach(({ name, module }) => {
  try {
    require(module);
    console.log(`   ✅ ${name} - Can be loaded`);
  } catch (error) {
    console.log(`   ❌ ${name} - Cannot be loaded: ${error.message}`);
  }
});

// Check 6: Try to load web crawler
console.log('\n6. Checking web crawler...');
try {
  const { WebCitationCrawler } = require('./web_crawler');
  const crawler = new WebCitationCrawler();
  console.log('   ✅ Web crawler - Can be instantiated');
} catch (error) {
  console.log(`   ❌ Web crawler - Error: ${error.message}`);
}

// Check 7: Environment variables
console.log('\n7. Checking environment variables...');
const envVars = ['SUPABASE_URL', 'SUPABASE_SERVICE_KEY'];
envVars.forEach(varName => {
  if (process.env[varName]) {
    console.log(`   ✅ ${varName} - Set`);
  } else {
    console.log(`   ⚠️  ${varName} - Not set (using default)`);
  }
});

// Check 8: Test simple server
console.log('\n8. Testing simple server startup...');
try {
  const express = require('express');
  const app = express();
  
  app.get('/test', (req, res) => {
    res.json({ status: 'ok' });
  });
  
  const server = app.listen(0, () => {
    const port = server.address().port;
    console.log(`   ✅ Express server - Can start on port ${port}`);
    server.close();
  });
} catch (error) {
  console.log(`   ❌ Express server - Error: ${error.message}`);
}

console.log('\n=====================================');
console.log('🎯 DIAGNOSIS COMPLETE');
console.log('=====================================\n');

console.log('📋 NEXT STEPS:');
console.log('1. If any dependencies are missing, run: npm install');
console.log('2. If files are missing, check you\'re in the right directory');
console.log('3. If everything looks good, run: npm run dev');
console.log('4. Then test at: http://localhost:3000\n');

// Check 9: Quick network test
console.log('9. Testing network connectivity...');
const https = require('https');

const testUrl = 'https://html.duckduckgo.com/html/?q=test';
const req = https.get(testUrl, (res) => {
  console.log(`   ✅ Network - Can reach DuckDuckGo (status: ${res.statusCode})`);
}).on('error', (error) => {
  console.log(`   ❌ Network - Cannot reach DuckDuckGo: ${error.message}`);
});

// Timeout the request after 5 seconds
req.setTimeout(5000, () => {
  req.destroy();
  console.log('   ⚠️  Network - Timeout reaching DuckDuckGo (may be blocked)');
});

console.log('\n🔍 Diagnosis complete! Check the results above.');
console.log('If you see any ❌ errors, those need to be fixed first.');
console.log('If everything shows ✅, try running: npm run dev');
