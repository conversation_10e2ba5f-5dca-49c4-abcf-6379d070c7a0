import React, { useEffect, useState } from 'react';
import { supabase } from './supabaseClient';
import { FaUser, FaEnvelope, FaUniversity, FaBuilding, FaPhone, FaGlobe, FaMapMarkerAlt, FaBook, FaLanguage, FaAward, FaIdBadge, FaLock, FaBriefcase, FaLink, FaEdit, FaCamera } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import ProfileSubmissions from './ProfileSubmissions';
import ProfileBlogs from './ProfileBlogs';
import ProfileFavorites from './ProfileFavorites';
import ProfileNotifications from './ProfileNotifications';
import ProfileAuthoredPublications from './ProfileAuthoredPublications';
import { Helmet } from 'react-helmet';

const FIELD_GROUPS = [
  {
    title: 'Personal Information',
    fields: [
      { key: 'full_name', label: 'Full Name', icon: <FaUser /> },
      { key: 'username', label: 'Username', icon: <FaIdBadge /> },
      { key: 'profile_image_url', label: 'Profile Image', icon: <FaUser /> },
      { key: 'bio', label: 'Bio', icon: <FaBook /> },
      { key: 'profile_visibility', label: 'Profile Visibility', icon: <FaLock /> },
    ],
  },
  {
    title: 'Academic & Professional',
    fields: [
      { key: 'profession', label: 'Profession', icon: <FaBriefcase /> },
      { key: 'otherProfession', label: 'Other Profession', icon: <FaBriefcase /> },
      { key: 'institution', label: 'Institution', icon: <FaUniversity /> },
      { key: 'organization', label: 'Organization', icon: <FaBuilding /> },
      { key: 'department', label: 'Department', icon: <FaBuilding /> },
      { key: 'position', label: 'Position', icon: <FaBriefcase /> },
      { key: 'research_interests', label: 'Research Interests', icon: <FaBook /> },
      { key: 'education', label: 'Education', icon: <FaBook /> },
      { key: 'achievements', label: 'Achievements', icon: <FaAward /> },
      { key: 'skills', label: 'Skills', icon: <FaAward /> },
      { key: 'languages', label: 'Languages', icon: <FaLanguage /> },
    ],
  },
  {
    title: 'Contact & Social',
    fields: [
      { key: 'email', label: 'Email', icon: <FaEnvelope /> },
      { key: 'phone', label: 'Phone', icon: <FaPhone /> },
      { key: 'address', label: 'Address', icon: <FaMapMarkerAlt /> },
      { key: 'country', label: 'Country', icon: <FaGlobe /> },
      { key: 'website', label: 'Website', icon: <FaGlobe /> },
      { key: 'social_links', label: 'Social Links', icon: <FaLink /> },
      { key: 'orcid_id', label: 'ORCID ID', icon: <FaIdBadge /> },
      { key: 'google_scholar_id', label: 'Google Scholar ID', icon: <FaIdBadge /> },
    ],
  },
];

export default function Profile({ user }) {
  const [userDetails, setUserDetails] = useState(null);
  const [blogs, setBlogs] = useState([]);
  const [likesMap, setLikesMap] = useState({});
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [editForm, setEditForm] = useState(null);
  const [submissions, setSubmissions] = useState([]);
  const [authoredCount, setAuthoredCount] = useState(0);
  const [saving, setSaving] = useState(false);
  // Add to state for uploading
  const [uploadingImage, setUploadingImage] = useState(false);
  const [activeTab, setActiveTab] = useState('submissions');
  const [editorialSubmission, setEditorialSubmission] = useState(null);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      // Fetch user details
      const { data: userData } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();
      setUserDetails(userData);
      setEditForm(userData);
      // Fetch user's blogs
      const { data: blogData } = await supabase
        .from('blog_posts')
        .select('id, title, views')
        .eq('author_id', user.id);
      setBlogs(blogData || []);
      // Fetch user's publication submissions
      let subData = [];
      if (userData.id) {
        // Try by user_id if available in request table
        const { data: reqById } = await supabase
          .from('request')
          .select('id, title, status, journal, submitted_at')
          .eq('user_id', userData.id);
        if (reqById && reqById.length > 0) {
          subData = reqById;
        }
      }
      if (subData.length === 0) {
        // Fallback: filter by username/email if present
        let orFilters = [];
        if (userData.username) orFilters.push(`username.eq.${userData.username}`);
        if (userData.email) orFilters.push(`email.eq.${userData.email}`);
        if (orFilters.length > 0) {
          const { data: reqByUser } = await supabase
            .from('request')
            .select('id, title, status, journal, submitted_at')
            .or(orFilters.join(','));
          if (reqByUser) subData = reqByUser;
        }
      }
      setSubmissions(subData || []);
      // Fetch likes for user's blogs
      if (blogData && blogData.length > 0) {
        const blogIds = blogData.map(b => b.id);
        const { data: likesData } = await supabase
          .from('blog_likes')
          .select('post_id')
          .in('post_id', blogIds);
        // Count likes per blog
        const map = {};
        blogIds.forEach(id => { map[id] = 0; });
        likesData.forEach(like => { map[like.post_id] = (map[like.post_id] || 0) + 1; });
        setLikesMap(map);
      } else {
        setLikesMap({});
      }
      // Fetch publications where user is an author (robust match)
      let authoredCount = 0;
      if (userData && userData.full_name) {
        const { data: pubs, error } = await supabase
          .from('publications')
          .select('id, authors');
        if (!error && pubs) {
          const normalize = str => str
            .toLowerCase()
            .replace(/[^a-z\s]/g, '')
            .replace(/\s+/g, ' ')
            .trim();
          const target = normalize(userData.full_name);
          authoredCount = pubs.filter(pub => {
            if (!pub.authors) return false;
            const authorsNorm = normalize(pub.authors);
            return authorsNorm.includes(target);
          }).length;
        }
      }
      setAuthoredCount(authoredCount);
      // Fetch editorial board submission by email
      if (userData && userData.email) {
        const { data: edSub } = await supabase
          .from('editorial_board_submissions')
          .select('*')
          .eq('email', userData.email)
          .order('created_at', { ascending: false })
          .limit(1);
        if (edSub && edSub.length > 0) setEditorialSubmission(edSub[0]);
        else setEditorialSubmission(null);
      }
      setLoading(false);
    }
    if (user) fetchData();
  }, [user]);

  if (!user) return <div className="container mt-5"><div className="alert alert-warning">You must be logged in to view your profile.</div></div>;
  if (loading) return <div className="container mt-5 text-center"><div className="spinner-border text-primary" role="status"><span className="visually-hidden">Loading...</span></div></div>;

  // Helper for avatar (initials)
  const getInitials = (name) => {
    if (!name) return '?';
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  // Calculate total likes and status/fee
  const totalLikes = Object.values(likesMap).reduce((a, b) => a + b, 0);
  const likePercent = Math.min(totalLikes * 0.01, 100); // 0.01% per like, max 100%
  const baseFee = 40;
  const likeDiscount = baseFee * (likePercent / 100);
  // Reviewer/editor discount
  const isReviewer = userDetails && userDetails.role === 'reviewer';
  const isEditor = userDetails && userDetails.role === 'editor';
  const isAdmin = userDetails && userDetails.role === 'admin';
  let roleDiscount = 0;
  let roleBadge = null;
  if (isAdmin) {
    roleDiscount = baseFee - likeDiscount; // 100% discount after likes
    roleBadge = <span className="badge bg-danger ms-2">100% Admin Discount Applied</span>;
  } else if (isEditor) {
    roleDiscount = (baseFee - likeDiscount) * 0.3;
    roleBadge = <span className="badge bg-success ms-2">30% Editor Discount Applied</span>;
  } else if (isReviewer) {
    roleDiscount = (baseFee - likeDiscount) * 0.2;
    roleBadge = <span className="badge bg-info ms-2">20% Reviewer Discount Applied</span>;
  }
  const finalFee = Math.max(baseFee - likeDiscount - roleDiscount, 0).toFixed(2);
  // Total percent reduction for status bar
  const totalPercent = ((baseFee - finalFee) / baseFee) * 100;

  // Helper to render grouped fields
  const renderFieldGroup = (group) => (
    <div className="mb-4" key={group.title}>
      <h5 className="fw-bold mb-3 text-primary">{group.title}</h5>
      <div className="row g-3">
        {group.fields.map(({ key, label, icon }) => {
          let value = userDetails[key];
          if (!value || value === '' || value === null) return null;
          // Render arrays nicely
          if (Array.isArray(value)) value = value.join(', ');
          // Render social_links as links if JSON or comma separated
          if (key === 'social_links') {
            try {
              const links = typeof value === 'string' ? value.split(',').map(s => s.trim()) : value;
              return (
                <div className="col-12 col-md-6" key={key}>
                  <div className="border rounded-3 p-2 bg-light mb-2">
                    <span className="me-2">{icon}</span>
                    <span className="fw-bold">{label}:</span> {links.map((l, i) => l ? <a key={i} href={l} target="_blank" rel="noopener noreferrer" className="ms-1">{l}</a> : null)}
                  </div>
                </div>
              );
            } catch {
              return null;
            }
          }
          // Render website as link
          if (key === 'website') {
            return (
              <div className="col-12 col-md-6" key={key}>
                <div className="border rounded-3 p-2 bg-light mb-2">
                  <span className="me-2">{icon}</span>
                  <span className="fw-bold">{label}:</span> <a href={value} target="_blank" rel="noopener noreferrer">{value}</a>
                </div>
              </div>
            );
          }
          return (
            <div className="col-12 col-md-6" key={key}>
              <div className="border rounded-3 p-2 bg-light mb-2">
                <span className="me-2">{icon}</span>
                <span className="fw-bold">{label}:</span> <span className="text-secondary">{value}</span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  // Edit logic
  const handleEditChange = e => {
    const { name, value } = e.target;
    setEditForm(prev => ({ ...prev, [name]: value }));
  };
  const handleEditSave = async () => {
    setSaving(true);
    const { error } = await supabase
      .from('users')
      .update(editForm)
      .eq('id', user.id);
    setSaving(false);
    setEditing(false);
    // Refresh user details
    const { data: userData } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();
    setUserDetails(userData);
    setEditForm(userData);
  };

  // Add image upload handler
  const handleProfileImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    setUploadingImage(true);
    const filePath = `profile-${user.id}-${Date.now()}-${file.name}`;
    let { data, error } = await supabase.storage.from('profile-images').upload(filePath, file, { upsert: true });
    if (error) {
      alert('Profile image upload failed.');
      setUploadingImage(false);
      return;
    }
    const url = supabase.storage.from('profile-images').getPublicUrl(filePath).data.publicUrl;
    setEditForm(prev => ({ ...prev, profile_image_url: url }));
    setUploadingImage(false);
  };

  return (
    <div className="profile-page">
      <Helmet>
        <title>Profile - darsgah-e-ahlebait</title>
        <meta name="description" content="Manage your darsgah-e-ahlebait profile and account settings." />
      </Helmet>
      <div className="container mt-5" style={{maxWidth: 950}}>
        {/* Cover/Banner Image */}
        <div className="position-relative mb-5" style={{height: 180, background: 'linear-gradient(90deg, #0d6efd 60%, #6c63ff 100%)', borderRadius: '1.5rem 1.5rem 0 0'}}>
          {/* Profile Image - large, centered, with edit overlay */}
          <div className="position-absolute top-100 start-50 translate-middle" style={{zIndex: 2}}>
            <div style={{position:'relative'}}>
              {userDetails.profile_image_url || (editForm && editForm.profile_image_url) ? (
                <img src={editing ? (editForm && editForm.profile_image_url) : userDetails.profile_image_url} alt="avatar" className="rounded-circle border border-4 border-white shadow" style={{width: 130, height: 130, objectFit: 'cover', background:'#fff'}} />
              ) : (
                <div className="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center border border-4 border-white shadow" style={{width: 130, height: 130, fontSize: 48, fontWeight: 700, background:'#fff'}}>
                  {getInitials(userDetails.full_name || userDetails.username || userDetails.email)}
                </div>
              )}
              {editing && (
                <label htmlFor="profile-image-upload" style={{position:'absolute',bottom:8,right:8,cursor:'pointer',background:'#fff',borderRadius:'50%',padding:8,border:'1px solid #ccc',boxShadow:'0 2px 8px rgba(0,0,0,0.08)'}} title="Upload profile image">
                  <FaCamera size={22} />
                  <input id="profile-image-upload" type="file" accept="image/*" style={{display:'none'}} onChange={handleProfileImageUpload} disabled={uploadingImage} />
                </label>
              )}
              {uploadingImage && <div className="spinner-border spinner-border-sm text-primary position-absolute" style={{bottom:10,right:10}} role="status"></div>}
            </div>
          </div>
        </div>
        {/* Main Card */}
        <div className="card shadow-lg border-0 rounded-4 pt-5 pb-4 px-4 position-relative" style={{marginTop: '-70px'}}>
          {/* Name, username, role, edit button */}
          <div className="d-flex flex-column flex-md-row align-items-center justify-content-between gap-3 mb-3">
            <div className="text-center text-md-start">
              <h2 className="fw-bold mb-1" style={{fontSize:'2.1rem'}}>{userDetails.full_name || userDetails.username || userDetails.email}
                {userDetails.profile_visibility === 'public' && <span className="badge bg-success ms-2" style={{fontSize:'1rem'}}>Verified</span>}
              </h2>
              <div className="text-secondary mb-1">@{userDetails.username || userDetails.email}</div>
              {userDetails.profession && <span className="badge bg-info bg-opacity-25 text-info me-2">{userDetails.profession}</span>}
              <span className="badge bg-light text-dark border ms-1">{userDetails.role || 'User'}</span>
            </div>
            <button className="btn btn-outline-primary mt-2 mt-md-0" onClick={() => setEditing(!editing)}><FaEdit className="me-2" />{editing ? 'Cancel' : 'Edit Profile'}</button>
          </div>
          {/* Stats Bar */}
          <div className="d-flex flex-wrap justify-content-center gap-4 mb-4 mt-2">
            <div className="text-center">
              <div className="fw-bold" style={{fontSize:'1.3rem'}}>{blogs.length}</div>
              <div className="text-muted" style={{fontSize:'1rem'}}>Blogs</div>
            </div>
            <div className="text-center">
              <div className="fw-bold" style={{fontSize:'1.3rem'}}>{submissions.length}</div>
              <div className="text-muted" style={{fontSize:'1rem'}}>Submissions</div>
            </div>
            <div className="text-center">
              <div className="fw-bold" style={{fontSize:'1.3rem'}}>{authoredCount}</div>
              <div className="text-muted" style={{fontSize:'1rem'}}>Authored Publications</div>
            </div>
            <div className="text-center">
              <div className="fw-bold" style={{fontSize:'1.3rem'}}>{totalLikes}</div>
              <div className="text-muted" style={{fontSize:'1rem'}}>Likes</div>
            </div>
          </div>
          {/* After the stats bar, add the status bar and fee */}
          <div className="d-flex flex-column align-items-center mb-4" style={{maxWidth: 400, margin: '0 auto'}}>
            <div className="w-100 mb-2">
              <div className="d-flex justify-content-between align-items-center mb-1">
                <span className="fw-bold text-secondary">Status Bar</span>
                <span className="fw-bold text-success">{totalPercent.toFixed(2)}%</span>
              </div>
              <div className="progress" style={{height: '22px', background:'#e9ecef'}}>
                <div className="progress-bar bg-success" role="progressbar" style={{width: `${totalPercent}%`}} aria-valuenow={totalPercent} aria-valuemin={0} aria-valuemax={100}></div>
              </div>
            </div>
            <div className="fw-bold mt-2" style={{fontSize:'1.15rem'}}>
              Current Publication Fee: <span className="text-primary">${finalFee}</span>
              {roleBadge}
            </div>
            <div className="text-muted small" style={{fontSize:'0.97rem'}}>
              Base: $40, -0.01% per like. Each like increases your status bar by 0.01% and reduces your publication fee by 0.01%.
              {isReviewer && ' As a reviewer, you get an additional 20% discount.'}
              {isEditor && ' As an editor, you get an additional 30% discount.'}
              {isAdmin && ' As an admin, you get a 100% discount.'}
            </div>
          </div>
          {/* Navigation Buttons as Tabs */}
          <div className="d-flex flex-wrap justify-content-center gap-3 mb-4">
            <button className={`btn px-4 ${activeTab === 'submissions' ? 'btn-primary' : 'btn-outline-primary'}`} onClick={() => setActiveTab('submissions')}>My Submissions</button>
            <button className={`btn px-4 ${activeTab === 'blogs' ? 'btn-secondary' : 'btn-outline-secondary'}`} onClick={() => setActiveTab('blogs')}>My Blogs</button>
            <button className={`btn px-4 ${activeTab === 'favorites' ? 'btn-warning text-white' : 'btn-outline-warning'}`} onClick={() => setActiveTab('favorites')}>Favorites</button>
            <button className={`btn px-4 ${activeTab === 'notifications' ? 'btn-dark text-white' : 'btn-outline-dark'}`} onClick={() => setActiveTab('notifications')}>Notifications</button>
            <button className={`btn px-4 ${activeTab === 'authored' ? 'btn-success text-white' : 'btn-outline-success'}`} onClick={() => setActiveTab('authored')}>My Authored Publications</button>
            <button className={`btn px-4 ${activeTab === 'editorial' ? 'btn-info text-white' : 'btn-outline-info'}`} onClick={() => setActiveTab('editorial')}>Editorial Board Application</button>
          </div>
          {/* Section Content */}
          <div className="mt-4">
            {activeTab === 'submissions' && <ProfileSubmissions user={user} />}
            {activeTab === 'blogs' && <ProfileBlogs user={user} />}
            {activeTab === 'favorites' && <ProfileFavorites />}
            {activeTab === 'notifications' && <ProfileNotifications />}
            {activeTab === 'authored' && <ProfileAuthoredPublications user={user} userDetails={userDetails} />}
            {activeTab === 'editorial' && (
              <div>
                <h4 className="fw-bold mb-3">Editorial Board Application</h4>
                {editorialSubmission ? (
                  <div className="alert alert-info">
                    <b>Status:</b> {editorialSubmission.status ? editorialSubmission.status.charAt(0).toUpperCase() + editorialSubmission.status.slice(1) : 'Pending'}<br/>
                    <b>Submitted on:</b> {editorialSubmission.created_at ? new Date(editorialSubmission.created_at).toLocaleString() : '-'}<br/>
                    <b>Department:</b> {editorialSubmission.department || '-'}<br/>
                    <b>Institution:</b> {editorialSubmission.institution || '-'}<br/>
                    <b>CV:</b> {editorialSubmission.cv_url && <a href={editorialSubmission.cv_url} target="_blank" rel="noopener noreferrer">View CV</a>}
                    {/* Add more fields as needed */}
                  </div>
                ) : (
                  <div className="alert alert-warning">You have not applied to the Editorial Board yet.</div>
                )}
              </div>
            )}
          </div>
          {/* Edit Form (modal style) */}
          {editing && editForm && (
            <div className="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style={{background:'rgba(0,0,0,0.15)',zIndex:9999}}>
              <div className="card shadow-lg border-0 rounded-4 p-4 d-flex flex-column" style={{maxWidth:600, width:'100%', maxHeight:'90vh', overflowY:'auto', paddingBottom: '80px'}}>
                <h4 className="fw-bold mb-3">Edit Profile</h4>
                <form>
                  <div className="row g-3">
                    {FIELD_GROUPS.flatMap(group => group.fields).filter(f => f.key !== 'profile_image_url').map(({ key, label }) => (
                      <div className="col-12 col-md-6" key={key}>
                        <label className="form-label fw-bold">{label}</label>
                        <input
                          type="text"
                          className="form-control"
                          name={key}
                          value={editForm[key] || ''}
                          onChange={handleEditChange}
                        />
                      </div>
                    ))}
                    <div className="col-12 col-md-6">
                      <label className="form-label fw-bold">Profile Image</label>
                      <div className="mb-3">
                        {editForm.profile_image_url && (
                          <img src={editForm.profile_image_url} alt="profile preview" className="rounded-circle border border-2" style={{width:70,height:70,objectFit:'cover'}} />
                        )}
                      </div>
                      <input
                        type="file"
                        className="form-control"
                        name="profile_image_url"
                        accept="image/*"
                        onChange={handleProfileImageUpload}
                        disabled={uploadingImage}
                      />
                      {uploadingImage && <div className="spinner-border spinner-border-sm text-primary mt-2" role="status"></div>}
                    </div>
                  </div>
                  <div className="mt-3 d-flex gap-2 justify-content-end sticky-bottom bg-white pt-3" style={{zIndex:10}}>
                    <button type="button" className="btn btn-success" onClick={handleEditSave} disabled={saving}>{saving ? 'Saving...' : 'Save Changes'}</button>
                    <button type="button" className="btn btn-secondary" onClick={() => setEditing(false)}>Cancel</button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 