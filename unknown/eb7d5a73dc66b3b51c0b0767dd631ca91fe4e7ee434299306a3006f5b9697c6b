# Citation System Guide

## How Citations Work in Your Journals

### 📊 **What is a Citation?**

A citation occurs when one academic paper references another paper in its bibliography or references section. For example:
- **Paper A** mentions **Paper B** in its references
- **Paper B** gets +1 citation count

### 🔍 **Your Current Citation System**

Your system uses **PDF Analysis** to automatically detect citations:

#### **1. PDF Processing Method**
- Reads the last 2 pages of each PDF file
- Searches for "References" or "Bibliography" sections
- Extracts all reference lines
- Matches references to your publications

#### **2. Matching Algorithms**
- **DOI Matching**: Looks for DOI numbers (e.g., `10.1000/123456`)
- **Title Matching**: Fuzzy matching of paper titles
- **Case-insensitive**: Ignores capitalization differences

#### **3. Database Updates**
- Updates `citation_count` field in publications table
- Tracks citations across all your journals (JBS, JNS, JIS)

### 🚀 **Enhanced Citation Features**

#### **Multiple Citation Sources**
- **Internal**: Citations from your own publications
- **Google Scholar**: External citations (simulated)
- **Manual**: Admin-added citations
- **Web of Science**: Future integration
- **Scopus**: Future integration

#### **Citation Impact Calculation**
```
Annual Citations = Total Citations / Paper Age (years)
Impact Level:
- Very High: ≥10 citations/year
- High: 5-9 citations/year  
- Medium: 2-4 citations/year
- Low: 1 citation/year
- Very Low: <1 citation/year
```

### 📈 **Impact Factor Calculation**

#### **Standard Formula**
```
Impact Factor = Citations in Current Year / Articles Published in Previous 2 Years
```

#### **Example**
- **2024 Citations**: 50
- **2022-2023 Articles**: 20
- **Impact Factor**: 50/20 = 2.500

### 🛠️ **Admin Citation Management**

#### **Access Citation Manager**
1. Go to Admin Panel
2. Click "Citation Manager"
3. View all publications with citation counts

#### **Add Manual Citations**
1. Select a publication
2. Enter citation count to add
3. Click "Add Citations"
4. System updates database automatically

#### **Citation Statistics**
- **Total Citations**: Sum of all citations
- **Average Citations**: Mean citations per paper
- **Most Cited**: Publication with highest citations
- **Top Cited**: Top 10 most cited papers

### 📋 **Citation Sources Explained**

#### **1. Internal Citations (Your System)**
- **How**: PDF analysis of your publications
- **Accuracy**: High (direct analysis)
- **Update**: Automatic when PDFs are processed

#### **2. Google Scholar Citations**
- **How**: Web scraping or API calls
- **Accuracy**: High (external verification)
- **Update**: Manual or scheduled

#### **3. Manual Citations**
- **How**: Admin manually adds citations
- **Accuracy**: 100% (admin verified)
- **Update**: On-demand

#### **4. Web of Science/Scopus**
- **How**: Official indexing services
- **Accuracy**: Highest (industry standard)
- **Update**: Quarterly/annual

### 🔧 **Technical Implementation**

#### **Citation Crawler Script**
```javascript
// Runs automatically to analyze PDFs
node citation_crawler.js
```

#### **Database Schema**
```sql
publications table:
- citation_count: INTEGER
- citation_sources: JSONB
- citation_last_updated: TIMESTAMP
```

#### **API Endpoints**
- `GET /api/citations/:publicationId` - Get citations
- `POST /api/citations/:publicationId` - Add citations
- `GET /api/citations/stats` - Get statistics

### 📊 **Citation Display**

#### **Journal Hero Sections**
- Shows real-time calculated Impact Factor
- Updates automatically when citations change
- Displays "Not Available" if insufficient data

#### **Publication Cards**
- Shows citation count badges
- Color-coded impact levels
- Links to detailed citation information

#### **Admin Dashboard**
- Citation statistics overview
- Manual citation management
- Export citation data

### 🎯 **Getting Official Impact Factors**

#### **Requirements for Web of Science**
- ✅ **ISSN Numbers** (JBS: 2308-0043, JNS: 2308-5061)
- ✅ **Consistent Publication Schedule**
- ✅ **Peer Review Process**
- ✅ **International Editorial Board**
- ❌ **JIS needs ISSN** (apply now)

#### **Application Process**
1. **Scopus Application** (Easier)
   - Apply at [Scopus Journal Evaluation](https://www.scopus.com/source/eval.uri)
   - Wait 3-6 months for evaluation

2. **Web of Science Application** (More prestigious)
   - Contact Clarivate Analytics
   - Submit detailed application
   - Pay $2000-5000 application fee
   - Wait 6-12 months for evaluation

### 📈 **Citation Best Practices**

#### **For Authors**
- Include proper references in manuscripts
- Use consistent citation format
- Upload complete PDFs with references

#### **For Admins**
- Regularly run citation crawler
- Verify manual citations
- Monitor citation statistics
- Apply for journal indexing

#### **For Journals**
- Maintain consistent publication schedule
- Ensure proper peer review process
- Build international editorial board
- Standardize citation format

### 🔄 **Automated Citation Updates**

#### **Scheduled Tasks**
- **Daily**: Check for new PDFs
- **Weekly**: Run citation analysis
- **Monthly**: Update impact factors
- **Quarterly**: Export citation data

#### **Manual Updates**
- Admin can add citations anytime
- Real-time impact factor calculation
- Immediate display updates

### 📊 **Citation Analytics**

#### **Journal Performance**
- Track citation trends over time
- Compare journals by impact
- Identify most cited topics

#### **Author Impact**
- Calculate author citation metrics
- Track author collaboration networks
- Identify rising star researchers

#### **Research Impact**
- Measure research influence
- Track interdisciplinary citations
- Monitor field-specific impact

This citation system provides comprehensive tracking and management of academic impact across all your journals! 