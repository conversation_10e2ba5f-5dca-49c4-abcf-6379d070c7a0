import React, { useEffect, useState } from 'react';
import { supabase } from './supabaseClient';
import { Helmet } from 'react-helmet';

function Events() {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchEvents() {
      setLoading(true);
      const { data, error } = await supabase
        .from('events')
        .select('*')
        .order('date', { ascending: true });
      if (!error) setEvents(data);
      setLoading(false);
    }
    fetchEvents();
  }, []);

  return (
    <div className="events-page">
      <Helmet>
        <title>Events - darsgah-e-ahlebait</title>
        <meta name="description" content="Stay updated with upcoming events and conferences from darsgah-e-ahlebait." />
      </Helmet>
      <div className="container py-5">
        <h2 className="mb-4 text-center fw-bold">Upcoming Events</h2>
        <style>{`
          .event-card {
            transition: box-shadow 0.3s, transform 0.3s;
            border-radius: 1.5rem !important;
            background: linear-gradient(120deg, #f8fafc 0%, #e3f0ff 100%);
            box-shadow: 0 2px 12px rgba(0,0,0,0.07);
            overflow: hidden;
          }
          .event-card:hover {
            box-shadow: 0 8px 32px rgba(0,0,0,0.13);
            transform: translateY(-6px) scale(1.04);
            background: linear-gradient(120deg, #e3f0ff 0%, #f8fafc 100%);
          }
          .event-img {
            border-top-left-radius: 1.5rem !important;
            border-top-right-radius: 1.5rem !important;
            object-fit: cover;
            max-height: 220px;
          }
          .event-date-loc {
            font-size: 1.05rem;
            font-weight: 500;
            color: #0d6efd;
            margin-bottom: 0.5rem;
          }
          .event-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1a237e;
          }
          .event-desc {
            color: #444;
            font-size: 1.05rem;
          }
        `}</style>
        {loading ? (
          <div className="text-center my-5">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : events.length === 0 ? (
          <div className="alert alert-info text-center">No events found.</div>
        ) : (
          <div className="row g-4">
            {events.map(event => (
              <div className="col-md-6 col-lg-4" key={event.id}>
                <div className="card event-card h-100 border-0">
                  {event.image_url && (
                    <img src={event.image_url} alt={event.title} className="event-img card-img-top" />
                  )}
                  <div className="card-body d-flex flex-column justify-content-between">
                    <div>
                      <div className="event-title mb-2">{event.title}</div>
                      <div className="event-date-loc">
                        <i className="bi bi-calendar-event me-1"></i>{event.date} &nbsp;|&nbsp; <i className="bi bi-geo-alt me-1"></i>{event.location}
                      </div>
                      <div className="event-desc mb-2">{event.description}</div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default Events; 