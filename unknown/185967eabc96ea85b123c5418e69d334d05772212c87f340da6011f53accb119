-- Add pinned_comment_id column to blog_posts table
ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS pinned_comment_id UUID REFERENCES blog_comments(id) ON DELETE SET NULL;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_pinned_comment_id ON blog_posts(pinned_comment_id);
 
-- Add RLS policy for pinned comment updates (only blog author can pin/unpin)
CREATE POLICY "Allow blog authors to update pinned comment" ON blog_posts
    FOR UPDATE USING (
        auth.uid() = author_id
    ); 