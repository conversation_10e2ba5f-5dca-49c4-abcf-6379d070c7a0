import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaBan, FaExclamationTriangle, FaEnvelope, FaPhone, FaHome } from 'react-icons/fa';

export default function BannedPage({ banReason: propBanReason }) {
  const navigate = useNavigate();
  const [banReason, setBanReason] = useState(propBanReason || 'No reason provided');

  useEffect(() => {
    // If no ban reason provided as prop, try to get it from localStorage
    if (!propBanReason) {
      const bannedUser = localStorage.getItem('bannedUser');
      if (bannedUser) {
        const userData = JSON.parse(bannedUser);
        setBanReason(userData.ban_reason || 'No reason provided');
      }
    }

    // Clean up banned user data when component unmounts
    return () => {
      localStorage.removeItem('bannedUser');
    };
  }, [propBanReason]);

  return (
    <div className="container mt-5">
      <div className="row justify-content-center">
        <div className="col-md-8 col-lg-6">
          <div className="card shadow-lg border-0 rounded-4">
            <div className="card-body text-center p-5">
              {/* Ban Icon */}
              <div className="mb-4">
                <div className="bg-danger rounded-circle d-inline-flex align-items-center justify-content-center" 
                     style={{width: 80, height: 80}}>
                  <FaBan className="text-white" style={{fontSize: '2rem'}} />
                </div>
              </div>

              {/* Main Message */}
              <h2 className="text-danger fw-bold mb-3">
                <FaExclamationTriangle className="me-2" />
                Account Banned
              </h2>
              
              <p className="text-muted mb-4" style={{fontSize: '1.1rem'}}>
                Your account has been suspended from accessing Darsgah-e-Ahlebait.
              </p>

              {/* Ban Reason */}
              <div className="alert alert-warning mb-4">
                <h5 className="alert-heading mb-2">Reason for Ban:</h5>
                <p className="mb-0">{banReason}</p>
              </div>

              {/* Contact Information */}
              <div className="bg-light rounded-3 p-4 mb-4">
                <h5 className="mb-3">Need Help?</h5>
                <p className="text-muted mb-3">
                  If you believe this ban was issued in error or have questions, 
                  please contact our support team:
                </p>
                
                <div className="row text-start">
                  <div className="col-md-6 mb-3">
                    <div className="d-flex align-items-center">
                      <FaEnvelope className="text-primary me-2" />
                      <div>
                        <strong>Email:</strong><br />
                        <a href="mailto:<EMAIL>" className="text-decoration-none">
                          <EMAIL>
                        </a>
                      </div>
                    </div>
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <div className="d-flex align-items-center">
                      <FaPhone className="text-primary me-2" />
                      <div>
                        <strong>Phone:</strong><br />
                        <a href="tel:03444244544" className="text-decoration-none">
                          03444244544
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="d-flex gap-3 justify-content-center">
                <button 
                  className="btn btn-outline-primary" 
                  onClick={() => {
                    localStorage.removeItem('bannedUser');
                    navigate('/');
                  }}
                >
                  <FaHome className="me-2" />
                  Go to Homepage
                </button>
                
                <button 
                  className="btn btn-outline-secondary" 
                  onClick={() => {
                    localStorage.removeItem('bannedUser');
                    navigate('/contact');
                  }}
                >
                  Contact Support
                </button>
              </div>

              {/* Additional Information */}
              <div className="mt-4 pt-3 border-top">
                <small className="text-muted">
                  <strong>Note:</strong> Banned accounts cannot access any features of the platform. 
                  To appeal this decision, please provide detailed information about your situation 
                  when contacting support.
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 