import React, { useState, useEffect } from 'react';
import { supabase } from './supabaseClient';

function DatabaseTest() {
  const [publications, setPublications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);
  const [externalCitations, setExternalCitations] = useState([]);

  useEffect(() => {
    testDatabase();
  }, []);

  const testDatabase = async () => {
    try {
      console.log('🧪 Testing database connection...');
      
      // Test 1: Get all publications
      const { data: allPubs, error } = await supabase
        .from('publications')
        .select('id, title, journal, citation_count, external_citation_count, authors, year');

      // Test 2: Get external citations
      const { data: externalCitations, error: citError } = await supabase
        .from('external_citations')
        .select('id, publication_id, source_url, citation_context, created_at')
        .order('created_at', { ascending: false })
        .limit(10);

      console.log('🔍 External Citations Test:');
      console.log('   Error:', citError);
      console.log('   Total external citations:', externalCitations ? externalCitations.length : 0);
      console.log('   Recent citations:', externalCitations);

      console.log('📊 Database Test Results:');
      console.log('   Error:', error);
      console.log('   Total publications found:', allPubs ? allPubs.length : 0);
      
      if (allPubs && allPubs.length > 0) {
        // Group by journal
        const journalStats = {};
        allPubs.forEach(pub => {
          const journal = pub.journal || 'NULL';
          if (!journalStats[journal]) {
            journalStats[journal] = {
              count: 0,
              internalCitations: 0,
              externalCitations: 0
            };
          }
          journalStats[journal].count++;
          journalStats[journal].internalCitations += (pub.citation_count || 0);
          journalStats[journal].externalCitations += (pub.external_citation_count || 0);
        });

        console.log('📚 Publications by journal:', journalStats);
        setStats(journalStats);
        setPublications(allPubs.slice(0, 10)); // Show first 10
      }

      // Set external citations
      if (externalCitations) {
        setExternalCitations(externalCitations);
      }

      setLoading(false);

    } catch (error) {
      console.error('❌ Database test failed:', error);
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mt-5">
        <div className="text-center">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p>Testing database connection...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mt-5">
      <h2>Database Test Results</h2>
      
      {stats && (
        <div className="row mb-4">
          <div className="col-12">
            <h4>Publications by Journal:</h4>
            <div className="table-responsive">
              <table className="table table-striped">
                <thead>
                  <tr>
                    <th>Journal</th>
                    <th>Publications</th>
                    <th>Internal Citations</th>
                    <th>External Citations</th>
                    <th>Total Citations</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(stats).map(([journal, data]) => (
                    <tr key={journal}>
                      <td><strong>{journal}</strong></td>
                      <td>{data.count}</td>
                      <td>{data.internalCitations}</td>
                      <td>{data.externalCitations}</td>
                      <td>{data.internalCitations + data.externalCitations}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      <h4>Sample Publications:</h4>
      {publications.length === 0 ? (
        <div className="alert alert-warning">
          <h5>No Publications Found!</h5>
          <p>This means your database is empty or there's a connection issue.</p>
          <p>Check the browser console for detailed error messages.</p>
        </div>
      ) : (
        <div className="table-responsive">
          <table className="table table-striped">
            <thead>
              <tr>
                <th>ID</th>
                <th>Title</th>
                <th>Journal</th>
                <th>Internal Citations</th>
                <th>External Citations</th>
                <th>Year</th>
              </tr>
            </thead>
            <tbody>
              {publications.map(pub => (
                <tr key={pub.id}>
                  <td>{pub.id}</td>
                  <td>{pub.title?.substring(0, 50)}...</td>
                  <td><span className="badge bg-primary">{pub.journal || 'NULL'}</span></td>
                  <td>{pub.citation_count || 0}</td>
                  <td>{pub.external_citation_count || 0}</td>
                  <td>{pub.year}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <h4>Recent External Citations:</h4>
      {externalCitations.length === 0 ? (
        <div className="alert alert-warning">
          <h5>No External Citations Found!</h5>
          <p>This means no citations have been saved from web crawling yet.</p>
        </div>
      ) : (
        <div className="table-responsive">
          <table className="table table-striped">
            <thead>
              <tr>
                <th>ID</th>
                <th>Publication ID</th>
                <th>Source URL</th>
                <th>Citation Context</th>
                <th>Created</th>
              </tr>
            </thead>
            <tbody>
              {externalCitations.map(cit => (
                <tr key={cit.id}>
                  <td>{cit.id}</td>
                  <td>{cit.publication_id}</td>
                  <td>{cit.source_url?.substring(0, 30)}...</td>
                  <td>{cit.citation_context?.substring(0, 50)}...</td>
                  <td>{new Date(cit.created_at).toLocaleDateString()}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <div className="mt-4">
        <button className="btn btn-primary" onClick={testDatabase}>
          Refresh Test
        </button>
      </div>
    </div>
  );
}

export default DatabaseTest;
